# tutransparencia-vue

## Consideraciones de Ejecución (develop)

Para la ejecución momentánea de las funcionalidades presentes en la rama `develop`, es necesario incluir el atributo `role` dentro del LocalStorage del navegador, con alguno de los siguientes valores: 

- `AUDITOR`: Para las vistas de auditor.
- `ADMIN`: Para las vistas de administrador.
- `GESTOR`: Para las vistas de gestor.

* Nota: Ésto sólo es una solución provisoria y que se mantendrá hasta que se implemente el login 
---

## Setup de ide recomendado

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (No usar Vetur).

Adicionalmente utilizar extension de ESlint (en periodo de prueba)
## Instalar dependencias

```sh
npm install
```

### Compilar y ejecutar en modo de desarrollo con recarga en caliente

```sh
npm run dev
```

### Compilar y minificar para producción

```sh
npm run build
```

### Correr linter de forma manual [ESLint](https://eslint.org/)

```sh
npm run lint
```
### Linter de css [Stylelint](https://stylelint.io/)

```sh
npm run lint:css
```

### Formatear código con [Prettier](https://prettier.io/) para ayudar con legibilidad

```sh
npm run format
```

### Repositorio utiliza [GitFlow](https://danielkummer.github.io/git-flow-cheatsheet/index.es_ES.html) para manejo de ramas.

### Para contribuir al proyecto, se debe crear una rama con el nombre del objetivo a cumplir, por ejemplo: feature/nombre-de-la-funcionalidad/vista

### La convención de commits es la siguiente: [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/)


