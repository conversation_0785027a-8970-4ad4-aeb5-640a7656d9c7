<template>
  <div class="flex flex-col gap-10">
    <Suspense>
      <template #default>
        <KPICardsRow />
      </template>
      <template #fallback>
        <RowSkeleton :count="5" />
      </template>
    </Suspense>
    <Suspense>
      <template #default>
        <InstitutionsByLevel />
      </template>
      <template #fallback>
        <DistributionChartSkeleton />
      </template>
    </Suspense>
    <TopInstitutionsSection />
    <DimensionsRanking />
  </div>
</template>
<script setup lang="ts">
import RowSkeleton from '@/components/templates/RowSkeleton.vue';
import KPICardsRow from '@/components/atoms/KPICardsRow.vue'
import DimensionsRanking from '@/components/organisms/DimensionsRanking.vue';
import InstitutionsByLevel from '@/components/organisms/InstitutionsByLevelSection.vue'
import TopInstitutionsSection from '@/components/organisms/TopInstitutionsSection.vue';
import DistributionChartSkeleton from '@/components/templates/charts/DistributionChartSkeleton.vue';
</script>
