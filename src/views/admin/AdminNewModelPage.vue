<script setup lang="ts">
import Card from '@/components/atoms/Card.vue';
import Button from '@/components/atoms/Button.vue';
import CreateModelForm from '@/components/molecules/CreateModelForm.vue';
import NewModelDimensionsSection from '@/components/organisms/NewModelDimensionsSection.vue';
import { useRouter } from 'vue-router';
import { reactive, ref, watch } from 'vue';
import type { Dimension } from '@/models/types';
import { useMaturityModels } from '@/hooks/useMaturityModels';
import 'vue-toast-notification/dist/theme-sugar.css';
import { useToast } from 'vue-toast-notification';
import { useAsyncFetch } from '@/hooks/useAsyncFetch';

const $toast = useToast();
const { handleCreateMaturityModel } = useMaturityModels();
const dimensions = ref<Dimension[]>([]);
const modelFormData = reactive({
    name: '',
    evaluationContext: '',
    description: '',
    timeLimit: 92
});

const createModelFormRef = ref<any>(null);
const router = useRouter();
const isFormDataValid = ref(false);
const fileRef = ref<File>();

const onDimensionsUploaded = (file: File, uploadedDimensions: Dimension[]) => {
    dimensions.value = uploadedDimensions;
    fileRef.value = file;
}

const handleValidateData = async () => {
    const isValid = await createModelFormRef.value?.validateForm();
    if (isValid && dimensions.value.length > 0) {
        isFormDataValid.value = true;
    } else {
        isFormDataValid.value = false;
    }
}

watch([modelFormData, dimensions], async () => {
    await handleValidateData();
});


const handleUploadForm = async () => {
    if (isFormDataValid.value && fileRef.value) {
        try {
            await handleCreateMaturityModel(modelFormData, fileRef.value)
            $toast.success('Modelo de madurez creado correctamente');
            router.back();
            router.afterEach(() => {
                window.location.reload();
            });
        } catch (error) {
            $toast.error('Error al crear el modelo de madurez');
        }
    }
}

const { fetchData: handleSubmit, isLoading } = useAsyncFetch(handleUploadForm);

</script>
<template>
    <Card class="flex flex-col gap-8">
        <div class="flex items-center justify-between">
            <h3 class="text-xl font-bold">Información general</h3>
            <div class="flex items-center gap-4">
                <Button @click="() => router.back()" variant="invert">Cancelar</Button>
                <Button :loading="isLoading" @click="handleSubmit" :disabled="!isFormDataValid">Guardar Cambios</Button>
            </div>
        </div>
        <CreateModelForm ref="createModelFormRef" v-model="modelFormData" />
        <NewModelDimensionsSection :onDimensionsUploaded="onDimensionsUploaded" />
    </Card>
</template>