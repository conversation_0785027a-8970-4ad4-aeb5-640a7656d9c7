<script setup lang="ts">
import InstitutionFormModal from '@/components/molecules/InstitutionFormModal.vue';
import AssignAuditorModal from '@/components/molecules/AssignAuditorModal.vue';
import InstitutionCard from '@/components/molecules/InstitutionCard.vue';
import InstitutionTile from '@/components/molecules/InstitutionTile.vue';
import Pagination from '@/components/atoms/Pagination.vue';
import SearchBar from '@/components/molecules/SearchBar.vue';
import { useInstitutions } from '@/hooks/useInstitutions';
import { onMounted, ref } from 'vue';
import type { Institution } from '@/models/types/index';
import ViewOptionsToggle from '@/components/organisms/ViewOptionsToggle.vue';
import IconButton from '@/components/atoms/IconButton.vue';
import Button from '@/components/atoms/Button.vue';

const isModalOpen = ref(false);
const isAssignAuditorModalOpen = ref(false);
const selectedInstitution = ref<Institution>();

const { institutions, page, totalPages, fetchPageData, handleSearchInstitutions, handleNextPage, handlePreviousPage, isLoading, handleGoToPage } = useInstitutions();

onMounted(() => {
    fetchPageData(page.value);
});

const handleAssignAuditor = (institution: Institution) => {
    selectedInstitution.value = institution;
    isAssignAuditorModalOpen.value = true;
}

const handleEditInstitution = (institution: Institution) => {
    selectedInstitution.value = institution;
    isModalOpen.value = true;
}

const handleCloseModal = () => {
    isModalOpen.value = false;
    selectedInstitution.value = undefined;
}
</script>

<template>
    <div class="flex flex-col w-full gap-8">
        <SearchBar :onSearch="handleSearchInstitutions" />
        <ViewOptionsToggle :isLoading="isLoading" :items="institutions" label="instituciones" :isDate="false"
            :headers="['Nombre', 'Procesos', 'Nivel general', 'Funcionarios', 'Auditor', 'Contacto', 'Acción']">
            <template #options>
                <IconButton @click="isModalOpen = true" icon="fa-solid fa-plus" />
            </template>
            <template #grid="{ item }">
                <InstitutionCard :institution="(item as Institution)" class="institution-card">
                    <template #options>
                        <div class="flex items-center gap-4">
                            <RouterLink :to="(item as Institution).nprocesses > 0 ? 
                            { path: `/admin/institutions/${(item as Institution).id}` } : ''"
                            @click="(item as Institution).nprocesses === 0 && $event.preventDefault()">
                                <Button 
                                variant="secondary" 
                                class="px-3 text-sm"
                                :class="{'opacity-50 cursor-not-allowed': (item as Institution).nprocesses === 0}"
                                :disabled="(item as Institution).nprocesses === 0"
                                > 
                                Ver Proceso</Button>
                            </RouterLink>
                            <Button @click="handleEditInstitution(item as Institution)" variant="invert"
                                class="px-3 text-sm border-none">Editar</Button>
                        </div>
                    </template>

                    <template #assign-auditor>
                        <button @click="handleAssignAuditor(item as Institution)"
                            class="px-3 text-xs text-red-500 font-semibold">Asignar auditor</button>
                    </template>
                </InstitutionCard>
            </template>
            <template #list="{ item }">
                <InstitutionTile :institution="item" />
            </template>
        </ViewOptionsToggle>
        <Pagination :itemCount="institutions?.length ?? 0" :totalPages="totalPages" :page="page" @next="handleNextPage"
            @previous="handlePreviousPage" @go-to-page="handleGoToPage" />
    </div>

    <InstitutionFormModal :institution="selectedInstitution" :isModalOpen="isModalOpen" :onClose="handleCloseModal" :enable="true" :enableAuditor="true"/>

    <AssignAuditorModal :isOpen="isAssignAuditorModalOpen" :onClose="() => { isAssignAuditorModalOpen = false }"
        :institution="selectedInstitution" />
</template>

<style scoped>
.institution-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  max-width: 100%; 
  height: auto;
  box-sizing: border-box; 
  background-color: white; 
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); 
}

.institution-card .card-content {
  overflow: hidden; 
  text-overflow: ellipsis; 
  white-space: nowrap; 
}

.institution-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .institution-card {
    flex-direction: column;
    flex-wrap: wrap;
  }

  .institution-card .card-header {
    flex-direction: column; 
    align-items: flex-start;
  }

  .institution-card .card-content {
    white-space: normal; 
  }

  .institution-card .card-header h3,
  .institution-card .card-header p {
    font-size: 14px; 
  }
}

</style>