<script setup lang="ts">
import ViewOptionsToggle from '@/components/organisms/ViewOptionsToggle.vue';
import AuditorCard from '@/components/molecules/AuditorCard.vue';
import SearchBar from '@/components/molecules/SearchBar.vue';
import Pagination from '@/components/atoms/Pagination.vue';
import { useAuditors } from '@/hooks/useAuditors';
import { onMounted, ref } from 'vue';
import type { Auditor } from '@/models/types/index';
import AuditorTile from '@/components/molecules/AuditorTile.vue';
import IconButton from '@/components/atoms/IconButton.vue';
import AuditorFormModal from '@/components/molecules/AuditorFormModal.vue';
import Button from '@/components/atoms/Button.vue';

const { auditors, page, totalPages, fetchPageData, handleSearchAuditors, handleNextPage, handlePreviousPage, isLoading, handleGoToPage } = useAuditors();
const isAuditorModalOpen = ref<boolean>(false);
const selectedAuditor = ref<Auditor>();

const handleEditAuditor = (auditor: Auditor) => {
    selectedAuditor.value = auditor;
    isAuditorModalOpen.value = true;
}

const handleCloseModal = () => {
    isAuditorModalOpen.value = false;
    selectedAuditor.value = undefined;
}

onMounted(() => {
    fetchPageData(page.value);
});
</script>

<template>
    <div class="flex flex-col gap-8 w-full">
        <SearchBar :onSearch="handleSearchAuditors" />
        <ViewOptionsToggle :isLoading="isLoading" :items="auditors" label="auditores" :isDate="false"
            :headers="['Nombre', 'Auditorías', 'Procesos activos', 'Ciudad', 'Contacto', 'Acción']">
            <template #options>
                <IconButton @click="isAuditorModalOpen = true" icon="fa-solid fa-plus" />
            </template>
            <template #grid="{ item }">
                <AuditorCard :auditor="(item as Auditor)" class="auditor-card">
                    <template #options>
                        <div class="flex items-center gap-4">
                            <RouterLink :to="{
                                path: `/admin/auditors/${(item as Auditor).id}/processes`,
                                state: {
                                    title: `${(item as Auditor).username}`
                                }
                            }">
                                <Button variant="secondary" class="px-3 text-sm">Ver procesos</Button>
                            </RouterLink>
                            <Button @click="handleEditAuditor(item as Auditor)" variant="invert"
                                class="px-3 text-sm border-none">Editar</Button>
                        </div>
                    </template>
                </AuditorCard>
            </template>
            <template #list="{ item }">
                <AuditorTile :auditor="item" />
            </template>
        </ViewOptionsToggle>
        <Pagination :itemCount="auditors?.length ?? 0" :page="page" :totalPages="totalPages" @next="handleNextPage"
            @previous="handlePreviousPage" @go-to-page="handleGoToPage" />
    </div>

    <AuditorFormModal v-model="selectedAuditor" :isModalOpen="isAuditorModalOpen" :enable="true" @close="handleCloseModal" />
</template>

<style scoped>
.auditor-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  max-width: 100%;
  height: auto;
  box-sizing: border-box; 
  background-color: white; 
  border-radius: 8px; 
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.auditor-card .card-content {
  overflow: hidden; 
  text-overflow: ellipsis; 
  white-space: nowrap; 
}

.auditor-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap; 
}

@media (max-width: 768px) {
  .auditor-card {
    flex-direction: column; 
    flex-wrap: wrap;
  }

  .auditor-card .card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .auditor-card .card-content {
    white-space: normal;
  }

  .auditor-card .card-header h3,
  .auditor-card .card-header p {
    font-size: 14px; 
  }
}

</style>