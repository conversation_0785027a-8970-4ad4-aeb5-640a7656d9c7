<script setup lang="ts">
import ListView from '@/components/organisms/ListView.vue';
import MaturityModelTile from '@/components/molecules/MaturityModelTile.vue';
import { useMaturityModels } from '@/hooks/useMaturityModels';
import { onMounted, ref } from 'vue';

const { maturityModels, fetchPageData, page, isLoading } = useMaturityModels();
const sortOrder = ref<'asc' | 'desc'>('asc');

/* 
const maturityModels = ref([
    {
        id: 1,
        name: "Modelo A",
        createdAt: "2024-09-29T04:01:37.647+00:00",
        active: true,
    },
    {
        id: 2,
        name: "Modelo B",
        createdAt: "2024-11-15T10:20:45.123+00:00",
        active: true,
    },
    {
        id: 3,
        name: "Modelo C",
        createdAt: "2024-08-05T09:00:12.500+00:00",
        active: true,
    },
]);
**/
const sortByDate = () => {
    const sortedModels = [...maturityModels.value].sort((a, b) => {
        const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0); 
        const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0); 
        return sortOrder.value === 'asc' 
            ? dateA.getTime() - dateB.getTime() 
            : dateB.getTime() - dateA.getTime();
    });
    maturityModels.value = sortedModels;
};

const toggleSortOrder = () => {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
    sortByDate(); 
};

onMounted(() => {
    fetchPageData(page.value);
    sortByDate();
});
</script>


<template>
    <div class="flex flex-col w-full gap-8">
        <div class="flex justify-between items-center w-full">
            <div>
                <p class="font-bold text-sm">Mostrando {{ maturityModels.length }} modelos</p>
                <p class="text-sm">de autodiagnostico</p>
            </div>
            <div class="flex gap-2">
                <router-link to="/admin/models/new">
                    <button class="w-10 h-10 flex items-center justify-center border border-primary-400 rounded-full">
                        <i class="fa-solid fa-plus text-2xl text-primary-900"></i>
                    </button>
                </router-link>

                <!-- Botón para ordenar -->
                <button class="flex gap-3 items-center px-4 py-2 border rounded-full border-primary-300 text-sm" @click="toggleSortOrder">
                    <i class="fa-solid text-base text-primary-800"
                        :class="sortOrder === 'asc' ? 'fa-arrow-down-short-wide' : 'fa-arrow-up-short-wide'"></i>
                    Creado
                    <i class="ml-2 fa-solid text-base text-primary-800 fa-chevron-down"></i>
                </button>
            </div>
        </div>

        <!-- Lista de modelos de madurez -->
        <ListView :isLoading="isLoading"
            :headers="['ID', 'Nombre', 'Fecha de Creación', 'Dimensiones', 'Preguntas', 'Evidencias', 'Estado']"
            :items="maturityModels">
            <template #default="{ item }">
                <MaturityModelTile :maturityModel="item" />
            </template>
        </ListView>
    </div>
</template>
