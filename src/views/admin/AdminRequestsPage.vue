<script setup lang="ts">
import { computed, ref } from 'vue';
import Card from '@/components/atoms/Card.vue';
import ProcessRequestsTab from '@/components/molecules/ProcessRequestsTab.vue';
import InstitutionRegisterTab from '@/components/organisms/InstitutionRegisterTab.vue';
import InstitutionRegisterRequestsTab from '@/components/molecules/InstitutionRegisterRequestsTab.vue';
import TabBar from '@/components/molecules/TabBar.vue';
import { useTabs } from '@/hooks/useTabs';
import type { InstitutionRequest } from '@/models/types/institution';
import type { Process } from '@/models/types/index';
import ProcessApprovalTab from '@/components/organisms/ProcessApprovalTab.vue';

const tabs = computed(() => [
    { name: 'Procesos', component: ProcessRequestsTab },
    { name: 'Registros', component: InstitutionRegisterRequestsTab },
]);

const { activeTabComponent: ActualTabComponent, activeTab, setActiveTab } = useTabs(tabs);

const selectedItem = ref<Process | InstitutionRequest | null>(null);
const typeSelectedItem = ref<string | null>(null);

const onItemSelect = (item: Process | InstitutionRequest, type: 'Process' | 'InstitutionRequest') => {
    selectedItem.value = item;
    typeSelectedItem.value = type;
}
</script>

<template>
    <Card class="flex h-full overflow-y-auto gap-10 p-0">
        <section class="flex flex-col h-full w-1/3 border-r border-black/10">
          
            <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4">
                <p class="text-sm">
                    Nota: Los correos electrónicos podrían llegar a spam. Le recomendamos revisar periódicamente.
                </p>
            </div>

            <TabBar class="w-full max-w-[95%]" :tabs="tabs" :activeTab="activeTab.name" :onTabChange="setActiveTab" :auditorEnable="true"/>
            
            <component :selectedItem="selectedItem" :onSelectCallback="onItemSelect" :is="ActualTabComponent">
            </component>
        </section>

        <ProcessApprovalTab v-if="typeSelectedItem && typeSelectedItem === 'Process'"
            :process="(selectedItem as Process)" />

        <InstitutionRegisterTab v-if="typeSelectedItem && typeSelectedItem === 'InstitutionRequest'"
            :institutionRequest="(selectedItem as InstitutionRequest)" />
    </Card>
</template>
