<template>
  <div class="flex h-screen">
    <div class="w-1/2 p-8 bg-white flex flex-col">
      <div class="flex items-center gap-3 mb-4">
        <span class="text-2xl 2xl:text-3xl font-bold">e-</span>
        <img class="2xl:w-10 2xl:h-10 w-8 h-8 object-cover -ml-2 rounded-full" src="@/assets/logos/Logo-svg-v1_2.svg" />
        <span class="text-2xl 2xl:text-3xl -ml-2 font-bold">ransparencia</span>
      </div>
      <div class="flex flex-1 flex-col justify-center">
        <RegisterForm />
      </div>
      <div
        class="2xl:text-sm text-xs text-neutral-800 bg-yellow-200 border border-accent-5-400 bg-opacity-10 p-4 rounded-md text-center mt-4 max-w-2xl mx-auto">
        <div class="flex justify-center items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-accent-5-400" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 8v4m0 4h.01M21 12c0-5.523-4.478-10-10-10S1 6.477 1 12s4.478 10 10 10 10-4.477 10-10z" />
          </svg>
          <p>Recuerda revisar Spam si no recibes los correos asociados a la aplicación.</p>
        </div>
      </div>
    </div>
    <div class="w-1/2 flex justify-center items-center bg-primary-800">
      <img class="h-[50%] object-cover" src="@/assets/logos/auth-logo.png" />
    </div>
  </div>
</template>

<script setup lang="ts">
import RegisterForm from '@/components/molecules/FormRegister.vue';
</script>
