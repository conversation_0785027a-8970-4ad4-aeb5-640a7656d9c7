<template>
    <div class="terms-container">
        <h2 class="header-2">Consentimiento Informado</h2>

        <div ref="termsRef" class="terms-content" @scroll="handleScroll">
            <p>He leído y comprendido la siguiente información:</p>
            <br />
            <p>
                Participo de forma voluntaria y anónima en el proyecto ANID IDeA I+D 2024, código ID24I10006, titulado:
                “Plataforma tecnológica para la aplicación de diagnóstico de la transparencia en los procedimientos
                electrónicos”,
                dirigido por el Dr. <PERSON>, investigador responsable de la Universidad de La Frontera, junto
                a los coinvestigadores
                Dr. <PERSON><PERSON><PERSON> y <PERSON>. <PERSON>.
            </p>

            <p>
                He sido informado/a sobre los objetivos y procedimientos del estudio, así como del tipo de participación
                solicitada mediante esta encuesta.
            </p>

            <p>
                Comprendo que mi participación es voluntaria, que puedo negarme o retirarme en cualquier momento, sin
                necesidad de justificar
                mi decisión y sin consecuencias negativas.
            </p>

            <p>
                Acepto que la información que entregue será tratada de manera confidencial y será utilizada
                exclusivamente con fines de investigación.
                Los datos serán almacenados por el investigador responsable en archivos digitales seguros de la
                Universidad de La Frontera.
            </p>

            <p>
                Entiendo que los resultados serán reportados de manera global y que no será posible identificarme
                individualmente.
            </p>

            <p>
                Acepto que mis respuestas puedan ser utilizadas en investigaciones futuras de características similares,
                sin necesidad de volver a otorgar
                consentimiento, siempre respetando los principios de anonimato y confidencialidad.
            </p>
        </div>

        <div class="check-section">
            <input type="checkbox" id="acceptCheckbox" :disabled="!hasScrolledToEnd" v-model="checkboxChecked" />
            <label for="acceptCheckbox" v-if="!hasScrolledToEnd" class="text-neutral-500 body-4">
                He leído y acepto los términos del consentimiento informado. <span
                    class="body-4 text-customRed-100">(Debe leer todo el documento para poder firmarlo).</span>
            </label>
            <label v-else for="acceptCheckbox">
                He leído y acepto los términos del consentimiento informado.
            </label>
        </div>

        <button :disabled="!checkboxChecked" @click="acceptTerms">
            Aceptar y continuar
        </button>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { fetchCountConsent } from '@/services/api/surveyService';
export default defineComponent({
    name: 'SurveyConsentPage',
    setup() {
        const router = useRouter();
        const surveyId = window.location.pathname.split('/').pop();
        const termsRef = ref<HTMLElement | null>(null);
        const hasScrolledToEnd = ref(false);
        const checkboxChecked = ref(false);

        const handleScroll = () => {
            const el = termsRef.value;
            if (el && el.scrollTop + el.clientHeight >= el.scrollHeight - 5) {
                hasScrolledToEnd.value = true;
            }
        };

        onMounted(() => {
            const el = termsRef.value;
            if (el && el.scrollHeight <= el.clientHeight) {
                hasScrolledToEnd.value = true;
            }
        });


        const acceptTerms = async () => {
            try {
                if (surveyId && !isNaN(Number(surveyId))) {
                    const processId = Number(surveyId);
                    await fetchCountConsent(processId, checkboxChecked.value);
                    router.push(`/survey/${surveyId}/questions`);
                } else {
                    console.error("ID de encuesta no válido");
                }
            } catch (error) {
                console.error("Error al aceptar los términos:", error);
            }
        };

        return {
            termsRef,
            hasScrolledToEnd,
            checkboxChecked,
            handleScroll,
            acceptTerms,
        };
    },
});
</script>

<style scoped>
.terms-container {
    max-width: 800px;
    height: 80vh;
    margin: auto;
    padding: 30px;
    font-family: Arial, sans-serif;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    gap: 20px;
    justify-content: center;
    box-sizing: border-box;
}

.terms-content {
    height: 55vh;
    overflow-y: auto;
    background: #f9f7f7;
    padding: 20px;
    border-radius: 10px;
    line-height: 1.6;
    font-size: 16px;
}

.check-section {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;
}

button {
    align-self: flex-start;
    padding: 12px 24px;
    font-size: 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    background: #6200ea;
    color: #fff;
    transition: background 0.3s;
}

button:hover:enabled {
    background: #4b00c0;
}

button:disabled {
    background: #ccc;
    cursor: not-allowed;
}
</style>