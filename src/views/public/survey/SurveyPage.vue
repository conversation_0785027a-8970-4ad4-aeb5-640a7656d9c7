<template>
    <div class="survey-container" v-if="survey">
        <div class="flex flex-col gap-6">
            <h3 class="font-bold flex flex-col gap-1">{{ survey.title }}
                <p class="text-sm font-medium">Esta encuesta tiene como objetivo medir el nivel de transparencia de su
                    institución.
                </p>
            </h3>
            <div class="flex flex-col gap-2 pb-4">
                <h3>Progreso</h3>
                <div class="progress-bar">
                    <div class="progress" :style="{ width: progress + '%' }">
                        <span class="progress-text">{{ progress.toFixed(0) }}%</span>
                    </div>
                </div>
            </div>
        </div>
        <h3 class="text-xl font-semibold text-purple-700 border-b-2 border-purple-500 pb-1 mb-4">
            Dimensión: {{ currentSection.title }}
        </h3>
        <div class="survey-table-wrapper">
            <table class="survey-table">
                <thead>
                    <tr>
                        <th></th>
                        <th v-for="option in options" :key="option.value">{{ option.label }}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(question, index) in currentSection.questions" :key="index">
                        <td class="question-text">{{ question.text }}</td>
                        <td v-for="option in options" :key="option.value">
                            <label class="option-label">
                                <input type="radio" :name="'question' + question.id" :value="option.value"
                                    v-model="responses[question.id!]" />
                            </label>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="button-container">
            <button @click="prevSection" :disabled="sectionIndex === 0">Volver</button>
            <button @click="nextSection">
                {{ sectionIndex === survey.sections.length - 1 ? 'Enviar' : 'Siguiente' }}
            </button>
        </div>

        <div v-if="showModal" class="modal-overlay">
            <div class="modal">
                <h3>Confirmación</h3>
                <p>¿Estás seguro de que quieres enviar tus respuestas?</p>
                <div class="modal-button-container">
                    <button @click="confirmSubmit" :disabled="isSubmitting">
                        <template v-if="isSubmitting">
                            <span class="loader"></span> Enviando...
                        </template>
                        <template v-else>
                            Sí, enviar
                        </template>
                    </button>

                    <button @click="cancelSubmit">Cancelar</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, reactive, computed, ref, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import axios from 'axios';
import type { Survey } from '@/models/types';
import 'vue-toast-notification/dist/theme-sugar.css';
import { useToast } from 'vue-toast-notification';
import { Logger } from '@/services/logger';
const $toast = useToast();

export default defineComponent({
    name: 'SurveyPage',
    setup() {
        const survey = ref<Survey | null>(null);
        const options = reactive([
            { label: 'Muy en desacuerdo', value: '1' },
            { label: 'En desacuerdo', value: '2' },
            { label: 'Indeciso', value: '3' },
            { label: 'De acuerdo', value: '4' },
            { label: 'Muy de acuerdo', value: '5' }
        ]);

        const route = useRoute();
        const router = useRouter();
        const sectionIndex = ref(0);
        const showModal = ref(false);
        const isSubmitting = ref(false);
        const SURVEY_API_URL = `${import.meta.env.VITE_SURVEY_API_URL}/surveys`;

        const savedResponses = localStorage.getItem('surveyResponses');
        const responses = reactive<Record<number, string>>(savedResponses ? JSON.parse(savedResponses) : {});

        watch(
            () => responses,
            (newResponses) => {
                localStorage.setItem('surveyResponses', JSON.stringify(newResponses));
            },
            { deep: true }
        );

        const fetchSurvey = async () => {
            const surveyId = route.params.surveyId as string;
            try {
                const response = await axios.get(`${SURVEY_API_URL}/${surveyId}`);
                console.log('Survey data:', response.data);
                survey.value = response.data.data;
            } catch (error) {
                Logger.error('Error fetching survey:', error);
            }
        };

        onMounted(() => {
            fetchSurvey();
        });

        const currentSection = computed(() => survey.value?.sections[sectionIndex.value] ?? { title: '', questions: [] });

        const progress = computed(() => {
            const answeredQuestions = Object.keys(responses).length;
            const totalQuestions = survey.value?.sections.reduce((sum, section) => sum + section.questions.length, 0) || 1;
            return (answeredQuestions / totalQuestions) * 100;
        });

        const nextSection = () => {
            const allAnswered = currentSection.value.questions.every(question => responses[question.id!] !== undefined);

            if (!allAnswered) {
                alert('Por favor, responde todas las preguntas antes de continuar.');
                return;
            }

            if (sectionIndex.value < (survey.value?.sections.length ?? 0) - 1) {
                sectionIndex.value += 1;
            } else {
                showModal.value = true;
            }
        };

        const prevSection = () => {
            if (sectionIndex.value > 0) {
                sectionIndex.value -= 1;
            }
        };

        const cancelSubmit = () => {
            showModal.value = false;
        };

        const confirmSubmit = async () => {
            isSubmitting.value = true;
            try {
                const surveyId = survey.value?.id;
                const surveyResponseDTO = {
                    surveyId,
                    questionResponses: responses,
                };
                await axios.post(`${SURVEY_API_URL}/${surveyId}/responses`, surveyResponseDTO);

                localStorage.removeItem('surveyResponses');

                showModal.value = false;
                $toast.success('Respuestas enviadas con éxito');
                router.replace('/');
            } catch (error) {
                Logger.error('Error sending responses:', error);
                $toast.error('Ocurrió un error al enviar las respuestas. Intenta nuevamente.');
            } finally {
                isSubmitting.value = false;
            }
        };

        return {
            survey,
            currentSection,
            options,
            responses,
            sectionIndex,
            progress,
            nextSection,
            prevSection,
            showModal,
            confirmSubmit,
            cancelSubmit,
            isSubmitting,
        };
    }
});
</script>


<style scoped>
.survey-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

h2,
h3,
p {
    text-align: left;
    color: #333;
}

.progress-bar {
    height: 20px;
    background: #eee;
    border-radius: 5px;
    overflow: hidden;
    position: relative;
}

.progress {
    height: 100%;
    background: #6200ea;
    transition: width 0.3s;
    position: relative;
}

.progress-text {
    position: absolute;
    width: 100%;
    text-align: center;
    top: 0;
    left: 0;
    color: #fff;
    font-weight: bold;
    line-height: 20px;
}

.survey-table-wrapper {
    overflow-x: auto;
}

.survey-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
}

.survey-table th,
.survey-table td {
    border-bottom: 1px solid #ddd;
    border-top: 1px solid #ddd;
    padding: 8px;
    text-align: center;
    min-width: 75px;
}

.survey-table th {
    background-color: #f9f7f7;
    color: #333;
}

.option-label {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}

.option-label input {
    margin: 0;
    accent-color: #40189d;
}

.question-text {
    color: #333;
    text-align: left;
    white-space: normal;
    /* Permite el ajuste de texto */
    word-break: break-word;
    /* Rompe las palabras largas si es necesario */
    width: 60%;
    /* Ajusta el ancho de la celda de la pregunta */
}

.button-container {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    color: #fff;
}

button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

button:not(:disabled) {
    background: #6200ea;
}

@media (max-width: 768px) {
    .survey-container {
        padding: 10px;
    }

    .survey-table th,
    .survey-table td {
        padding: 4px;
        min-width: 200px;
    }

    button {
        padding: 8px 16px;
    }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    max-width: 400px;
    width: 100%;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.modal h3 {
    margin-bottom: 20px;
    color: #333;
}

.modal p {
    margin-bottom: 20px;
    color: #666;
}

.modal-button-container {
    display: flex;
    justify-content: space-around;
}

.modal-button-container button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.modal-button-container button:first-child {
    background: #6200ea;
    color: #fff;
}

.modal-button-container button:last-child {
    background: #ccc;
    color: #333;
}

.loader {
    border: 3px solid #f3f3f3;
    /* Gris claro */
    border-top: 3px solid #6200ea;
    /* Color principal */
    border-radius: 50%;
    width: 14px;
    height: 14px;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 8px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}
</style>