<template>
    <PublicNavBar />
    <ButtonSection></ButtonSection>
    <InfoSection></InfoSection>
    <ServiceSection></ServiceSection>
    <ContactSection></ContactSection>
    <PublicFooter/>
</template>
<script setup lang="ts">
import PublicNavBar from '@/components/layout/public/PublicNavBar.vue';
import ButtonSection from '@/components/molecules/ButtonSection.vue';
import InfoSection from '@/components/molecules/InfoSection.vue';
import ServiceSection from '@/components/organisms/ServiceSection.vue';
import ContactSection from '@/components/organisms/ContactSection.vue';
import PublicFooter from '@/components/layout/public/PublicFooter.vue';
</script>