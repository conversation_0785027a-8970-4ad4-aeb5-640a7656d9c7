<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import Pagination from '@/components/atoms/Pagination.vue';
import SearchBar from '@/components/molecules/SearchBar.vue';
import ViewOptionsToggle from '@/components/organisms/ViewOptionsToggle.vue';
import ProcessCard from '@/components/molecules/ProcessCard.vue';
import ProcessTile from '@/components/molecules/ProcessTile.vue';
import type { Process } from '@/models/types';
import { useProcesses } from '@/hooks/process/useProcesses';
import { useRoute } from 'vue-router';

const route = useRoute();
const processStatus = ref<string>(route.params.status as string);
const isDateAscending = ref(true); 

const { getRouterLinkByStatusAndID, canAction, processes, page, totalPages, fetchPageData, handleSearch, handleNextPage, handlePreviousPage, isLoading, handleGoToPage, handleGetButtonTextByStatus } = useProcesses({
  status: processStatus,
});

const sortedProcesses = computed(() => {
  return [...processes.value].sort((a, b) => {
    const dateA = new Date(a.startDate);
    const dateB = new Date(b.startDate);
    return isDateAscending.value ? dateA.getTime() - dateB.getTime() : dateB.getTime() - dateA.getTime();
  });
});

const headers = computed(() => ['ID', 'Fecha Inicio - Fin', 'Institución', 'Funcionarios', 'Ciudad', 'Contacto', 'Acción']);

onMounted(async () => {
  const parts = route.path.split('/');
  processStatus.value = parts[2];
  await fetchPageData(page.value);
});
const toggleDateSort = () => {
  isDateAscending.value = !isDateAscending.value;
};
</script>

<template>
  <div class="flex flex-col w-full gap-8">
    <SearchBar :onSearch="handleSearch" placeholder="Buscar instituciones..." />
    <ViewOptionsToggle 
      :isLoading="isLoading" 
      :items="sortedProcesses" 
      :headers="headers" 
      label="procesos" 
      :isDate="true"
      @toggleDateSort="toggleDateSort"
      :isDateAscending = "isDateAscending"
    >
      <template #grid="{ item }">
        <ProcessCard :process="(item as Process)" :path="getRouterLinkByStatusAndID(
          (item as Process).status, (item as Process).id)" 
          :buttonText="handleGetButtonTextByStatus((item as Process).status)" 
          :disabled="!canAction((item as Process).status)" class="process-card"/>
      </template>
      <template #list="{ item }">
        <ProcessTile :process="(item as Process)" :path="getRouterLinkByStatusAndID((item as Process).status,
          (item as Process).id)" :buttonText="handleGetButtonTextByStatus((item as Process).status)" 
          :disabled="!canAction((item as Process).status)" />
      </template>
    </ViewOptionsToggle>
    <Pagination :itemCount="sortedProcesses?.length ?? 0" :totalPages="totalPages" :page="page" @next="handleNextPage"
      @previous="handlePreviousPage" @go-to-page="handleGoToPage" />
  </div>
</template>


<style scoped>
.process-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  max-width: 100%; 
  height: auto;
  box-sizing: border-box; 
  background-color: white; 
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); 
}

.process-card .card-content {
  overflow: hidden; 
  text-overflow: ellipsis; 
  white-space: nowrap; 
}

.process-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .process-card {
    flex-direction: column;
    flex-wrap: wrap;
  }

  .process-card .card-header {
    flex-direction: column; 
    align-items: flex-start;
  }

  .process-card .card-content {
    white-space: normal; 
  }

  .process-card .card-header h3,
  .process-card .card-header p {
    font-size: 14px; 
  }
}

</style>