<script setup lang="ts">
import Card from '@/components/atoms/Card.vue';
import EvidenceAuditSection from '@/components/organisms/EvidenceAuditSection.vue';
import DimensionsWithQuestionsSkeleton from '@/components/templates/DimensionsWithQuestionsSkeleton.vue';
import { useProcesses } from '@/hooks/process/useProcesses';
import { useAuth } from '@/hooks/useAuth';
import { useDimensions } from '@/hooks/useDimensions';
import type { Dimension, Process } from '@/models/types';
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useAsyncFetch } from '@/hooks/useAsyncFetch';
import ProcessDetailsPageSkeleton from '@/components/templates/pages/ProcessDetailsPageSkeleton.vue';

const route = useRoute();
const { authData } = useAuth();
const actualProcess = ref<Process>();
const actualDimension = ref<Dimension>()
const dimensions = ref<Dimension[]>([])
const dimensionId = ref<number>(0);
const { handleGetProcessByID } = useProcesses();
const { handleGetDimensionsByProcessID } = useDimensions();
dimensionId.value = Number(route.params.processID);
const actualProcessId = Number(route.params.processID);
const handleGetDimensions = async () => {
  if (authData.value) {
    const process = await handleGetProcessByID(parseInt(route.params.processID as string));
    actualProcess.value = process;
    dimensions.value = await handleGetDimensionsByProcessID(actualProcessId);
    actualDimension.value = dimensions.value[0];
  }
}
const { fetchData, isLoading } = useAsyncFetch(handleGetDimensions)

onMounted(async () => {
  dimensionId.value = Number(route.params.id);
  await fetchData();
});


</script>

<template>
  <div class="w-full flex flex-col gap-8" v-if="!isLoading">
    <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-3 mb-3 max-w-full">
      <p class="text-sm">
        Nota: Los correos electrónicos relacionados con este proceso podrían llegar a spam. Le recomendamos revisa periódicamente.
      </p>
    </div>
    <Card class="flex flex-col gap-4">
      <div>
        <div class="text-lg font-bold">Este proceso está listo para auditar!</div>
        <div class="text-sm mt-2 text-gray-500">
          Recuerda evaluar las evidencias y sus condicionales, en caso de que no se cumplan, considera objetar una dimensión.
        </div>
      </div>
    </Card>
    <Suspense v-if="actualProcess">
      <EvidenceAuditSection v-model="actualProcess.institutionId" :id="actualProcessId" />
      <template #fallback>
        <DimensionsWithQuestionsSkeleton />
      </template>
    </Suspense>
  </div>
  <ProcessDetailsPageSkeleton v-else />
</template>

