<script setup lang="ts">
import Card from '@/components/atoms/Card.vue';
import InstitutionProcessSection from '@/components/atoms/InstitutionProcessSection.vue';
import MilestonesSection from '@/components/atoms/MilestonesSection.vue';
import { useRoute } from 'vue-router';
import { onMounted, ref } from 'vue';
import SummaryCardSkeleton from '@/components/templates/SummaryCardSkeleton.vue';
import type { Process } from '@/models/types';
import { useProcesses } from '@/hooks/process/useProcesses';

const route = useRoute();
const processID = ref<number>(0)
const process = ref<Process>();
const { handleGetProcessByID } = useProcesses();

const startDate = route.query.startDate as string;
const endDate = route.query.endDate as string;
onMounted(async () => {
  processID.value = parseInt(route.params.processID as string)
  process.value = await handleGetProcessByID(processID.value)

})
</script>


<template>
  <div class="flex flex-col gap-6 h-full w-full">
    <Card class="flex flex-col">
      <!-- se comentó este componente momentáneamente, debido a que no tenían sentido los datos que se mostraban -->
      <!-- <div class="text-lg font-bold">Respuestas en proceso</div> -->
      <!-- <CircularProgress></CircularProgress> -->
      <div>
        <div class="text-lg font-bold">Este proceso ha finalizado</div>
        <div class="text-sm mt-2 text-gray-500">Este proceso ya ha sido Auditado y Finalizado correctamente.</div>
      </div>
    </Card>
    <div class="flex gap-6">
      <Suspense v-if="processID !== 0">
        <InstitutionProcessSection :processID="processID" :startDate="startDate" :finishDate="endDate " />
        <template #fallback>
          <SummaryCardSkeleton />
        </template>
      </Suspense>
    </div>
  </div>
</template>
