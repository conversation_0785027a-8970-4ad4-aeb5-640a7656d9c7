<script setup lang="ts">
import Card from '@/components/atoms/Card.vue';
import EvidenceAuditSection from '@/components/organisms/EvidenceAuditSection.vue';
import DimensionsWithQuestionsSkeleton from '@/components/templates/DimensionsWithQuestionsSkeleton.vue';
import { useProcesses } from '@/hooks/process/useProcesses';
import { useAuth } from '@/hooks/useAuth';
import { useDimensions } from '@/hooks/useDimensions';
import type { Dimension, Process } from '@/models/types';
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useAsyncFetch } from '@/hooks/useAsyncFetch';
import ProcessDetailsPageSkeleton from '@/components/templates/pages/ProcessDetailsPageSkeleton.vue';

const route = useRoute();
const { authData } = useAuth();
const actualProcess = ref<Process>();
const actualDimension = ref<Dimension>()
const dimensions = ref<Dimension[]>([])
  const dimensionId = ref<number>(0);
  dimensionId.value = Number(route.params.processID);
  const actualProcessId = Number(route.params.processID);
const { handleGetProcessByID } = useProcesses();
const { handleGetDimensionsByProcessID } = useDimensions();

const handleGetDimensions = async () => {
  if (authData.value) {
    const process = await handleGetProcessByID(parseInt(route.params.processID as string));
    actualProcess.value = process;
    dimensions.value = await handleGetDimensionsByProcessID(actualProcessId);
    actualDimension.value = dimensions.value[0];
  }
}
const { fetchData } = useAsyncFetch(handleGetDimensions)

onMounted(async () => {
  await fetchData();
});

</script>

<template>
  <Suspense v-if="actualProcess">
    <EvidenceAuditSection 
    class="h-full"
    :modelValue="actualProcess.institutionId" 
    :appealMode="true"
    :id="actualProcessId"
  />
  <template #fallback>
    <DimensionsWithQuestionsSkeleton />
  </template>
  </Suspense>
</template>