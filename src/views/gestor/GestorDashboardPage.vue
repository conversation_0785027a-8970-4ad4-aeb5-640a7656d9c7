<script setup lang="ts">
import { ref, type Ref, watch } from 'vue'
import Card from '@/components/atoms/Card.vue'
import { useAuth } from '@/hooks/useAuth'
import { useInstitutions } from '@/hooks/useInstitutions'
import { useRoute } from 'vue-router';
import type { DashboardInstitution } from '@/models/types'
import arcChart from '@/components/organisms/RealTimeChartGestor.vue'
import surveysProcess from '@/components/organisms/SurveyProgressChart.vue'
import ProgressPaceChart from '@/components/molecules/ProgressPaceChart.vue'
import BarChart from '@/components/molecules/BarChart.vue'
import { convertUTCToLocalNaiveDate } from '@/helpers/formatters/dateFormatters'
import { useResponsePercentage } from '@/hooks/useResponsePercentage'
import { Logger } from '@/services/logger';
import DashboardGestorSkeleton from '@/components/templates/charts/DashboardGestorSkeleton.vue';
import NoDataDashboarGestor from '@/components/organisms/NoDataDashboarGestor.vue';

const noDataMessage = ref('Esperando recibir datos ');
const isWaiting = ref(true);
let timeoutId: ReturnType<typeof setTimeout> | null = null;

const actualInstitutionID = ref(-1)
const dashboardInstitutionData = ref<DashboardInstitution | null>(null)
const { dashboardGetInstitution } = useInstitutions()
const { authData } = useAuth()

let startDate = ref('')
let lastDate = ref('')
let estimateDate = ref('')
let currentDate = new Date()
const formattedToday = currentDate.toISOString().split('T')[0]

const lastTenDays: { day: number; month: string; responses: number; dayName: string }[] = []

let realAverage = 0
let idealAverage = 0
let totalAnswers = 0
let originalData: Ref<number[]> = ref([])
let projectionData: Ref<number[]> = ref([])
let answeredSurveys = 0
let surveyQuorum = 0
let activeChart = true
let daysLeft = 0
let daysPassed = 0
const today = new Date()
const options: Intl.DateTimeFormatOptions = { month: 'long' }

for (let i = 0; i < 10; i++) {
  const pastDate = new Date(today)
  pastDate.setDate(today.getDate() - i)

  const day = pastDate.getDate()
  const month = pastDate.toLocaleDateString('es-ES', options)
  const dayName = pastDate.toLocaleString('es-ES', { weekday: 'long' })

  lastTenDays.push({ day, month, responses: 0, dayName })
}

lastTenDays.sort((a, b) => {
  const monthMap: {[key: string]: number} = {
    'enero': 1, 'febrero': 2, 'marzo': 3, 'abril': 4, 
    'mayo': 5, 'junio': 6, 'julio': 7, 'agosto': 8, 
    'septiembre': 9, 'octubre': 10, 'noviembre': 11, 'diciembre': 12
  };

  const monthA = monthMap[a.month.toLowerCase()];
  const monthB = monthMap[b.month.toLowerCase()];
  if (Math.abs(monthA - monthB) > 6) {
    return monthB - monthA;
  }
  if (monthA !== monthB) {
    return monthA - monthB;
  }
  return a.day - b.day;
});


const { responsePorcentage } = useResponsePercentage(
  dashboardInstitutionData,
  originalData,
  projectionData,
  lastTenDays
)
const fetchDashboardInstitutionData = async (institutionId: number) => {
  try {
    const apiData = await dashboardGetInstitution(institutionId);
    if (apiData && apiData.processResult) {
      dashboardInstitutionData.value = apiData.processResult as unknown as DashboardInstitution;
      totalAnswers = dashboardInstitutionData.value.totalSurveys;
      answeredSurveys = dashboardInstitutionData.value.answeredSurveys;
      surveyQuorum = dashboardInstitutionData.value.surveyQuorum;
      daysLeft = dashboardInstitutionData.value.daysLeft;
      daysPassed = dashboardInstitutionData.value.daysPassed;


      if (timeoutId) clearTimeout(timeoutId);
      isWaiting.value = false;

      if (dashboardInstitutionData.value.processEndDate) {
        lastDate.value = convertUTCToLocalNaiveDate(dashboardInstitutionData.value.processEndDate).toISOString().split('T')[0];
      }
      if (dashboardInstitutionData.value.processStartDate) {
        startDate.value = convertUTCToLocalNaiveDate(dashboardInstitutionData.value.processStartDate).toISOString().split('T')[0];
      }
      if (dashboardInstitutionData.value.estimatedCompletionDate) {
        estimateDate.value = convertUTCToLocalNaiveDate(dashboardInstitutionData.value.estimatedCompletionDate).toISOString().split('T')[0];
      }

      responsePorcentage();
      activeChart = isChartEnabled(totalAnswers, answeredSurveys, daysLeft);

      const averages = dashboardInstitutionData.value.responseAverages;
      if (averages && averages.length > 0) {
        realAverage = averages[averages.length - 1].realAverage;
        idealAverage = averages[averages.length - 1].idealAverage;
      }
    } else {
      Logger.error('Invalid API response:', apiData);
    }
  } catch (error) {
    Logger.error('Error fetching dashboard institution data:', error);
  }
};


const isChartEnabled = (totalAnswers: number, answeredSurveys: number, daysLeft: number): boolean => {
  return answeredSurveys != totalAnswers || daysLeft != 0;
};
const getInstitutionIdFromRoute = (): number => {
  const route = useRoute();
  const institutionId = route.params.institutionID; 
  return institutionId ? parseInt(institutionId as string) : -1;
};
watch(
  authData,
  async (newValue) => {
    if (newValue && newValue.simpleInstitutionDTO) {
      actualInstitutionID.value = newValue.simpleInstitutionDTO.id;
    } else {
      actualInstitutionID.value = getInstitutionIdFromRoute();
    }
    if (actualInstitutionID.value !== -1) {
      timeoutId = setTimeout(() => {
        if (!dashboardInstitutionData.value) {
          noDataMessage.value = 'Es necesario iniciar un proceso';
          isWaiting.value = false;
        }
      }, 10000);
      await fetchDashboardInstitutionData(actualInstitutionID.value);
    } else {
      Logger.error('Institution ID was not found in authData or URL.');
    }
  },
  { immediate: true }
);
</script>

<template>
  <!-- Skeleton Loading State -->
  <section v-if="isWaiting" class="flex flex-col w-full gap-5 py-8">
    <DashboardGestorSkeleton />
  </section>

  <!-- Actual Dashboard Data -->
  <section v-else-if="dashboardInstitutionData" class="flex flex-col w-full gap-5 py-8">
    <section class="flex w-full gap-5 flex-wrap">
      <Card class="flex flex-col h-full flex-1 min-w-[300px] aspect-[16/11]">
        <arcChart
          :totalAnswers="totalAnswers"
          :answeredSurveys="answeredSurveys"
          :surveyQuorum="surveyQuorum"
          :startDate="startDate"
          :active="activeChart"
        />
      </Card>
      <Card class="flex flex-col h-full flex-1 min-w-[300px] aspect-[16/11]">
        <surveysProcess
          :startDate="startDate"
          :endDate="lastDate"
          :currentDate="formattedToday"
          :daysPassed="daysPassed"
          :daysLeft="daysLeft"
          :active="activeChart"
        />
      </Card>
    </section>
    <section class="flex w-full gap-5 flex-wrap">
      <Card class="flex flex-col h-full flex-1 min-w-[300px] aspect-[16/11]">
        <ProgressPaceChart
          :originalData="originalData"
          :projectionData="projectionData"
          :endDate="lastDate"
          :startDate="startDate"
          :estimationDate="estimateDate"
          :active="activeChart"
        />
      </Card>
      <Card class="flex flex-col h-full flex-1 min-w-[300px] aspect-[16/11]">
        <BarChart
          :totalAnswers="totalAnswers"
          :endDate="lastDate"
          :startDate="startDate"
          :mean="realAverage"
          :idealAverage="idealAverage"
          :lastTenDays="lastTenDays"
        />
      </Card>
    </section>
  </section>

  <!-- No Data State -->
  <section v-else>
    <NoDataDashboarGestor />
  </section>
</template>

<style scoped>
.dots-animation span {
  display: inline-block;
  animation: bounce 1.5s infinite;
  font-size: 1.25rem;
}

.dots-animation span:nth-child(2) {
  animation-delay: 0.2s;
}

.dots-animation span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8px);
  }
}
</style>
