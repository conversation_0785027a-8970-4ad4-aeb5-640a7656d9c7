<script setup lang="ts">
import EvidenceAuditSection from '@/components/organisms/EvidenceAuditSection.vue';
import DimensionsWithQuestionsSkeleton from '@/components/templates/DimensionsWithQuestionsSkeleton.vue';
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import UploadEvidencePage from './UploadEvidencePage.vue';

const route = useRoute();
const processID = ref<number>(0);

onMounted(async () => {
    processID.value = parseInt(route.params.processID as string);
});
</script>

<template>
    <Suspense v-if="processID !== 0">
        <UploadEvidencePage :appealMode="true" />
        <template #fallback>
            <DimensionsWithQuestionsSkeleton />
        </template>
    </Suspense>

</template>