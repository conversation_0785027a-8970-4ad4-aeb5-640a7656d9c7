<script setup lang="ts">
import Card from '@/components/atoms/Card.vue';
import Button from '@/components/atoms/Button.vue';
import { useAuth } from '@/hooks/useAuth';
import { useProcesses } from '@/hooks/process/useProcesses';
import { onMounted, ref } from 'vue';
import { useAsyncFetch } from '@/hooks/useAsyncFetch';
import ProcessDetailsPageSkeleton from '@/components/templates/pages/ModelDetailsPageSkeleton.vue';
import { ProcessStatus, type Process } from '@/models/types/index';
import { useProcessSteps } from '@/hooks/process/useProcessSteps';
import { useRoute } from 'vue-router';
import ProcessStepsSection from '@/components/organisms/ProcessStepsSection.vue';
import RecommendationsPage from './RecommendationsPage.vue';
import ProcessResultSection from '@/components/organisms/ProcessResultSection.vue';
import SummaryCardSkeleton from '@/components/templates/SummaryCardSkeleton.vue';
const { handleGetActualProcessByInstitutionID, handleGetProcessByID } = useProcesses();
const actualProcess = ref<Process | undefined>(undefined);
const route = useRoute();
import { formatDateToDDMMYYYY } from '@/helpers/formatters';
const handleFetchProcess = async () => {
    if (route.params.processID && parseInt(route.params.processID as string)) {
        const processID = parseInt(route.params.processID as string);
        actualProcess.value = await handleGetProcessByID(processID);
        return;
    } else if (authData.value?.simpleInstitutionDTO) {
        const result = await handleGetActualProcessByInstitutionID(authData.value!.simpleInstitutionDTO!.id);
            if (result !== null) {
            actualProcess.value = result;
            }
    }
};
const { authData } = useAuth();
const showScheduleSection = ref(false);


const { fetchData, isLoading } = useAsyncFetch(handleFetchProcess);

onMounted(async () => {
    await fetchData();
});
const { currentStep } = useProcessSteps(actualProcess);

</script>

<template>
    <ProcessDetailsPageSkeleton v-if="isLoading" />

    <div class="flex h-full w-full" v-else>


        <!-- ✅ Si el proceso existe y aún está activo -->
        <section v-if="actualProcess && new Date(actualProcess.startDate) <= new Date()"
            class="h-full w-full flex flex-col gap-10">
            <section class="flex w-full gap-8">
                <ProcessStepsSection :actualProcess="actualProcess" :currentStep="currentStep" />
            </section>
            <div class="text-xl font-semibold text-gray-800 cursor-pointer px-5 pt-10"
                @click="showScheduleSection = !showScheduleSection">
                Programar un nuevo proceso
                {{ showScheduleSection ? '▼' : '►' }}
            </div>
            <section v-if="actualProcess && showScheduleSection === true" class="w-full">
                <Card class="flex flex-col gap-5 w-full h-min">
                    <div class="flex w-full items-center justify-between">
                        <h3 class="text-xl font-bold">Agendar un nuevo proceso</h3>
                        <Button variant="primary">
                            <router-link :to="{
                                path: '/gestor/requestProcess/new',
                                query: { minStartDate: actualProcess.endDate }
                            }">
                                Agendar
                            </router-link>
                        </Button>
                    </div>
                    <p class="text-sm text-gray-700">
                        Puedes agendar un nuevo proceso para comenzar después del actual.
                        Asegúrate de que la fecha de inicio sea posterior al término del proceso vigente.
                    </p>
                    <p class="text-sm text-gray-600">
                        Fecha de término del proceso actual: <strong>{{ formatDateToDDMMYYYY(actualProcess.endDate)
                            }}</strong>
                    </p>
                </Card>
            </section>

        </section>


        <!-- ✅ Si no hay proceso actual o aún no inicia -->
        <div v-else>
            <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4">
                <p class="text-sm">
                    Nota: Los correos electrónicos relacionados con este proceso podrían llegar a spam. Recomendamos
                    revisar
                    periódicamente.
                </p>
            </div>
            <Card class="flex flex-col gap-5 w-full h-min">
                <div class="flex w-full items-center justify-between">
                    <h3 class="text-xl font-bold">Proceso de Autodiagnóstico General</h3>
                    <Button variant="primary">
                        <router-link
                            :to="{ path: '/gestor/requestProcess/new', query: { minStartDate: new Date().toISOString().split('T')[0] } }">
                            Solicitar
                        </router-link>
                    </Button>
                </div>
                <span class="font-semibold text-primary-800">e-Transparencia</span>
                <p class="flex flex-col gap-4 text-sm">
                    <span class="text-lg font-bold">Descripción del proceso</span>
                    El proceso de autodiagnóstico en nuestra aplicación web permite a las instituciones evaluar su
                    desempeño
                    en
                    diversas áreas mediante una serie de pasos intuitivos. Este proceso abarca seis dimensiones:
                    Rendición
                    de
                    Cuentas, Adquisición de Bienes, Adquisición de Servicios, Comunicación, Contratación de Personal e
                    Institucionalización. Cada dimensión requiere la presentación de evidencias para ser aprobada, y
                    estas
                    evidencias deben cumplir con criterios de aprobación específicos relacionados con cada área.
                </p>
                <div class="flex flex-col gap-2">
                    <span class="flex items-center gap-5 font-semibold text-base">
                        <i class="fa-solid fa-check text-green-600 text-xl"></i>
                        <p class="border-b border-black/10 py-3 w-[30%]">6 dimensiones</p>
                    </span>
                    <span class="flex items-center gap-5 font-semibold text-base">
                        <i class="fa-solid fa-check text-green-600 text-xl"></i>
                        <p class="border-b border-black/10 py-3 w-[30%]">54 preguntas</p>
                    </span>
                    <span class="flex items-center gap-5 font-semibold text-base">
                        <i class="fa-solid fa-check text-green-600 text-xl"></i>
                        <p class="border-b border-black/10 py-3 w-[30%]">18 evidencias</p>
                    </span>
                </div>
            </Card>
        </div>
    </div>
</template>
