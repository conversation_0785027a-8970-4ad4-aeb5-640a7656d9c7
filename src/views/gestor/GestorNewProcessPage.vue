<script setup lang="ts">
import Card from '@/components/atoms/Card.vue';
import DateRangePicker from '@/components/atoms/DateRangePicker.vue';
import ImportEmployeesSection from '@/components/organisms/ImportEmployeesSection.vue';
import { useAuth } from '@/hooks/useAuth';
import { useProcesses } from '@/hooks/process/useProcesses';
import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useToast } from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-sugar.css';

const $toast = useToast();

const startDate = ref<string>('');
const endDate = ref<string>('');
const { authData } = useAuth();
const { handleRequestProcess } = useProcesses();
const router = useRouter();
const route = useRoute();
const minStartDate = computed(() => route.query.minStartDate as string | undefined);

const handleSendProcessRequest = async (employees: any[]) => {
  try {
    const processName = 'Proceso de Autodiagnóstico';
    const institutionID = authData.value!.simpleInstitutionDTO!.id;
    await handleRequestProcess(institutionID, processName, employees, startDate.value, endDate.value);
    $toast.success('Solicitud enviada correctamente');
    router.replace('/gestor/dashboard');

  } catch (error) {
    if (error instanceof Error) {
      if (error.message.includes('A process already exists')) {
        $toast.warning('Actualmente hay un proceso agendado o solicitado. No se puede solicitar uno nuevo hasta que se complete el proceso actual.');
      } 
      else if(error.message.includes('The start date cannot be in the past')){
        $toast.warning('La fecha de incio ingresada no puede ser procesada. Favor de ingresar una nueva');
        }
      else {
        $toast.error('Error al enviar la solicitud');
      }
    } else {
      $toast.error('Error desconocido al enviar la solicitud');
    }
  }
};

</script>

<template>
    <Card class="flex flex-col gap-10">
      <h3 class="text-lg font-bold">Seleccionar periodo de evaluación</h3>
      <!-- <h5 class="body-4 -mt-10">Por favor, ingresa una fecha de inicio con un día adicional al que necesite, ya que esto nos permite realizar los cálculos necesarios correctamente.</h5>-->
      <DateRangePicker class="w-[50%]" v-model:modelValueStart="startDate" v-model:modelValueEnd="endDate" :minDate="minStartDate || undefined"
 />
    </Card>
    <Card class="flex flex-col gap-10 mt-5">
      <ImportEmployeesSection :canAction="true" :onProcessRequest="handleSendProcessRequest" />
    </Card>
    
</template>
