<script setup lang="ts">
import Card from '@/components/atoms/Card.vue';
import Button from '@/components/atoms/Button.vue';
import SendSurveysModal from '@/components/molecules/SendSurveysModal.vue';
import { onMounted, ref, watch } from 'vue';
import ProcessStepsSection from '@/components/organisms/ProcessStepsSection.vue';
import { useProcessSteps } from '@/hooks/process/useProcessSteps';
import ObtainedAnswersSection from '@/components/organisms/ObtainedAnswersSection.vue';
import MilestonesSection from '@/components/atoms/MilestonesSection.vue';
import { ProcessStatus, type Process } from '@/models/types/index';
import { useProcesses } from '@/hooks/process/useProcesses';
import { useAuth } from '@/hooks/useAuth';
import { useAsyncFetch } from '@/hooks/useAsyncFetch';
import GestorActualPageSkeleton from '@/components/templates/pages/ProcessDetailsPageSkeleton.vue';
import { useRoute } from 'vue-router';
import RecommendationsPage from './RecommendationsPage.vue';
import ProcessResultSection from '@/components/organisms/ProcessResultSection.vue';
import SummaryCardSkeleton from '@/components/templates/SummaryCardSkeleton.vue';

const { authData } = useAuth();
const { handleGetActualProcessByInstitutionID, handleGetProcessByID } = useProcesses();
const isSendSurveysModalOpen = ref(false);
const actualProcess = ref<Process>();
const route = useRoute();

const handleFetchProcess = async () => {
    if (route.params.processID && parseInt(route.params.processID as string)) {
        const processID = parseInt(route.params.processID as string);
        actualProcess.value = await handleGetProcessByID(processID);
        return;
    } else if (authData.value?.simpleInstitutionDTO) {
        actualProcess.value = await handleGetActualProcessByInstitutionID(authData.value!.simpleInstitutionDTO!.id) ?? undefined;
    }
};

const { fetchData, isLoading } = useAsyncFetch(handleFetchProcess);

onMounted(async () => {
    await fetchData();
});

const { currentStep, handleButtonAction, buttonLabel, processStatus, buttonEnabled } = useProcessSteps(actualProcess);


</script>

<template>
    <GestorActualPageSkeleton v-if="isLoading" />
    <div v-else>
        <section class="h-full w-full flex flex-col gap-10" v-if="actualProcess">
            <!-- respuestas conseguidas -->
            <Card class="flex flex-col gap-10 w-full shadow-md">
                <!-- <ObtainedAnswersSection :currentStep="currentStep" :actualProcess="actualProcess" /> -->
                <div class="flex w-full items-center justify-between">
                    <div class="flex flex-col gap-2 xl:w-[70%]">
                        <h3 class="font-semibold text-lg">{{ processStatus.title }}</h3>
                        <p class="text-sm text-black/50"> {{ processStatus.description }}</p>
                    </div>
                    <Button v-if="buttonEnabled" @click="handleButtonAction">
                        {{ buttonLabel }}
                    </Button>
                </div>

            </Card>


            <section v-if="actualProcess.status === ProcessStatus.FINISHED" class="flex flex-col gap-10 w-full">
                <h3 class="text-xl font-semibold"> Recomendaciones sugeridas en este proceso</h3>
                <RecommendationsPage />
            </section>
        </section>
        <section v-else class="w-full h-[90vh] flex flex-col items-center justify-center">
            <Card class="flex flex-col gap-10 items-center mb-28 max-w-[40%]">
                <svg fill="none" height="200" viewBox="0 0 48 48" width="200" xmlns="http://www.w3.org/2000/svg">
                    <g clip-rule="evenodd" fill="#333" fill-rule="evenodd">
                        <path
                            d="m18.7071 4.69719c.3905-.39052 1.0237-.39052 1.4142 0l8.4853 8.48531c.3905.3905.3905 1.0237 0 1.4142-.3906.3905-1.0237.3905-1.4142 0l-8.4853-8.4853c-.3905-.39052-.3905-1.02369 0-1.41421z" />
                        <path
                            d="m28.7071 4.7068c.3905.39053.3905 1.02369 0 1.41422l-8.4853 8.48528c-.3905.3905-1.0237.3905-1.4142 0s-.3905-1.0237 0-1.4142l8.4853-8.4853c.3905-.39052 1.0237-.39052 1.4142 0z" />
                        <path
                            d="m24.3162 15.0513c-.2052-.0684-.4271-.0684-.6324 0l-14.81529 4.9376c-.22248.0741-.40551.2213-.52604.4096l-3.94442 5.0628c-.19955.2562-.26232.5932-.16835.904s.33299.5567.64102.6593l3.32253 1.1073.0027 8.6315c.00041 1.291.82662 2.4369 2.05135 2.8451l13.3818 4.4606c.1184.0473.2447.0715.3718.0714.1284.0005.256-.0237.3756-.0715l13.3812-4.4604c1.225-.4083 2.0512-1.5546 2.0513-2.8458l.0006-8.63 3.325-1.1082c.3081-.1026.5471-.3485.6411-.6593.0939-.3108.0312-.6478-.1684-.904l-3.9956-5.1286c-.119-.1567-.2843-.2785-.4802-.3438zm-14.77279 7.0599 12.80259 4.2668-2.6982 3.4633-12.8026-4.2668zm14.45909 2.7091 11.6501-3.8827-11.6526-3.8835-11.65 3.8826zm-13.8065 11.9425-.0025-7.9642 9.4925 3.1636c.402.134.8447.0001 1.1051-.3341l2.2092-2.8357-.0002 12.9589-12.1204-4.0401c-.4082-.1361-.6836-.5181-.6837-.9484zm27.6135-7.9635-9.4902 3.1629c-.4019.134-.8446.0001-1.105-.3341l-2.213-2.8405.0036 12.9638 12.1203-4.0401c.4084-.1361.6838-.5182.6838-.9486zm-9.4519 1.042-2.6993-3.4646 12.8026-4.2668 2.6993 3.4646z" />
                    </g>
                </svg>

                <p class="text-center">Su institución no tiene un proceso activo aún. Cuando tenga un proceso activo,
                    los resultados aparecerán aquí.</p>

                <RouterLink :to="{ path: '/gestor/requestProcess' }">
                    <Button>Solicitar un proceso</Button>
                </RouterLink>

            </Card>

        </section>

        <SendSurveysModal :onSend="handleButtonAction" v-model="isSendSurveysModalOpen" />
    </div>

</template>