<script setup lang="ts">
import Card from '@/components/atoms/Card.vue'
import Button from '@/components/atoms/Button.vue'
import { onMounted, ref, type Ref } from 'vue'
import { useProcessSteps } from '@/hooks/process/useProcessSteps'
import { type DashboardInstitution, type Institution, type Process } from '@/models/types/index'
import { useProcesses } from '@/hooks/process/useProcesses'
import { useAuth } from '@/hooks/useAuth'
import { useAsyncFetch } from '@/hooks/useAsyncFetch'
import { useRoute } from 'vue-router'
import ColorBarChart from '@/components/molecules/ColorBarChart.vue'
import { useInstitutions } from '@/hooks/useInstitutions'
import InstitutionKPICard from '@/components/molecules/InstitutionKPICard.vue'
import ModalLevel from '@/components/molecules/ModalLevel.vue'
import GeneralLevelCard from '@/components/organisms/GeneralLevelCard.vue'
import ObservationsSection from '@/components/organisms/ObservationsSection.vue'
import { Logger } from '@/services/logger'
import { useEvidencesData } from '@/hooks/useEvidencesData'
import RecommendationsSection from '@/components/organisms/RecommendationsSection.vue'

const { handleGetInstitutionByManagerID } = useInstitutions()
const actualInstitutionID = ref(-1)
const { dashboardGetInstitution } = useInstitutions()
const { authData } = useAuth()
const { handleGetProcessByID } = useProcesses()
const actualProcess = ref<Process | undefined>();
const actualInstitution = ref<Institution>()
const route = useRoute()
const dashboardInstitutionData = ref<DashboardInstitution | null>(null)
const { sectionsWithComments, fetchEvidenceData } = useEvidencesData();

let levelData: Ref<number[]> = ref([])
let levelDataAux: Ref<number[]> = ref([1, 1, 1, 1, 1, 1])
const isModalVisible = ref(false);

const openModal = () => { isModalVisible.value = true }
const closeModal = () => { isModalVisible.value = false }

const handleFetchProcess = async () => {
    const processID = parseInt(route.params.processID as string)
    if (isNaN(processID)) {
        Logger.error('ID inválido en la ruta')
        return
    }

    try {
        actualProcess.value = await handleGetProcessByID(processID)

        if (!authData.value?.simpleInstitutionDTO?.id) {
            Logger.error('No se encontró ID de institución en authData')
            return
        }

        const institutionID = parseInt(authData.value.simpleInstitutionDTO.id as unknown as string)
        if (isNaN(institutionID)) {
            Logger.error('ID de institución inválido')
            return
        }

        actualInstitution.value = await handleGetInstitutionByManagerID(institutionID)

        const apiData = await dashboardGetInstitution(institutionID)
        if (apiData && apiData.processResult) {
            dashboardInstitutionData.value = apiData.processResult as unknown as DashboardInstitution
            if (dashboardInstitutionData.value?.dimensionLevels) {
                levelData.value = dashboardInstitutionData.value.dimensionLevels
            }
        } else {
            Logger.error('Invalid API response:', apiData)
        }
    } catch (error) {
        Logger.error('Error en handleFetchProcess:', error)
    }
}

const { fetchData } = useAsyncFetch(handleFetchProcess)

onMounted(async () => {
    if (authData.value?.simpleInstitutionDTO) {
        actualInstitutionID.value = authData.value.simpleInstitutionDTO.id
        await fetchEvidenceData(actualInstitutionID.value)
        try {
            await fetchData()
        } catch (error) {
            Logger.error('Error fetching data:', error)
        }
    }
})

const { buttonLabel, processStatus, buttonEnabled } = useProcessSteps(actualProcess)
</script>

<template>

    <section class="2xl:container 2xl:mx-auto">
        <Card class="flex flex-col gap-10 w-full shadow-md 2xl:p-8">
            <div class="flex w-full items-center justify-between">
                <div class="flex items-center gap-2 2xl:gap-4">
                    <i :class="processStatus.icon + ' text-4xl 2xl:text-5xl ' + processStatus.color"></i>
                    <h3 class="font-semibold text-xl 2xl:text-2xl">
                        Revisión de proceso finalizado #ID-{{ route.params.processID }}
                    </h3>
                </div>

                <RouterLink v-if="buttonEnabled && actualProcess?.status === 'APPEALABLE' && actualProcess?.id" :to="{
                    path: `/gestor/process/${actualProcess.id}/appeal`,
                    query: {
                        allSections: JSON.stringify(sectionsWithComments)
                    }
                }">
                    <Button>{{ buttonLabel }}</Button>
                </RouterLink>
            </div>

            <p class="text-sm 2xl:text-base text-black/50">
                Esta página corresponde a un proceso anterior ya finalizado. A continuación puede revisar sus
                resultados, observaciones y recomendaciones asociadas.
            </p>
        </Card>

        <section class="flex flex-col w-full mt-5 2xl:mt-8 gap-10 2xl:gap-12">
            <section class="flex flex-col lg:flex-row w-full 2xl:gap-12 gap-8">
                <!-- Columna Izquierda -->
                <section class="flex flex-col gap-10 lg:w-1/2 w-full">
                    <GeneralLevelCard v-if="dashboardInstitutionData && actualProcess"
                        :level="dashboardInstitutionData.institutionLevel ?? 1"
                        :institutionID="actualInstitutionID"
                        :processId="actualProcess.id"
                        class="2xl:p-6" />
                    <div v-else class="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
                    <ObservationsSection class="2xl:p-6" />
                </section>

                <!-- Modal -->
                <div v-if="isModalVisible"
                    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
                    <ModalLevel :isVisible="isModalVisible" @close="closeModal" />
                </div>

                <!-- Columna Derecha -->
                <section class="flex flex-col gap-10 lg:w-1/2 w-full">
                    <Card class="flex flex-col gap-5 h-full 2xl:p-6">
                        <div class="flex justify-between items-center mb-4 2xl:mb-6">
                            <h3 class="text-lg 2xl:text-xl font-semibold">Nivel por dimensión</h3>
                            <a href="#" class="text-indigo-600 text-sm 2xl:text-base font-semibold" @click="openModal">
                                Más información
                            </a>
                        </div>
                        <div class="flex justify-center items-center h-full">
                            <ColorBarChart v-if="levelData.length > 0" class="w-full h-full" :levelData="levelData" />
                            <ColorBarChart v-else class="w-full h-full" :levelData="levelDataAux" />
                        </div>
                    </Card>

                    <!-- KPI -->
                    <section v-if="actualInstitution && actualProcess" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 2xl:gap-8 w-full">
                        <InstitutionKPICard class="flex flex-col justify-between h-full 2xl:p-6"
                            icon="fa-solid fa-briefcase text-green-600 2xl:text-6xl text-2xl"
                            label="Procesos realizados" :value="(actualInstitution.nprocesses?.toString() || actualInstitution.processes?.length?.toString() || '1')">
                            <RouterLink :to="{ path: '/gestor/record' }"
                                class="text-primary-800 font-semibold 2xl:body-1 body-3">
                                Ver historial
                            </RouterLink>
                        </InstitutionKPICard>

                        <InstitutionKPICard class="flex flex-col justify-between h-full 2xl:p-6"
                            icon="fa-solid fa-comment-dots text-purple-800 text-3xl 2xl:text-6xl"
                            label="Funcionarios encuestados" :value="(actualProcess.nemployees?.toString() || '0')">
                        </InstitutionKPICard>

                        <!-- TODO: Revisar por que estan hardcodeados -->
                        <InstitutionKPICard class="flex flex-col justify-between h-full 2xl:p-6"
                            icon="fa-solid fa-phone text-purple-800 text-2xl 2xl:text-6xl" label="Recomendaciones"
                            :value="'6'">
                            <RouterLink :to="{ path: '/gestor/recommendations' }"
                                class="text-purple-800 font-semibold 2xl:text-lg">
                                Ver más
                            </RouterLink>
                        </InstitutionKPICard>
                    </section>
                    <section v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 2xl:gap-8 w-full">
                        <div class="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
                        <div class="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
                        <div class="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
                    </section>
                </section>
            </section>
        </section>
    </section>
<section v-if="actualProcess" class="mt-10">
  <h2 class="text-xl font-semibold mb-4">Recomendaciones del proceso</h2>
  <RecommendationsSection :processId="actualProcess.id" />
</section>
<section v-else class="mt-10">
  <h2 class="text-xl font-semibold mb-4">Recomendaciones del proceso</h2>
  <div class="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
</section>


</template>