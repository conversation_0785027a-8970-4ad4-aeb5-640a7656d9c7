<script setup lang="ts">
import Button from '@/components/atoms/Button.vue'
import DimensionsWithQuestionsSection from '@/components/organisms/DimensionsWithQuestionsSection.vue'
import EvidenceTile from '@/components/atoms/EvidenceTile.vue'
import EvidenceModal from '@/components/molecules/EvidenceModal.vue'
import type { Dimension, Evidence, Process } from '@/models/types/index'
import { onMounted, ref } from 'vue'
import { useProcesses } from '@/hooks/process/useProcesses'
import { useAuth } from '@/hooks/useAuth'
import { useDimensions } from '@/hooks/useDimensions'
import { useAsyncFetch } from '@/hooks/useAsyncFetch'
import DimensionsWithQuestionsSkeleton from '@/components/templates/DimensionsWithQuestionsSkeleton.vue'
import { useEvidences } from '@/hooks/useEvidences'
import EvidenceAppealTile from '@/components/molecules/EvidenceAppealTile.vue'
import { useRouter } from 'vue-router'
import DimensionsWhitQuestionAudit from '@/components/organisms/DimensionsWhitQuestionAudit.vue'
const props = defineProps({
  appealMode: {
    type: Boolean,
    required: false,
    default: false
  }
})

const router = useRouter()
const dimensions = ref<Dimension[]>([])
const actualDimension = ref(dimensions.value![0])
const uploadedEvidences = ref<Evidence[]>([])
const actualProcess = ref<Process | undefined>(undefined)
const { authData } = useAuth()
const { handleGetActualProcessByInstitutionID } = useProcesses()
const { handleGetDimensionsByProcessID, handleGetRejectedDimensionsByProcessID } = useDimensions()
const { handleUploadEvidence, handleDeleteEvidence, handleAppealEvidence } = useEvidences()

const handleFetchDimensions = async () => {
  actualProcess.value = await handleGetActualProcessByInstitutionID(
    authData.value!.simpleInstitutionDTO!.id
  ) ?? undefined;

  dimensions.value = props.appealMode
    ? await handleGetRejectedDimensionsByProcessID(actualProcess.value!.id)
    : await handleGetDimensionsByProcessID(actualProcess.value!.id)

  if (dimensions.value.length > 0) {
    actualDimension.value = dimensions.value[0]
  }
}
const onEvidenceUpload = async (evidence: Evidence, onModalClose: () => void) => {
  props.appealMode
    ? await handleAppealEvidence(evidence, dimensions)
    : await handleUploadEvidence(
        actualProcess.value!.id,
        actualDimension.value,
        evidence,
        dimensions
      )
  onModalClose()
}

const onEvidenceDelete = async (evidence: Evidence) => {
  await handleDeleteEvidence(evidence, actualDimension.value, dimensions)
}

const { fetchData: handleFetch, isLoading } = useAsyncFetch(handleFetchDimensions)

onMounted(async () => {
  await handleFetch()
})
</script>

<template>
  <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4">
    <p class="text-sm">
      Nota: Los correos electrónicos relacionados con este proceso podrían llegar a spam.
      Recomendamos revisar periódicamente.
    </p>
  </div>

  <DimensionsWithQuestionsSkeleton v-if="isLoading" />
  <div class="h-full w-full" v-else>
    <div v-if="appealMode">
      <DimensionsWithQuestionsSection
        v-if="dimensions.length > 0"
        v-model="actualDimension"
        class="h-full"
        :dimensions="dimensions"
        :selectable="true"
      >
        <template #title>
          <h3 class="2xl:text-xl xl:text-lg font-bold">Dimensiones a apelar</h3>
        </template>
        <template #evidences="{ evidence }">
          <EvidenceAppealTile
            v-if="appealMode"
            :key="evidence.id"
            :onEvidenceUpload="onEvidenceUpload"
            :evidence="
              (uploadedEvidences.find((e) => e.title === evidence.title) as Evidence) ?? evidence">
            <template #button-text> Actualizar </template>
            <template #evidence-modal="{ isEvidenceModalOpen, onModalClose, evidence }">
              <EvidenceModal
                :isEvidenceModalOpen="isEvidenceModalOpen"
                :onModalClose="onModalClose"
                :onEvidenceDelete="onEvidenceDelete"
                :isAppealMode="appealMode"
                :onEvidenceUpload="(evidence: Evidence) => onEvidenceUpload(evidence, onModalClose)"
                :evidence="evidence"
              >
              </EvidenceModal>
            </template>
          </EvidenceAppealTile>
          <EvidenceTile
            v-else
            :key="evidence.title"
            :onEvidenceUpload="onEvidenceUpload"
            :evidence="
              (uploadedEvidences.find((e) => e.title === evidence.title) as Evidence) ?? evidence
            "
          >
            <template #evidence-modal="{ isEvidenceModalOpen, onModalClose, evidence }">
              <EvidenceModal
                :isEvidenceModalOpen="isEvidenceModalOpen"
                :onModalClose="onModalClose"
                :onEvidenceDelete="onEvidenceDelete"
                :isAppealMode="appealMode"
                :onEvidenceUpload="(evidence: Evidence) => onEvidenceUpload(evidence, onModalClose)"
                :evidence="evidence"
              >
              </EvidenceModal>
            </template>
          </EvidenceTile>
        </template>
        <template v-if="appealMode" #dimension-footer>
          <p class="text-justify 2xl:text-base xl:text-sm">
            En esta sección se presentan las dimensiones y evidencias objetadas, con el fin de
            actualizar la entrega corregida conforme a las observaciones del auditor.
          </p>
        </template>
        <template v-else #end-button>
          <Button @click="() => router.back()" class="w-40"> Finalizar </Button>
        </template>
      </DimensionsWithQuestionsSection>
    </div>
    <div v-else>
      <DimensionsWhitQuestionAudit
        v-if="dimensions.length > 0"
        v-model="actualDimension"
        class="h-full"
        :dimensions="dimensions"
        :selectable="true"
      >
        <template #title>
          <h3 class="2xl:text-xl xl:text-lg font-bold">Dimensiones a evaluar</h3>
        </template>
        <template #evidences="{ evidence }">
          <EvidenceAppealTile
            v-if="appealMode"
            :key="evidence.id"
            :onEvidenceUpload="onEvidenceUpload"
            :evidence="
              (uploadedEvidences.find((e) => e.title === evidence.title) as Evidence) ?? evidence
            "
          >
            <template #button-text> Actualizar </template>
            <template #evidence-modal="{ isEvidenceModalOpen, onModalClose, evidence }">
              <EvidenceModal
                :isEvidenceModalOpen="isEvidenceModalOpen"
                :onModalClose="onModalClose"
                :onEvidenceDelete="onEvidenceDelete"
                :onEvidenceUpload="(evidence: Evidence) => onEvidenceUpload(evidence, onModalClose)"
                :evidence="evidence"
              >
              </EvidenceModal>
            </template>
          </EvidenceAppealTile>
          <EvidenceTile
            v-else
            :key="evidence.title"
            :onEvidenceUpload="onEvidenceUpload"
            :evidence="
              (uploadedEvidences.find((e) => e.title === evidence.title) as Evidence) ?? evidence
            "
          >
            <template #evidence-modal="{ isEvidenceModalOpen, onModalClose, evidence }">
              <EvidenceModal
                :isEvidenceModalOpen="isEvidenceModalOpen"
                :onModalClose="onModalClose"
                :onEvidenceDelete="onEvidenceDelete"
                :onEvidenceUpload="(evidence: Evidence) => onEvidenceUpload(evidence, onModalClose)"
                :evidence="evidence"
              >
              </EvidenceModal>
            </template>
          </EvidenceTile>
        </template>
        <template v-if="appealMode" #dimension-footer>
          <p class="text-justify 2xl:text-base xl:text-sm">
            En esta sección se presentan las dimensiones y evidencias objetadas, con el fin de
            actualizar la entrega corregida conforme a las observaciones del auditor.
          </p>
        </template>
        <template v-else #end-button>
          <Button @click="() => router.back()" class="w-40"> Finalizar </Button>
        </template>
      </DimensionsWhitQuestionAudit>
    </div>
  </div>
</template>
