<script setup lang="ts">
import ProcessHistoryTile from '@/components/molecules/ProcessHistoryTile.vue';
import SearchBar from '@/components/molecules/SearchBar.vue';
import ListView from '@/components/organisms/ListView.vue';
import { formatLabel } from '@/helpers/stringHelpers';
import { useAuth } from '@/hooks/useAuth';
import { processStatusesMap } from '@/mappers';
import type { Process } from '@/models/types';
import { computed, onMounted, ref } from 'vue';
import { useProcesses } from '@/hooks/process/useProcesses';
import Button from '@/components/atoms/Button.vue';
import TrendChart from '@/components/molecules/TrendChart.vue';

const { authData } = useAuth();
const institutionID = ref<number>(authData.value?.simpleInstitutionDTO?.id ?? 0);
const { processes, page, fetchPageData, handleSearch, isLoading } = useProcesses({ institutionID: institutionID });

const headers = computed(() => ['Estado', 'ID', 'Inicio', 'Termino', 'Funcionarios', '% de Respuestas', 'Nivel General Alcanzado', 'Acción']);

const iconMap: { [key: string]: string } = {
    "Finalizado": 'fa-regular fa-circle-check',
    "En Progreso": 'fa-solid fa-clock-rotate-left',
    "Pendiente": 'fa-solid fa-hourglass',
};
const colorMap: { [key: string]: string } = {
    "Finalizado": 'text-green-500',
    "En Progreso": 'text-yellow-500',
    "Pendiente": 'text-red-500',
};

const getIconClass = (status: string): string => {
    const iconClass = iconMap[status] || 'fa-circle';
    const colorClass = colorMap[status] || 'text-gray-500';
    return `${iconClass} ${colorClass}`;
};

const isModalVisible = ref(false);

const showModal = () => {
    isModalVisible.value = true;
};

const closeModal = () => {
    isModalVisible.value = false;
};

const closeModalIfClickedOutside = (event: MouseEvent) => {
    const modal = event.target as HTMLElement;
    if (modal.classList.contains('modal-overlay')) {
        closeModal();
    }
};

onMounted(async () => {
    await fetchPageData(page.value);
});
</script>

<template>
    <section class="flex flex-col gap-10">
        <SearchBar :onSearch="handleSearch" />
        <ListView :items="processes" :headers="headers" :isLoading="isLoading">
            <template #header>
                <div class="flex w-full gap-12 text-sm items-center">
                    <span class="font-semibold"> Mostrando {{ formatLabel(processes.length, 'proceso', 'procesos') }}</span>
                    <div class="flex items-center gap-5">
                        <span class="flex gap-2 items-center" v-for="processStatus in processStatusesMap" :key="processStatus.value">
                            <i :class="getIconClass(processStatus.text)" class="text-2xl"></i>
                            {{ processStatus.text }}
                        </span>
                    </div>
                    <!-- Botón para ver el gráfico 
                    <Button :variant="'invert'" class="btn btn-primary ml-auto" @click="showModal">Ver Tendencia</Button> -->
                </div>
            </template>
            <template #default="{ item }">
                <ProcessHistoryTile :process="(item as Process)" />
            </template>
        </ListView>
        
        <div v-if="isModalVisible" @click="closeModalIfClickedOutside" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 modal-overlay"></div>
        <transition name="modal">
            <div v-if="isModalVisible" class="fixed inset-0 flex items-center justify-center bg-transparent">
                <div class="bg-white p-4 rounded-lg shadow-lg w-full md:w-2/3 relative">
                    <button
                        class="absolute top-4 right-4 w-8 h-8 rounded-full bg-red-600 text-white text-2xl flex items-center justify-center font-bold"
                        @click="closeModal">
                        &times;
                    </button>
                    <TrendChart />
                    <!-- Leyenda -->
                    <div class="absolute bottom-4 left-4 text-gray-500 text-sm">
                        * Para apreciar correctamente el gráfico, es necesario que existan dos o más procesos para esta institución.
                    </div>
                </div>
            </div>
        </transition>
    </section>
</template>


<style scoped>
.fixed {
    z-index: 1000; 
}

.modal-overlay {
    z-index: 999; 
}
.modal-enter-active,
.modal-leave-active {
    transition: all 0.3s ease-in-out;
}

.modal-enter-from {
    opacity: 0;
    transform: translateY(-100%);
}

.modal-enter-to {
    opacity: 1;
    transform: translateY(0);
}

.modal-leave-from {
    opacity: 1;
    transform: translateY(0);
}

.modal-leave-to {
    opacity: 0;
    transform: translateY(-100%);
}
</style>
