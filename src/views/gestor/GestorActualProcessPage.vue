<script setup lang="ts">
import Card from '@/components/atoms/Card.vue'
import Button from '@/components/atoms/Button.vue'
import { onMounted, ref, type Ref } from 'vue'
import { useProcessSteps } from '@/hooks/process/useProcessSteps'
import { type DashboardInstitution, type Institution, type Process } from '@/models/types/index'
import { useProcesses } from '@/hooks/process/useProcesses'
import { useAuth } from '@/hooks/useAuth'
import { useAsyncFetch } from '@/hooks/useAsyncFetch'
import { useRoute } from 'vue-router'
import ColorBarChart from '@/components/molecules/ColorBarChart.vue'
import { useInstitutions } from '@/hooks/useInstitutions'
import InstitutionKPICard from '@/components/molecules/InstitutionKPICard.vue'
import ModalLevel from '@/components/molecules/ModalLevel.vue'
import GeneralLevelCard from '@/components/organisms/GeneralLevelCard.vue'
import ObservationsSection from '@/components/organisms/ObservationsSection.vue'
import { Logger } from '@/services/logger'
import { useEvidencesData } from '@/hooks/useEvidencesData'

const { handleGetInstitutionByManagerID } = useInstitutions()
const actualInstitutionID = ref(-1)
const { dashboardGetInstitution } = useInstitutions()
const { authData } = useAuth()
const { handleGetActualProcessByInstitutionID, handleGetProcessByID } = useProcesses()
const actualProcess = ref<Process | undefined>();
const actualInstitution = ref<Institution>()
const route = useRoute()
const dashboardInstitutionData = ref<DashboardInstitution | null>(null)
  const { sectionsWithComments, fetchEvidenceData } = useEvidencesData();
let levelData: Ref<number[]> = ref([])
let levelDataAux: Ref<number[]> = ref([])
const isModalVisible = ref(false);
levelDataAux.value= [1,1,1,1,1,1]
const openModal = () => {
  isModalVisible.value = true;
};

const closeModal = () => {
  isModalVisible.value = false;
};
const isAppealable = (appeal: string): boolean => {
  return appeal == "APPEALABLE"
};


const handleFetchProcess = async () => {
  if (route.params.processID && parseInt(route.params.processID as string)) {
    const processID = parseInt(route.params.processID as string)
    const institutionID = parseInt(authData.value?.simpleInstitutionDTO?.id as unknown as string)
    actualProcess.value = await handleGetProcessByID(processID)
    actualInstitution.value = await handleGetInstitutionByManagerID(institutionID)
    return
  } else if (authData.value?.simpleInstitutionDTO) {
    actualProcess.value = await handleGetActualProcessByInstitutionID(
    authData.value!.simpleInstitutionDTO!.id
    ) ?? undefined;
    if (actualProcess.value) {
    isAppealable(actualProcess.value.status);
    }
    const apiData = await dashboardGetInstitution(authData.value!.simpleInstitutionDTO!.id) 
    if (apiData && apiData.processResult) {
      dashboardInstitutionData.value = apiData.processResult as unknown as DashboardInstitution
      levelData.value = dashboardInstitutionData.value.dimensionLevels
    } else {
      Logger.error('Invalid API response:', apiData)
    }
  }
}
const { fetchData } = useAsyncFetch(handleFetchProcess)
onMounted(async () => {
  if (authData.value && authData.value.simpleInstitutionDTO) {
    actualInstitutionID.value = authData.value.simpleInstitutionDTO.id
    await fetchEvidenceData(actualInstitutionID.value);
    try {
      await fetchData()
    } catch (error) {
      Logger.error('Error fetching data:', error)
    }
  }
})
const {buttonLabel, processStatus, buttonEnabled } = useProcessSteps(actualProcess)
</script>
<template>
  <section v-if="!actualProcess || actualProcess.status === 'UNINITIATED' || new Date(actualProcess.startDate) > new Date()" class="w-full h-[90vh] flex flex-col items-center justify-center">
    <Card class="flex flex-col gap-10 items-center mb-28 max-w-[40%] 2xl:max-w-[30%]">
      <svg fill="none" height="200" viewBox="0 0 48 48" width="200" xmlns="http://www.w3.org/2000/svg"
        class="2xl:w-64 2xl:h-64">
        <g clip-rule="evenodd" fill="#333" fill-rule="evenodd">
          <path
            d="m18.7071 4.69719c.3905-.39052 1.0237-.39052 1.4142 0l8.4853 8.48531c.3905.3905.3905 1.0237 0 1.4142-.3906.3905-1.0237.3905-1.4142 0l-8.4853-8.4853c-.3905-.39052-.3905-1.02369 0-1.41421z" />
          <path
            d="m28.7071 4.7068c.3905.39053.3905 1.02369 0 1.41422l-8.4853 8.48528c-.3905.3905-1.0237.3905-1.4142 0s-.3905-1.0237 0-1.4142l8.4853-8.4853c.3905-.39052 1.0237-.39052 1.4142 0z" />
          <path
            d="m24.3162 15.0513c-.2052-.0684-.4271-.0684-.6324 0l-14.81529 4.9376c-.22248.0741-.40551.2213-.52604.4096l-3.94442 5.0628c-.19955.2562-.26232.5932-.16835.904s.33299.5567.64102.6593l3.32253 1.1073.0027 8.6315c.00041 1.291.82662 2.4369 2.05135 2.8451l13.3818 4.4606c.1184.0473.2447.0715.3718.0714.1284.0005.256-.0237.3756-.0715l13.3812-4.4604c1.225-.4083 2.0512-1.5546 2.0513-2.8458l.0006-8.63 3.325-1.1082c.3081-.1026.5471-.3485.6411-.6593.0939-.3108.0312-.6478-.1684-.904l-3.9956-5.1286c-.119-.1567-.2843-.2785-.4802-.3438zm-14.77279 7.0599 12.80259 4.2668-2.6982 3.4633-12.8026-4.2668zm14.45909 2.7091 11.6501-3.8827-11.6526-3.8835-11.65 3.8826zm-13.8065 11.9425-.0025-7.9642 9.4925 3.1636c.402.134.8447.0001 1.1051-.3341l2.2092-2.8357-.0002 12.9589-12.1204-4.0401c-.4082-.1361-.6836-.5181-.6837-.9484zm27.6135-7.9635-9.4902 3.1629c-.4019.134-.8446.0001-1.105-.3341l-2.213-2.8405.0036 12.9638 12.1203-4.0401c.4084-.1361.6838-.5182.6838-.9486zm-9.4519 1.042-2.6993-3.4646 12.8026-4.2668 2.6993 3.4646z" />
        </g>
      </svg>

      <p class="text-center 2xl:text-lg">Su institución no tiene un proceso activo aún. Cuando tenga un proceso activo,
        los resultados aparecerán aquí.</p>

      <RouterLink :to="{ path: '/gestor/requestProcess' }">
        <Button class="2xl:text-lg 2xl:py-3 2xl:px-6">Solicitar un proceso</Button>
      </RouterLink>
    </Card>
  </section>

  <section v-else class="2xl:container 2xl:mx-auto">
    <Card class="flex flex-col gap-10 w-full shadow-md 2xl:p-8">
      <div class="flex w-full items-center justify-between">
        <div class="flex items-center gap-2 2xl:gap-4">
          <i :class="processStatus.icon + ' text-4xl 2xl:text-5xl ' + processStatus.color"></i>
          <h3 class="font-semibold text-xl 2xl:text-2xl">
            {{ processStatus.title }} #ID-{{ actualProcess?.id }}
          </h3>
        </div>

        <RouterLink v-if="buttonEnabled && actualProcess.status === 'APPEALABLE'" :to="{
          path: `/gestor/process/${actualProcess.id}/appeal`,
          query: {
            allSections: JSON.stringify(sectionsWithComments)
          }
        }">
          <Button>{{ buttonLabel }}</Button>
        </RouterLink>
      </div>
      <p class="text-sm 2xl:text-base text-black/50">
        {{ processStatus.description }}
      </p>
    </Card>

    <section class="flex flex-col w-full mt-5 2xl:mt-8 gap-10 2xl:gap-12">
      <section class="flex flex-col lg:flex-row w-full 2xl:gap-12 gap-8">
        <!-- Columna Izquierda -->
        <section class="flex flex-col gap-10 lg:w-1/2 w-full">
          <GeneralLevelCard :level="dashboardInstitutionData?.institutionLevel ?? 1"
            :institutionID="actualInstitutionID" :processId="actualProcess?.id" class="2xl:p-6" />
          <ObservationsSection class="2xl:p-6" />
        </section>

        <!-- Modal -->
        <div v-if="isModalVisible" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <ModalLevel :isVisible="isModalVisible" @close="closeModal" />
        </div>

        <!-- Columna Derecha -->
        <section class="flex flex-col gap-10 lg:w-1/2 w-full">
          <Card class="flex flex-col gap-5 h-full 2xl:p-6">
            <div class="flex justify-between items-center mb-4 2xl:mb-6">
              <h3 class="text-lg 2xl:text-xl font-semibold">Nivel por dimensión</h3>
              <a href="#" class="text-indigo-600 text-sm 2xl:text-base font-semibold" @click="openModal">
                Más información
              </a>
            </div>
            <div class="flex justify-center items-center h-full">
              <ColorBarChart v-if="levelData.length > 0" class="w-full h-full" :levelData="levelData" />
              <ColorBarChart v-else class="w-full h-full" :levelData="levelDataAux" />
            </div>
          </Card>

          <!-- KPI -->
          <section class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 2xl:gap-8 w-full">
            <InstitutionKPICard class="flex flex-col justify-between h-full 2xl:p-6"
              icon="fa-solid fa-briefcase text-green-600 2xl:text-6xl text-2xl" label="Procesos realizados"
              :value="actualInstitution?.processes.toString() || '1'">
              <RouterLink :to="{ path: '/gestor/record' }" class="text-primary-800 font-semibold 2xl:body-1 body-3">
                Ver historial
              </RouterLink>
            </InstitutionKPICard>

            <InstitutionKPICard class="flex flex-col justify-between h-full 2xl:p-6"
              icon="fa-solid fa-comment-dots text-purple-800 text-3xl 2xl:text-6xl" label="Funcionarios encuestados"
              :value="actualProcess?.nemployees.toString() || '0'">
            </InstitutionKPICard>

            <!-- TODO: Revisar por que estan hardcodeados -->
            <InstitutionKPICard class="flex flex-col justify-between h-full 2xl:p-6"
              icon="fa-solid fa-phone text-purple-800 text-2xl 2xl:text-6xl" label="Recomendaciones" :value="'6'">
              <RouterLink :to="{ path: '/gestor/recommendations' }" class="text-purple-800 font-semibold 2xl:text-lg">
                Ver más
              </RouterLink>
            </InstitutionKPICard>
          </section>
        </section>
      </section>
    </section>
  </section>
</template>