export * from './dimensionMappers';
export * from './institutionMappers';
export * from './processMappers'
export * from './auditorMappers';

export const mapRoleToHumanReadable = (role: string): string => {
    const roleMap: Record<string, string> = {
        'ADMIN': 'Administrador',
        'MANAGER': 'Gestor Institucional',
        'AUDITOR': 'Auditor',
    };

    return roleMap[role] || role;
};

