import type { Institution } from "@/models/types/index";
import type { GestorFormSchema, InstitutionBaseInfoSchema, InstitutionFormSchema, InstitutionLegalInfoFormSchema } from "@/models/validation/formSchemas";

/**
 * método para mapear una institución a un esquema de formulario
 * @param institution institución a mapear
 * @returns esquema de formulario de la institución como `InstitutionFormSchema`
 */
export const mapInstitutionToInstitutionFormSchema = (institution: Institution): InstitutionFormSchema => ({
    baseInfo: mapInstitutionToBaseInstitutionInfoSchema(institution),
    legalInfo: mapInstitutionToLegalInfoSchema(institution),
    gestorInfo: mapInstitutionToGestorInfoSchema(institution)
});

/**
 * método para mapear un esquema de formulario de institución a una institución
 * @param institutionId id de la institución
 * @param managerId id del gestor
 * @param institutionFormSchema esquema de formulario de la institución
 * @returns la institución mapeada como `Institution`
 */
export const mapInstitutionFormSchemaToInstitution = (institutionId: number, managerId: number, institutionFormSchema: InstitutionFormSchema): Institution => ({
    id: institutionId,
    level: 1,
    nemployees: 0,
    processes: [],
    nprocesses: 0,
    ...mapBaseInfo(institutionFormSchema.baseInfo),
    ...mapLegalInfo(institutionFormSchema.legalInfo),
    manager: mapGestorInfo(managerId, institutionFormSchema.gestorInfo)
});

/**
 * método para mapear una institución a un esquema de información base
 * @param institution institución a mapear
 * @returns esquema de información base de la institución como `InstitutionBaseInfoSchema`
 */
export const mapInstitutionToBaseInstitutionInfoSchema = (institution: Institution): InstitutionBaseInfoSchema => ({
    name: institution.name ?? '',
    address: institution.address ?? '',
    city: institution.city ?? '',
    region: institution.region ?? '',
    whatsapp: institution.whatsapp ?? '',
    color: institution.color ?? '',
    acronym: institution.acronym ?? '',
    email: institution.email ?? institution.manager?.email,
    password: '',
    confirmPassword: ''
});

/**
 * método para mapear una institución a un esquema de información legal
 * @param institution institución a mapear
 * @returns esquema de información legal de la institución como `InstitutionLegalInfoFormSchema`
 */
export const mapInstitutionToLegalInfoSchema = (institution: Institution): InstitutionLegalInfoFormSchema => ({
    phoneNumber: institution.phoneNumber ?? '',
    companyName: institution.name ?? '',
    website: institution.website ?? '',
    companyRut: institution.companyRut ?? ''
});

/**
 * método para mapear una institución a un esquema de información de gestor
 * @param institution institución a mapear
 * @returns esquema de información de gestor de la institución como `GestorFormSchema`
 */
export const mapInstitutionToGestorInfoSchema = (institution: Institution): GestorFormSchema => ({
    username: institution.manager?.username ?? '',
    email: institution.manager?.email ?? '',
    position: institution.manager?.position ?? '',
    rut: institution.manager?.rut ?? '',
    password: '',
    confirmPassword: '',
    phoneNumber: institution.manager?.phoneNumber ?? ''
});

const mapGestorInfo = (managerId: number, gestorInfo?: GestorFormSchema) => ({
    id: managerId,
    username: gestorInfo?.username ?? '',
    email: gestorInfo?.email ?? '',
    phoneNumber: gestorInfo?.phoneNumber ?? '',
    position: gestorInfo?.position ?? '',
    rut: gestorInfo?.rut ?? '',
    userRole: 'GESTOR',
    city: '',
    nassignedInstitutions: 0, 
    nauditsPerformed: 0, 
    createdAt: new Date(), 
    updatedAt: new Date(), 
    updatedAtts: {} 
});


const mapBaseInfo = (baseInfo?: InstitutionBaseInfoSchema) => ({
    name: baseInfo?.name ?? '',
    address: baseInfo?.address ?? '',
    city: baseInfo?.city ?? '',
    region: baseInfo?.region ?? '',
    whatsapp: baseInfo?.whatsapp ?? '',
    color: baseInfo?.color ?? '',
    acronym: baseInfo?.acronym ?? '',
    email: baseInfo?.email ?? ''
});

const mapLegalInfo = (legalInfo?: InstitutionLegalInfoFormSchema) => ({
    phoneNumber: legalInfo?.phoneNumber ?? '',
    website: legalInfo?.website ?? '',
    companyRut: legalInfo?.companyRut ?? '',
    companyName: legalInfo?.companyName ?? ''
});