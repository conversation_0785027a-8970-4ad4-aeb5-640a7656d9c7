import type { Auditor } from "@/models/types";
import type { AuditorFormSchema } from "@/models/validation/formSchemas";

export const mapAuditorToAuditorFormSchema = (auditor: Auditor): AuditorFormSchema => {
    return {
        username: auditor.username,
        email: auditor.email,
        phoneNumber: auditor.phoneNumber ?? "",
        city: auditor.city ?? "",
        acronym: auditor.acronym ?? "",
        color: auditor.color ?? "",
    }

}