import { EvidenceStatus, type Evidence } from "@/models/types";

/**
 * Map de los estados de las evidencias con su respectiva clase de icono y etiqueta
 */
export const EVIDENCE_STATUSES_MAP = {
    [EvidenceStatus.ACCEPTED]: {
        class: "fa-regular fa-circle-check text-green-500",
        label: "Aprobado"
    },
    [EvidenceStatus.ACCEPTED_WITH_COMMENT]: {
        class: "fa-solid fa-comment text-amber-500",
        label: "Aprobado con observación"
    },
    [EvidenceStatus.REJECTED]: {
        class: "fa-regular fa-circle-xmark text-red-500",
        label: "<PERSON><PERSON>zad<PERSON>"
    },
} as Record<EvidenceStatus, { class: string, label: string }>;


/**
 * mapper de evidencias subidas por el usuario
 * @param ev evidencia a mapear
 * @param parsedRequirement requerimientos de aprobación de la evidencia 
 * @returns la evidencia mapeada como `Evidence`
 */
export const mapEvidence = (ev: any, parsedRequirements: any[]): Evidence => {
    return {
        id: ev.id,
        title: ev.fileName,
        approvalRequirements: parsedRequirements.find((req: any) => req.title === ev.fileName)?.requirement || [],
        type: 'PDF',
        uploaded: true,
        evidenceStatus: (ev.evidenceStatus === 'ACCEPTED' && ev.comment) ? 'ACCEPTED_WITH_COMMENT' : ev.evidenceStatus,
        comment: ev.comment,
    };
};

/**
 * mapper de evidencias faltantes
 * @param req requerimiento de evidencia faltante
 * @returns la evidencia mapeada como `Evidence`
 */
export const mapMissingEvidence = (req: any): Evidence => {
    return {
        title: req.title,
        approvalRequirements: req.requirement,
        type: 'PDF',
        uploaded: false,
    };
};