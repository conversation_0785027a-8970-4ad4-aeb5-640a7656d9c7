
export const processStatusesMap = [
    { value: 'FINISHED', text: 'Finalizado', color: 'text-green-500' },
    { value: 'IN_PROGRESS', text: 'En Progreso', color: 'text-blue-500' },
    { value: 'UNINITIATED', text: 'Pendiente', color: 'text-red-500' }
]

export const mapProcessStatusToColor = (processStatus: string) => {
    const COLORS = {
        'UNINITIATED': 'text-red-500',
        'IN_PROGRESS': 'text-blue-500',
        'FINISHED': 'text-green-500'
    } as Record<string, string>;

    return COLORS[processStatus] ?? 'text-gray-500';
}

export const mapProcessStatusToButtonText = (processStatus: string) => {
    const BUTTON_TEXT = {
        'IN_PROGRESS': 'Seguimiento',
        'FINISHED': 'Resumen'
    } as Record<string, string>;

    return BUTTON_TEXT[processStatus] ?? null;
}