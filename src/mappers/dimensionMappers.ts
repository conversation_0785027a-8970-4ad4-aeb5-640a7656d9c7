import type { Dimension } from "@/models/types/index";
import dimensions from "@/assets/JsonData/dimensions.json";
import type { DimensionFormSchema } from "@/models/validation/formSchemas";
import { mapEvidence, mapMissingEvidence } from "./evidenceMappers";
import { Logger } from "@/services/logger";

export const mapToColor = (dimensionName: string): string => {
    const dimension = dimensions.find((d) => d.name === dimensionName);
    if (!dimension) {
        Logger.warn(`Dimension with name not found: ${dimensionName}`);
        return '';
    }
    return dimension.color ?? '';
}

/**
 * método para mapear a una dimensión.
 * @param dimension Dimensión a mapear
 * @returns la dimensión mapeada como `Dimension`
 */
export const mapToDimension = (dimension: any): Dimension => {
    const mappedDimension = {
        level: dimension.level ?? 1,
        color: dimensions.find((d) => d.name === dimension.name)?.color ?? '',
        icon: dimensions.find((d) => d.name === dimension.name)?.icon ?? '',
        ...dimension
    };
    return mappedDimension;
}

/**
 * método para mapear una dimensión a un esquema de formulario
 * @param dimension dimensión a mapear
 * @returns la dimensión mapeada como `DimensionFormSchema`
 */
export const mapDimensionToDimensionFormSchema = (dimension: Dimension): DimensionFormSchema => {
    const formattedQuestions: Record<string, string> = {};

    dimension.questions?.forEach((question, index) => {
        formattedQuestions[`question_${index + 1}`] = question.text;
    });

    const jsonQuestions = {
        title: dimension.name,
        questions: formattedQuestions
    }

    const dimensionFormSchema = {
        name: dimension.name,
        description: dimension.description,
        jsonQuestions: JSON.stringify(jsonQuestions ?? []),
        evidenceRequirements: JSON.stringify(dimension.evidences ?? []),
    };

    Logger.info(`Mapped form schema: ${JSON.stringify(dimensionFormSchema)}`);
    return dimensionFormSchema;
}

/**
 * mapper de dimensiones con evidencias
 * @param dimension dimensión a mapear
 * @param evidence evidencias de la dimensión
 * @returns la dimensión mapeada como `Dimension`
 */
export const mapToDimensionWithEvidences = (dimension: any, evidence: any[]): Dimension => {
    const parsedQuestions = parseJsonQuestions(dimension.jsonQuestions);
    const parsedRequirements = dimension.evidenceRequirements;

    const evidences = [
        ...evidence.map((ev: any) => mapEvidence(ev, parsedRequirements)),
        ...parsedRequirements
            .filter((req: any) => !evidence.some((ev: any) => ev.fileName === req.title))
            .map(mapMissingEvidence),
    ];

    const mappedDimension = {
        ...mapToDimension(dimension),
        questions: parsedQuestions,
        evidences,
    } as Dimension;

    return mappedDimension;
}

const safeJsonParse = <T>(jsonString: string | undefined, fallback: T): T => {
    try {
        return jsonString ? JSON.parse(jsonString) : fallback;
    } catch (error) {
        Logger.error('Error parsing JSON:', { error });
        return fallback;
    }
};

const extractQuestions = (parsedJson: any): Record<string, string> => {
    return parsedJson?.questions && typeof parsedJson.questions === 'object'
        ? parsedJson.questions
        : {};
};

const parseJsonQuestions = (jsonQuestions: string | undefined): any[] => {
    const parsedJson = safeJsonParse(jsonQuestions, {});
    const questions = extractQuestions(parsedJson);

    const parsedQuestions = Object.keys(questions).map((key) => ({
        id: key,
        text: questions[key],
    }));
    return parsedQuestions;
};
