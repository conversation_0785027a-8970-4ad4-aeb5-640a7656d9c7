<script setup lang="ts">

defineProps({
    decorationColor: {
        type: String,
    }
})

</script>

<template>
    <div :class="decorationColor !== undefined ? 'relative' : ''" class="bg-white rounded-2xl p-8 flex">
        <span v-if="decorationColor !== undefined" :style="`background-color: ${decorationColor}`"
            class="absolute h-3 w-full rounded-t-lg top-0 left-0 right-0"></span>
        <slot></slot>
    </div>
</template>
