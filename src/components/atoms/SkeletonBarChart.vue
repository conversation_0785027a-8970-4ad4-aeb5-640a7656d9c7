<script setup lang="ts">
</script>

<template>
  <div class="relative box-border flex justify-between">
  <div>
    <div class="skeletonHead w-64 h-4 mb-1"></div>
  </div>

  <div class="flex flex-col items-end">
    <div class="flex items-center space-x-2">
      <div class="skeletonHead 2xl:w-40 w-32 h-4 mb-1"></div> 
    </div>
  </div>
</div>

  <div class="flex justify-start items-center gap-28 mt-6 mb-6">
    <div class="flex flex-col items-center">
      <div class="flex items-center space-x-2 mb-2">
        <span class="skeletonHead 2xl:w-32 w-24 h-3 rounded-full"></span>
        <p class="skeleton 2xl:w-96 w-64 h-3"></p>
      </div>
      <div class="flex items-center space-x-2 mb-2">
        <span class="skeletonHead 2xl:w-32 w-24 h-3 rounded-full"></span>
      <p class="skeleton 2xl:w-96 w-64 h-3"></p>
      </div>
    </div>
  </div>

  <div class="flex justify-between mt-5 items-end w-full h-[50%] px-4 gap-2">
  <div class="skeletonChart w-12 2xl:h-56 h-44 "></div>
  <div class="skeletonChart w-12 2xl:h-36 h-20 "></div>
  <div class="skeletonChart w-12 2xl:h-44 h-28 "></div>
  <div class="skeletonChart w-12 2xl:h-48 h-14 "></div>
  <div class="skeletonChart w-12 2xl:h-20 h-20 "></div>
  <div class="skeletonChart w-12 2xl:h-28 h-10 "></div>
  <div class="skeletonChart w-12 2xl:h-36 h-8 "></div>
  <div class="skeletonChart w-12 2xl:h-14 h-2 "></div>
  <div class="skeletonChart w-12 2xl:h-40 h-5 "></div>
</div>

  
  <div class="flex justify-end 2xl:mt-20 mt-5 space-x-2">
    <span class="skeleton w-3 h-3"></span>
    <p class="skeleton w-[25%] h-3"></p>
  </div>
</template>

<style scoped>
.skeleton {
  @apply bg-gray-300 rounded-full animate-pulse;
}
.skeletonHead {
  @apply bg-gray-400 rounded-full animate-pulse;
}
.skeletonChart{
  @apply bg-gray-400 animate-pulse;
}
</style>
