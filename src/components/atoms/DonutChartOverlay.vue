<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
    percentage: { type: Number, required: true },
    color: { type: String, default: '#1e3a8a' },
    size: { type: Number, default: 100 },
    title: { type: String, default: 'Chart Title' },
    centerText: { type: String, default: null }
});

const strokeWidth = 14;
const backgroundStrokeWidth = 9.5;
const radius = 16.955;
const circumference = 2 * Math.PI * radius;

const strokeDashoffset = computed(() => {
    const mainCircleLength = circumference * props.percentage / 100;
    const offset = circumference - mainCircleLength;
    return offset;
});

const gradientStart = props.color;
const gradientEnd = props.color + '80';

const getGradientX2 = () => {
    const angle = (props.percentage / 100) * 360;
    const x = Math.sin(angle * (Math.PI / 180)) * 100 + 50;
    return `${x}%`;
};

const getGradientY2 = () => {
    const angle = (props.percentage / 100) * 360;
    const y = Math.cos(angle * (Math.PI / 180)) * 100 + 50;
    return `${y}%`;
};
</script>

<template>
    <div class="flex flex-col justify-center items-center">
        <!-- main chart -->
        <svg :width="size" :height="size" viewBox="-2 -3 43 43" class="block max-w-[90%] max-h-[250px]">
            <defs>
                <linearGradient id="mainGradient" x1="70%" y1="0%" :x2="getGradientX2()" :y2="getGradientY2()">
                    <stop offset="0%" :stop-color="gradientStart" />
                    <stop offset="100%" :stop-color="gradientEnd" />
                </linearGradient>
            </defs>
            <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                <feOffset result="offOut" in="SourceGraphic" dx="1" dy="1" />
                <feColorMatrix result="matrixOut" in="offOut" type="matrix"
                    values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0   0 0 0 0.5 0" />
                <feGaussianBlur result="blurOut" in="matrixOut" stdDeviation="2" />
                <feBlend in="SourceGraphic" in2="blurOut" mode="normal" />
            </filter>
            <!-- background circle -->
            <path class="stroke-[7px] fill-none" d="M18 2.0845
             a 15.9155 15.9155 0 0 1 0 31.831
             a 15.9155 15.9155 0 0 1 0 -31.831" :stroke-width="backgroundStrokeWidth" stroke="#f59e0b" />
            <!-- main circle -->
            <path class="fill-none rounded-full circle" stroke="#1e3a8a" d="M18 2.0845
             a 15.9155 15.9155 0 0 1 0 31.831
             a 15.9155 15.9155 0 0 1 0 -31.831" :stroke-width="strokeWidth" :stroke-dasharray="circumference"
                :stroke-dashoffset="strokeDashoffset" />
            <!-- inner transparent circle -->
            <circle cx="18" cy="18" class="rounded-lg shadow-lg" :r="radius - backgroundStrokeWidth / 2" fill="#fff" />
            <!-- percentage text -->
            <text x="18" y="18" class="fill-black text-[0.45rem] font-semibold text-center" text-anchor="middle"
                dominant-baseline="middle">
                {{ centerText ?? percentage.toString() + '%' }}
            </text>
        </svg>
        <div v-if="title" class="text-black font-bold text-xl">{{ title }}</div>
    </div>
</template>
