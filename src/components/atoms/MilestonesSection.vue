<script setup lang="ts">
import Card from './Card.vue';

defineProps({
  milestones: {
    type: Array as () => string[],
    required: true,
  },
})

</script>


<template>
  <Card class="flex flex-col gap-4 flex-1 max-w-[35%] max-h-[80vh]">
    <h3 class="text-center font-extrabold text-xl">
      Hitos
    </h3>
    <ul class="flex flex-col gap-6 overflow-y-auto">
      <li v-for="milestone in milestones" :key="milestone"
        class="flex gap-4 items-center cursor-pointer mb-2 h-20 transition-colors duration-300 ease-in-out border-b pb-4">

        <div class="flex w-12 h-12 items-center justify-center bg-green-600 rounded-md">
          <i class="fa-solid fa-clipboard-list text-white text-3xl"></i>
        </div>
        <div class="flex flex-col gap-2">
          <span> {{ milestone }}</span>
          <!-- se debe reemplazar por el timestamp de los milestones
          cuando estos lo tengan -->
          <span class="text-sm font-semibold text-black/40">21-06-2024</span>
        </div>

      </li>
    </ul>
  </Card>
</template>
