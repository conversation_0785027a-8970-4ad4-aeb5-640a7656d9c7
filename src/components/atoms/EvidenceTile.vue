<script setup lang="ts">
import { EVIDENCE_STATUSES_MAP } from '@/mappers/evidenceMappers';
import { EvidenceStatus, type Evidence } from '@/models/types';
import { ref, h, computed } from 'vue';

const props = defineProps({
    auditorMode: {
        type: Boolean,
        required: false,
        default: false
    },
    onEvidenceUpload: {
        type: Function,
        required: false
    },
    evidence: {
        type: Object as () => Evidence,
        required: true
    }

})

const isEvidenceModalOpen = ref(false);

const openModal = () => {
    isEvidenceModalOpen.value = true;
}

const getIcon = () => {
    if (props.evidence.evidenceStatus) {
        const iconConfig = EVIDENCE_STATUSES_MAP[props.evidence.evidenceStatus];
        if (iconConfig) {
            return h('i', { class: iconConfig.class });
        }
    }
}

const shouldFill = () => {
    return props.evidence?.uploaded && !props.auditorMode;
}

const buttonText = computed(() => {
    if (props.auditorMode && props.evidence.evidenceStatus !== EvidenceStatus.NOT_EVALUATED) {
        return ''
    } else if (props.auditorMode) {
        return 'Evaluar'
    }
    else
        return props.evidence?.uploaded ? 'Ver' : 'Subir';
})

</script>

<template>
    <div @click="openModal" :class="`${shouldFill() ? 'bg-primary-600 text-white' : 'bg-white text-primary-800'}`"
        class="flex items-center gap-5 py-4 px-4 rounded-xl border border-primary-700 cursor-pointer
    hover:bg-primary-900 hover:text-white transition-all duration-300 group relative">
        <div v-if="evidence.evidenceStatus" class="absolute top-0.5 right-1">
            <component class="text-lg" :is="getIcon()" />
        </div>
        <span class="px-4 py-3 rounded-xl flex items-center justify-center bg-primary-100">
            <i class="fa-solid fa-file-arrow-up text-primary-800 2xl:text-2xl xl:text-xl"></i>
        </span>
        <div class="flex flex-col gap-2">
            <h3 class="font-semibold 2xl:text-base xl:text-sm"> {{ evidence.title }}</h3>
            <p v-if="onEvidenceUpload && buttonText"
                :class="`${evidence?.uploaded} ? 'text-white' : 'text-primary-900'`"
                class="font-semibold group-hover:text-white 2xl:text-sm xl:text-sm">
                {{ buttonText }}
            </p>
        </div>
    </div>
    <slot name="evidence-modal" :isEvidenceModalOpen="isEvidenceModalOpen"
        :onModalClose="() => { isEvidenceModalOpen = false }" :evidence="evidence">
    </slot>
</template>