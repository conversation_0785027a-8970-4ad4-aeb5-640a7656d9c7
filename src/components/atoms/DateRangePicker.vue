<script setup lang="ts">
import { addDaysToDate, parseStringToDate } from '@/helpers/dateHelpers';
import { formatDateToString, formatDateToYYYYMMDD } from '@/helpers/formatters';
import { ref, watch, computed } from 'vue';

const props = defineProps({
    modelValueStart: {
        type: String,
        default: null
    },
    modelValueEnd: {
        type: String,
        default: null
    },
    minDate: {
        type: String,
        default: null
    }
});

const emit = defineEmits(['update:modelValueStart', 'update:modelValueEnd']);

const startDate = ref(props.modelValueStart || '');
const today = new Date();
today.setHours(0, 0, 0, 0);
const computedMinDate = computed(() => {
    return formatDateToYYYYMMDD(props.minDate) || formatDateToString(today);
});
const minStartDate = computedMinDate.value;
const minEndDate = computed(() => {
    if (!startDate.value) return '';
    return formatDateToString(addDaysToDate(parseStringToDate(startDate.value), 7));
});

const endDate = ref(props.modelValueEnd || '');

watch(startDate, (newValue) => {
    emit('update:modelValueStart', newValue);
    const minEnd = addDaysToDate(parseStringToDate(newValue), 7);
    if (!endDate.value || parseStringToDate(endDate.value) < minEnd) {
        endDate.value = formatDateToString(minEnd);
        emit('update:modelValueEnd', endDate.value);
    }
});

watch(endDate, (newValue) => {
    emit('update:modelValueEnd', newValue);
});
</script>

<template>
    <div class="flex items-center gap-6">
        <div class="flex-1 flex flex-col gap-3">
            <span>Fecha de inicio</span>
            <input type="date" class="mt-1 p-2 border rounded w-full" v-model="startDate" :min="minStartDate" />
        </div>
        <div class="flex-1 flex flex-col gap-3">
            <span>Fecha de término</span>
            <input
                type="date"
                class="mt-1 p-2 border rounded w-full"
                v-model="endDate"
                :min="minEndDate"
            />
        </div>
    </div>
</template>
