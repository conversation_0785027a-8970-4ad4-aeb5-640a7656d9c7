<!-- Componente que indica la cantidad de estrellas en base a una puntuación-->
<template>
    <span class="text-primary-900 justify-center text-center text-5xl">
      {{ stars }}
    </span>
  </template>
  
  <script setup lang="ts">
  import { computed, defineProps } from 'vue'
  const props = defineProps<{ level: number }>()
  
  const stars = computed(() => {
    const starLevel = Math.max(0, Math.min(props.level, 5))
    return '★'.repeat(starLevel) + '☆'.repeat(5 - starLevel)
  })
  </script>
  