<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
    direction: {
        type: String,
        default: 'horizontal',
        validator: (value: string) => ['horizontal', 'vertical'].includes(value),
    },
});

const dividerClass = computed(() => {
    return props.direction === 'vertical'
        ? 'w-1 h-full bg-gray-200 rounded-full'
        : 'w-full h-1 bg-gray-200 rounded-full';
});

</script>

<template>
    <div :class="dividerClass"> </div>
</template>