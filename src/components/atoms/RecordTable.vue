<template>
    <div class="container mx-auto p-4">
      <div v-if="sortedItems.length === 0" class="text-center text-gray-500 mt-4">Sin Resultados</div>
      <div v-else>
        <table class="min-w-full bg-white text-center border border-gray-200 table-auto overflow-x-auto mt-4 rounded-lg">
          <!-- Table header -->
          <thead>
            <tr>
              <th class="py-2 px-4 border-b">Estado</th>
              <th class="py-2 px-4 border-b">ID</th>
              <th class="py-2 px-4 border-b">Inicio</th>
              <th class="py-2 px-4 border-b">Termino</th>
              <th class="py-2 px-4 border-b">Funcionarios</th>
              <th class="py-2 px-4 border-b">% de Respuestas</th>
              <th class="py-2 px-4 border-b">Nivel General Alcanzado</th>
              <th class="py-2 px-4 border-b">Acción</th>
            </tr>
          </thead>
          <!-- Table body -->
          <tbody>
            <tr v-for="item in sortedItems" :key="item.id">
              <td class="py-2 px-4 border-b">
                <span v-if="item.estado === 'pendiente'">
                    <i class="fa-regular fa-circle-check text-blue-500 text-2xl"></i>
                </span>
                <span v-else-if="item.estado === 'en curso'">
                    <i class="fa-regular fa-circle-check text-orange-500 text-2xl"></i>
                </span>
                <span v-else-if="item.estado === 'completado'">
                    <i class="fa-regular fa-circle-check text-green-500 text-2xl"></i>
                </span>
              </td>
              <td class="py-2 px-4 border-b font-bold">{{ item.id }}</td>
              <td class="py-2 px-4 border-b">{{ item.fecha_inicio }}</td>
              <td class="py-2 px-4 border-b">{{ item.fecha_termino }}</td>
              <td class="py-2 px-4 border-b">{{ item.funcionario }}</td>
              <td class="py-2 px-4 border-b font-bold">{{ item.respuestas }} %</td>
              <td class="py-2 px-4 border-b font-bold">{{ item.nivel_general_alcanzado }}</td>
              <td class="py-2 px-4 border-b">
                <button class="rounded-full px-4 py-2 bg-purple-200 text-primary-950 hover:bg-primary-950 hover:text-white">
                  Seguimiento
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import jsonData from '@/assets/JsonData/ProcessData.json';
  
  const sortedItems = jsonData.sort((a, b) => a.id - b.id);
  </script>
  
  <style scoped>
 table {
    border-radius: 20px;
  }
  
  th, td {
    border-radius: 20;
  }
  
  </style>
  