<script setup lang="ts">
import { ref, defineProps } from 'vue';
import type { Question } from '@/models/types';

const accordionOpen = ref<boolean>(false);
const activeIndex = ref<number | null>(null);

const props = defineProps({
  questions: {
    type: Array as () => Question[],
    required: true
  }
});

const toggleAccordion = () => {
  accordionOpen.value = !accordionOpen.value;
  activeIndex.value = null;
};

const toggleAnswer = (index: number) => {
  activeIndex.value = (activeIndex.value === index) ? null : index;
};
</script>

<template>
  <div class="accordion">
    <button @click="toggleAccordion"
      class="py-4 cursor-pointer flex items-center hover:text-primary-800 transition-all duration-200">
      {{ accordionOpen ? '- Preguntas' : '+ Preguntas' }}
    </button>
    <div v-show="accordionOpen" class="flex flex-col gap-3 h-full overflow-y-auto">
      <div v-for="(question, index) in questions" :key="index" class="">
        <div class="flex w-full items-center gap-3 opacity-50 hover:opacity-100 transition-opacity duration-300 py-4">
          <i :class="{ 'fa-solid fa-minus': activeIndex === index, 'fa-solid fa-circle': activeIndex !== index }"
            class="fa-solid px-4" style="font-size: 7px; margin-right: 5px;"></i>
          <p @click="toggleAnswer(index)" class="cursor-pointer question-text">
            {{ question.text }}
          </p>
        </div>
        <div v-if="activeIndex === index" class="answer">
          <div>
            <table>
              <th></th>
              <tbody>
                <tr v-for="(answer, answerIndex) in question.answer" :key="answerIndex" class="answer-item">
                  <td class="py-3 px-12">{{ answer }}</td>
                  <td class="py-3 px-12">{{ 10 }}%</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
