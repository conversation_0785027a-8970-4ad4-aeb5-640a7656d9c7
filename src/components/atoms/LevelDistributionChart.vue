<template>
  <div class="flex flex-col justify-center items-center">
    <!-- main chart -->
    <svg :width="170" :height="size" viewBox="0 0 36 36" class="block max-w-[80%] max-h-[250px]">

      <defs>
        <linearGradient id="mainGradient" x1="70%" y1="0%" :x2="getGradientX2()" :y2="getGradientY2()">
          <stop offset="0%" :stop-color="mainChart.gradientStart" />
          <stop offset="100%" :stop-color="mainChart.gradientEnd" />
        </linearGradient>
      </defs>

      <!-- background circle -->
      <path class="stroke-[2.5px] fill-none" d="M18 2.0845
           a 15.9155 15.9155 0 0 1 0 31.831
           a 15.9155 15.9155 0 0 1 0 -31.831" stroke-width="2" />

      <!-- main circle -->
      <path class="fill-none stroke-[2.5px] circle" stroke="url(#mainGradient)" stroke-width="2" d="M18 2.0845
           a 15.9155 15.9155 0 0 1 0 31.831
           a 15.9155 15.9155 0 0 1 0 -31.831" :stroke-dasharray="mainChart.circumference"
        :stroke-dashoffset="mainChart.strokeDashoffset" />

      <!-- center circle   -->
      <circle cx="18" cy="18" r="13" :fill="mainChart.color" />

      <!-- percentage text -->
      <text x="18" y="22.35" class=" fill-white text-sm text-center" text-anchor="middle">
        {{ mainChart.level }}</text>
    </svg>
    <div class="text-black font-bold text-xl">NIVEL GENERAL DE INSTITUCIONES</div>

    <!-- mini charts -->
    <div class="flex space-x-2 mt-4">
      <div v-for="(miniChart, index) in miniCharts" :key="index" class="flex flex-col items-center select-none"
        @click="handleMiniChartClick(index)">
        <p class="text-sm font-semibold text-black/40 transition-all duration-300"
          :style="`color: ${isMinichartSelected(index) ? miniChart.color : ''}`"> {{
            miniChart.ninstitutions }}</p>
        <svg width="100" height="100" viewBox="0 0 36 36" class="block max-w-[80%] max-h-[250px] cursor-pointer">
          <path class="circle-bg" d="M18 2.0845
               a 15.9155 15.9155 0 0 1 0 31.831
               a 15.9155 15.9155 0 0 1 0 -31.831" stroke="#eee" stroke-width="3.8" />
          <path class="circle" :stroke="miniChart.color" stroke-width="3.8" d="M18 2.0845
               a 15.9155 15.9155 0 0 1 0 31.831
               a 15.9155 15.9155 0 0 1 0 -31.831" :stroke-dasharray="miniChart.circumference"
            :stroke-dashoffset="miniChart.strokeDashoffset" />
          <text x="18" y="20.35" class="percentage fill-black" text-anchor="middle">{{ miniChart.percentage }}%</text>
        </svg>
        <div class="text-center mt-2 text-xs font-semibold">Nivel {{ miniChart.level }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { LevelPercentage } from '@/models/types';
import { onMounted, reactive, ref, toRefs, watch } from 'vue';

const COLORS_MAP = {
  1: '#40189D',
  2: '#FFA14A',
  3: '#36E266',
  4: '#F55A5A',
  5: '#3666E2',
} as Record<number, string>;

const props = defineProps({
  level: { type: Number, required: true },
  levelDistribution: { type: Array as () => LevelPercentage[], required: true },
  onMiniChartClick: { type: Function, required: true },
  size: { type: Number, default: 100 },
});

const color = ref(COLORS_MAP[props.level]);
const { size, level } = toRefs(props);


const mainChart = reactive({
  percentage: 0,
  color: color.value,
  level: props.level,
  get circumference() { return 100; },
  get strokeDashoffset() { return (100 - this.percentage) / 100 * this.circumference; },
  gradientStart: color.value,
  gradientMiddle: color.value + 'B3',
  gradientEnd: color.value + '80'
});

const handleLevelChange = (level: number) => {
  mainChart.level = level;
  color.value = COLORS_MAP[level];
  mainChart.percentage = props.levelDistribution.find(chart => chart.level === level)?.percentage || 0;
  mainChart.color = COLORS_MAP[level];
  mainChart.gradientStart = COLORS_MAP[level];
  mainChart.gradientMiddle = COLORS_MAP[level] + 'B3';
  mainChart.gradientEnd = COLORS_MAP[level] + '80';
};

onMounted(() => handleLevelChange(level.value));

watch(level, handleLevelChange);


const getGradientX2 = () => {
  const angle = (mainChart.percentage / 100) * 360;
  const x = Math.sin(angle * (Math.PI / 180)) * 100 + 50;
  return `${x}%`;
};

const getGradientY2 = () => {
  const angle = (mainChart.percentage / 100) * 360;
  const y = Math.cos(angle * (Math.PI / 180)) * 100 + 50;
  return `${y}%`;
};

const isMinichartSelected = (index: number) => mainChart.level === miniCharts.value[index].level;

const miniCharts = ref(props.levelDistribution.map(chart => ({
  ...chart,
  color: COLORS_MAP[chart.level],
  circumference: 100,
  strokeDashoffset: (100 - chart.percentage) / 100 * 100
})));

const handleMiniChartClick = (index: number) => {
  const selectedChart = miniCharts.value[index];
  handleLevelChange(selectedChart.level);
  props.onMiniChartClick(selectedChart.level);
};

</script>

<style scoped>
.circle-bg {
  fill: none;
  stroke-width: 3.8;
}

.circle {
  fill: none;
  stroke-linecap: flat;
  transition: stroke-dashoffset 0.5s;
}

.percentage {
  font-size: 0.5em;
}

.flex {
  display: flex;
}

.space-x-2>*+* {
  margin-left: 0.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.text-center {
  text-align: center;
}

.text-xs {
  font-size: 0.75rem;
}

.font-semibold {
  font-weight: 600;
}
</style>