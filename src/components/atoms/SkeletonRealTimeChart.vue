<script setup lang="ts">
</script>

<template>
  <div class="relative box-border flex justify-between mb-6">
    <div>
      <div class="skeletonHead w-64 h-4 mb-1"></div>
    </div>
  </div>

  <div class="flex justify-start items-center 2xl:gap-28 gap-10 mb-6">
    <div class="flex flex-col items-center">
      <div class="flex items-center space-x-2 mb-2">
        <span class="skeletonHead 2xl:w-3 w-2 2xl:h-3 h-2 rounded-full"></span>
        <p class="skeletonHead 2xl:w-32 w-24 2xl:h-3 h-3"></p>
      </div>
      <p class="skeleton 2xl:w-32 w-24 2xl:h-3 h-3"></p>
    </div>

    <div class="flex flex-col items-center">
      <div class="flex items-center space-x-2 mb-2">
        <span class="skeletonHead 2xl:w-3 w-2 2xl:h-3 h-2 rounded-full"></span>
        <p class="skeletonHead 2xl:w-32 w-24 2xl:h-3 h-3"></p>
      </div>
      <p class="skeleton 2xl:w-32 w-24 2xl:h-3 h-3"></p>
    </div>

    <div class="flex flex-col items-center">
      <div class="flex items-center space-x-2 mb-2">
        <span class="skeletonHead 2xl:w-3 w-2 2xl:h-3 h-2 rounded-full"></span>
        <p class="skeletonHead 2xl:w-32 w-24 2xl:h-3 h-3"></p>
      </div>
      <p class="skeleton 2xl:w-32 w-24 2xl:h-3 h-3"></p>
    </div>
  </div>

  <div class="flex justify-center items-center mb-8">
    <div class="bg-gray-300 animate-pulse w-[80%] h-24 2xl:w-[100%] 2xl:h-56"></div>
  </div>

  <div class="flex flex-col items-start space-y-2">
    <div class="skeleton w-[100%] h-3"></div>
    <div class="skeleton w-[100%] h-3"></div>
    <div class="skeleton w-[75%] h-3"></div>
  </div>
</template>

<style scoped>
.skeleton {
  @apply bg-gray-300 rounded-full animate-pulse;
}
.skeletonHead {
  @apply bg-gray-400 rounded-full animate-pulse;
}
</style>
