<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
    percentage: { type: Number, required: true },
    color: { type: String, default: '#40189D' },
    size: { type: Number, default: 100 },
    title: { type: String, default: 'Chart Title' },
    centerText: { type: String, default: null },
    centerTextSize: { type: String, default: '18' }
});

const circumference = 100;
const strokeDashoffset = computed(() => (100 - props.percentage) / 100 * circumference);

const gradientStart = props.color;
const gradientEnd = props.color + '80';

const getGradientX2 = () => {
    const angle = (props.percentage / 100) * 360;
    const x = Math.sin(angle * (Math.PI / 180)) * 100 + 50;
    return `${x}%`;
};

const getGradientY2 = () => {
    const angle = (props.percentage / 100) * 360;
    const y = Math.cos(angle * (Math.PI / 180)) * 100 + 50;
    return `${y}%`;
};
</script>

<template>
    <div class="flex flex-col justify-center items-center">
        <!-- main chart -->
        <svg :width="170" :height="size" viewBox="0 0 36 36" class="block max-w-[80%] max-h-[250px]">
            <defs>
                <linearGradient id="mainGradient" x1="70%" y1="0%" :x2="getGradientX2()" :y2="getGradientY2()">
                    <stop offset="0%" :stop-color="gradientStart" />
                    <stop offset="100%" :stop-color="gradientEnd" />
                </linearGradient>
            </defs>
            <!-- background circle -->
            <path class="stroke-[2.5px] fill-none" d="M18 2.0845
             a 15.9155 15.9155 0 0 1 0 31.831
             a 15.9155 15.9155 0 0 1 0 -31.831" stroke-width="2" />
            <!-- main circle -->
            <path class="fill-none stroke-[2.5px] circle" stroke="url(#mainGradient)" stroke-width="2" d="M18 2.0845
             a 15.9155 15.9155 0 0 1 0 31.831
             a 15.9155 15.9155 0 0 1 0 -31.831" :stroke-dasharray="circumference"
                :stroke-dashoffset="strokeDashoffset" />
            <!-- center circle -->
            <circle cx="18" cy="18" r="13" :fill="color" />
            <!-- percentage text -->
            <text x="50%" y="50%" :class="`fill-white text-sm font-semibold`" :style="{ fontSize: centerTextSize }"
                text-anchor="middle" dominant-baseline="central">
                {{ centerText ?? percentage.toString() + '%' }}
            </text>
        </svg>
        <div class="text-black font-bold text-xl">{{ title }}</div>
    </div>
</template>
