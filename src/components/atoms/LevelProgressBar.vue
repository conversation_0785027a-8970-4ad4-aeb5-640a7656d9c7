<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
    level: { type: Number, required: true, validator: (value: number) => value >= 0 && value <= 5 },
    label: { type: String, default: 'Nivel' },
});

const percentage = computed(() => (props.level / 5) * 100);

</script>

<template>
    <div class="flex w-full flex-col gap-1.5">
        <div class="progress-level-bar w-full h-1.5 bg-gray-200 rounded-md overflow-hidden">
            <div class="progress-bar h-full rounded-md transition-all duration-300 bg-blue-900"
                :style="{ width: `${percentage}%` }">
            </div>
        </div>
        <span class="flex w-full justify-between font-semibold text-xs">
            <span>{{ props.label }}</span>
            <span>{{ props.level }}</span>
        </span>
    </div>
</template>
