<template>
    <div class="relative box-border flex justify-between">
  <div>
    <div class="skeletonHead w-64 h-4 mb-1"></div>
    <div class="skeleton w-32 h-2"></div>
  </div>

  <div class="flex flex-col items-end">
    <div class="flex items-center space-x-2">
        <div class="skeletonHead w-3 h-3 mb-1"></div> 
      <div class="skeletonHead w-40 h-3 mb-1"></div> 
    </div>
    <div class="flex flex-col">
      <div class="skeleton w-40 h-3"></div>
    </div>
  </div>
</div>
    <div class="relative flex justify-center h-[65%] 2xl:mt-10 mt-10">
        <div class="w-[70%] h-[80%] bg-gray-300 rounded-t-full animate-pulse"></div>
      <div class="absolute inset-0 flex flex-col justify-center items-center">
      </div>
      <div class="absolute bottom-0 justify-center left-0 w-full flex flex-col items-center">
  <div class="skeleton text-center w-[60%] h-4 mt-10 2xl:mb-2"></div>
  <div class="skeleton text-center w-[50%] 2xl:mt-0 mt-1 h-3"></div> 
</div>
</div>
  </template>
  
  <script setup lang="ts">
  </script>
  <style scoped>
  .skeleton {
    @apply bg-gray-300 rounded-full animate-pulse;
  }
  .skeletonHead{
    @apply bg-gray-400 rounded-full animate-pulse;
  }
  </style>