<script setup lang="ts">
import Card from './Card.vue';
import { fetchInstitutionByID } from '@/services/api/institutionService';
import { fetchProcessByID } from '@/services/api/processService';
import { ref, onMounted } from 'vue';
import type { Institution } from '@/models/types';

const props = defineProps({
  processID: {
    type: Number,
    required: true,
  },
  startDate: {
    type: String,
    required: true,
  },
  finishDate: {
    type: String,
    required: true,
  }
});

const institutionData = ref<Institution>();

const processData = ref({
  startDate: '',
  endDate: '',
});

const formatDateToString = (date: string): string => {
  const correctedDate = date.replace(' ', '+');

  const formattedDate = new Date(correctedDate);
  
  if (isNaN(formattedDate.getTime())) {
    return '';
  }

  const year = formattedDate.getFullYear();
  const month = String(formattedDate.getMonth() + 1).padStart(2, '0');
  const day = String(formattedDate.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};


const handleFetchData = async () => {
  const process = await fetchProcessByID(props.processID);
  institutionData.value = await fetchInstitutionByID(process.institutionId);
  
  processData.value.startDate = formatDateToString(props.startDate);
  processData.value.endDate = formatDateToString(props.finishDate);
};
onMounted(async () => {
  await handleFetchData();
});
</script>

<template>
  <Card v-if="institutionData" class="flex flex-col gap-5">
    <form class="bg-white rounded">
      <!-- General -->
      <div class="mb-4">
        <h2 class="text-lg font-bold">General</h2>
        <div class="grid grid-cols-3 gap-4">
          <!-- Primer grupo de campos -->
          <div class="mb-4 py-6">
            <label class="text-gray-700 text-sm font-bold mb-2">Nombre de la institución
              <input v-model="institutionData.name" type="text"
                class="shadow appearance-none border-b w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mt-2"
                readonly>
            </label>
          </div>
          <div class="mb-4 py-6">
            <label class="text-gray-700 text-sm font-bold mb-2">Funcionarios
              <input v-model="institutionData.nemployees" type="text"
                class="shadow appearance-none border-b w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mt-2"
                readonly>
            </label>
          </div>
          <div class="mb-4 py-6">
            <label class="text-gray-700 text-sm font-bold mb-2">Procesos realizados
              <input v-model="institutionData.nprocesses" type="text"
                class="shadow appearance-none border-b w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mt-2"
                readonly>
            </label>
          </div>

          <!-- Segundo grupo de campos (debajo del primero) -->
          <div class="mb-4 py-6">
            <label class="text-gray-700 text-sm font-bold">Región
              <input v-model="institutionData.region" type="text"
                class="shadow appearance-none border-b w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mt-2"
                readonly>
            </label>
          </div>
          <div class="mb-4 py-6">
            <label class="text-gray-700 text-sm font-bold mb-2">Ciudad
              <input v-model="institutionData.city" type="text"
                class="shadow appearance-none border-b w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mt-2"
                readonly>
            </label>
          </div>
          <div class="mb-4 py-6">
            <label class="text-gray-700 text-sm font-bold mb-2">Nivel general
              <input v-model="institutionData.level" type="text"
                class="shadow appearance-none border-b w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mt-2"
                readonly>
            </label>
          </div>
        </div>
      </div>

      <!-- Contacto -->
      <div class="mb-4 py-6">
        <h2 class="text-lg font-bold mb-2">Contacto</h2>
        <div class="grid grid-cols-3 gap-4">
          <!-- Tercer grupo de campos -->
          <div class="mb-4 py-6">
            <label class="block text-gray-700 text-sm font-bold mb-2"> <i
                class="fa-solid fa-phone text-xl px-4 text-customPurple"></i>Teléfono
              <input v-model="institutionData.phoneNumber" type="text"
                class="shadow appearance-none border-b w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mt-2"
                readonly>
            </label>
          </div>
          <div class="mb-4 py-6">
            <label class="block text-gray-700 text-sm font-bold mb-2"><i
                class="fa-brands fa-whatsapp text-xl px-4 text-customPurple"></i>Whatsapp
              <input v-model="institutionData.whatsapp" type="text"
                class="shadow appearance-none border-b w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mt-2"
                readonly>
            </label>
          </div>
          <div class="mb-4 py-6">
            <label class="block text-gray-700 text-sm font-bold mb-2"><i
                class="fa-solid fa-envelope text-xl px-4 text-customPurple"></i>Email
              <input v-if="institutionData.manager" v-model="institutionData.manager.email" type="text"
                class="shadow appearance-none border-b w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mt-2"
                readonly>
            </label>
          </div>
        </div>
      </div>

      <!-- Proceso -->
      <div class="mb-4 py-6">
        <h2 class="text-lg font-bold mb-2">Proceso</h2>
        <div class="grid grid-cols-3 gap-4">
          <!-- Cuarto grupo de campos -->
          <div class="mb-4 py-6">
            <label class="block text-gray-700 text-sm font-bold mb-2">Fecha inicio
              <input v-model="processData.startDate" type="date"
                class="shadow appearance-none border-b w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mt-2"
                readonly>
            </label>
          </div>
          <div class="mb-4 py-6">
            <label class="block text-gray-700 text-sm font-bold mb-2">Fecha término
              <input v-model="processData.endDate" type="date"
                class="shadow appearance-none border-b w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mt-2"
                readonly>
            </label>
          </div>
        </div>
      </div>
    </form>
  </Card>
</template>
