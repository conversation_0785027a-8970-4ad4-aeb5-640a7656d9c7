<script setup lang="ts">
import type { KPICard } from '@/models/types/index';
import { fetchDashboardKPIStats } from '@/services/api/adminService';
import { Logger } from '@/services/logger';
import { ref } from 'vue';

const dashboardKPIStats = ref<KPICard[]>();
const loading = ref<boolean>(true);

const handleFetchDashboardStats = async () => {
    loading.value = true;
    try {
        const response = await fetchDashboardKPIStats();
        dashboardKPIStats.value = response;
        Logger.debug("Data successfully obtained", response);
    } catch (error: any) {
        Logger.error("Error getting dashboard data", error);
        dashboardKPIStats.value = [
            { title: "Instituciones", value: "0", icon: "fas fa-building", color: "#4E36E2" },
            { title: "Auditores", value: "0", icon: "fas fa-user-tie", color: "#48A9F8" },
            { title: "Auditorias Realizadas", value: "0", icon: "fas fa-clipboard-check", color: "#2AA88A" },
            { title: "Procesos en curso", value: "0", icon: "fas fa-spinner", color: "#8BC740" },
            { title: "Personas Encuestadas", value: "0", icon: "fas fa-people-group", color: "#3666E2" },
        ];
    } finally {
        loading.value = false;
        Logger.info("KPI dashboard fetch completed");
    }
};

await handleFetchDashboardStats();
</script>

<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
    <div v-for="kpi in dashboardKPIStats" :key="kpi.title" :style="`background-color: ${kpi.color}`"
      class="rounded-xl shadow lg:py-4 py-4 px-6 flex items-center">
      <i :class="kpi.icon + ' text-4xl mr-4 text-white'"></i>
      <div class="flex-1 flex flex-col gap-y-4 text-right text-white">
        <h2 class="4xl:text-lg 3xl:text-base 2xl:text-sm xl:text-sm text-pretty">{{ kpi.title }}</h2>
        <p class="text-4xl font-bold">{{ kpi.value }}</p>
      </div>
    </div>
  </div>
</template>