<script setup lang="ts">
import { watch, ref, onMounted, onUnmounted, computed } from 'vue';

const props = defineProps({
    type: {
        type: String,
        default: 'text',
    },
    placeholder: {
        type: String,
        required: false,
    },
    errorMessage: String,
    modelValue: {
        type: String,
        required: false,
    },
    formatter: {
        type: Function,
        default: (value: string) => value
    },
    disabled: {
        type: Boolean,
        default: false
    },
    variant: {
        type: String as () => 'underline' | 'outline',
        default: 'underline',
    },
    showErrorIcon: {
        type: Boolean,
        default: true
    }
});

const emit = defineEmits(['update:modelValue']);
const errorIconRef = ref<HTMLElement | null>(null);
const tooltipRef = ref<HTMLElement | null>(null);
const showTooltip = ref(false);

const handleInput = (event: any) => {
    const target = event.target as HTMLInputElement;
    emit('update:modelValue', target.value);
};

/**
 * watcher para formatear el valor del input
 * con un formateador personalizado
 * del tipo (value: string) => string
 */
if (props.formatter) {
    watch(() => props.modelValue, (newValue) => {
        handleInput({
            target: {
                value: props.formatter(newValue)
            }
        });
    });
}

const positionTooltip = () => {
    if (!errorIconRef.value || !tooltipRef.value) return;
    
    const iconRect = errorIconRef.value.getBoundingClientRect();
    const tooltipEl = tooltipRef.value;
    
    tooltipEl.style.left = `${iconRect.left + (iconRect.width / 2)}px`;
    tooltipEl.style.top = `${iconRect.top - 10}px`;
};
const handleWindowEvents = () => {
    if (showTooltip.value) {
        positionTooltip();
    }
};

onMounted(() => {
    window.addEventListener('scroll', handleWindowEvents, true);
    window.addEventListener('resize', handleWindowEvents);
});

onUnmounted(() => {
    window.removeEventListener('scroll', handleWindowEvents, true);
    window.removeEventListener('resize', handleWindowEvents);
});
</script>

<template>
    <div :class="{ 'opacity-50 select-none': disabled }" class="relative w-full flex flex-col">
        <span class="relative w-full flex items-center gap-2">
            <input
                :type="type"
                :placeholder="placeholder"
                :disabled="disabled"
                @input="handleInput"
                :value="modelValue"
                :class="[
                    'outline-none transition-all duration-200 w-full',
                    props.variant === 'underline' ? 'border-b' : 'border rounded-md p-2',
                    errorMessage ? 'border-red-500 focus:border-red-500' : 'border-black/20 focus:border-primary-600'
                ]"
            />
            <span 
                v-if="errorMessage && showErrorIcon" 
                ref="errorIconRef" 
                @mouseenter="showTooltip = true" 
                @mouseleave="showTooltip = false"
                class="text-red-500 absolute right-2"
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
            </span>
        </span>
        
        <!-- Mensaje de error debajo del input -->
        <div v-if="errorMessage" class="text-red-500 text-sm mt-1">
            {{ errorMessage }}
        </div>
        
        <!-- Tooltip (para dispositivos con hover) -->
        <div 
            v-if="showTooltip && errorMessage" 
            ref="tooltipRef" 
            class="error-tooltip fixed bg-white text-red-500 px-3 py-2 rounded-md text-sm"
            style="transform: translate(-50%, -100%);"
        >
            {{ errorMessage }}
            <div class="tooltip-arrow absolute bg-white w-2 h-2 rotate-45" style="bottom: -5px; left: 50%; transform: translateX(-50%);"></div>
        </div>
    </div>
</template>

<style scoped>
.error-tooltip {
    max-width: 300px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.1);
    animation: tooltip-fade-in 0.2s ease-out;
    z-index: 9999;
    pointer-events: none;
}

.tooltip-arrow {
    box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
}

@keyframes tooltip-fade-in {
    from {
        opacity: 0;
        transform: translate(-50%, -90%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -100%);
    }
}
@media (hover: none) {
    .error-tooltip {
        padding: 8px 12px;
        font-size: 14px;
    }
}
</style>