<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
    percentage: { type: Number, required: true },
    color: { type: String, default: '#1e3a8a' },
    size: { type: Number, default: 100 },
    title: { type: String, default: 'Chart Title' },
    centerText: { type: String, default: null }
});

const strokeWidth = 10;
const radius = 20.9155 - strokeWidth / 2;
const circumference = 2 * Math.PI * radius;

const strokeDashoffset = computed(() => (100 - props.percentage) / 100 * circumference);

const gradientStart = props.color;
const gradientEnd = props.color + '80';

const getGradientX2 = () => {
    const angle = (props.percentage / 100) * 360;
    const x = Math.sin(angle * (Math.PI / 180)) * 100 + 50;
    return `${x}%`;
};

const getGradientY2 = () => {
    const angle = (props.percentage / 100) * 360;
    const y = Math.cos(angle * (Math.PI / 180)) * 100 + 50;
    return `${y}%`;
};
</script>

<template>
    <div class="flex flex-col justify-center items-center">
        <!-- main chart -->
        <svg :width="size" :height="size" viewBox="-5 -5 45 45" class="block max-w-[100%] max-h-[250px]">
            <defs>
                <linearGradient id="mainGradient" x1="70%" y1="0%" :x2="getGradientX2()" :y2="getGradientY2()">
                    <stop offset="0%" :stop-color="gradientStart" />
                    <stop offset="100%" :stop-color="gradientEnd" />
                </linearGradient>
            </defs>
            <!-- background circle -->
            <path class="fill-none" d="M18 2.0845
             a 15.9155 15.9155 0 0 1 0 31.831
             a 15.9155 15.9155 0 0 1 0 -31.831" stroke-width="10" stroke="#F6EEFF" />
            <!-- main circle -->
            <path class="fill-none rounded-full circle shadow-lg" :stroke="`${color}`" d="M18 2.0845
             a 15.9155 15.9155 0 0 1 0 31.831
             a 15.9155 15.9155 0 0 1 0 -31.831" :stroke-width="strokeWidth" :stroke-dasharray="circumference"
                :stroke-dashoffset="strokeDashoffset" />
            <!-- inner transparent circle -->
            <circle cx="18" cy="18" :r="radius - strokeWidth / 2" fill="#fff" class="shadow-lg" />
            <!-- percentage text -->
            <text x="18" y="18" class="fill-black text-[0.45rem] font-semibold text-center mt-2" text-anchor="middle"
                dominant-baseline="middle">
                {{ centerText ?? percentage.toString() + '%' }}
            </text>
        </svg>
        <div v-if="title" class="text-black font-medium mt-2 2xl:text-pretty xl:text-center xl:text-sm 2xl:text-base">{{
            title }}
        </div>
    </div>
</template>