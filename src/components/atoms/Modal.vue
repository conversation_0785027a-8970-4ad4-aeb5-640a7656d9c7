<script setup lang="ts">
import { useClickOutside } from '@/hooks/useClickOutside';
import { ref, type Ref } from 'vue';
const props = defineProps({
    refs: {
        type: Array as () => Ref<HTMLElement | null>[],
        required: false,
    },
    isOpen: {
        type: Boolean,
        required: true,
    },
    onClose: {
        type: Function,
        required: true,
    },
    title: String,
})

const modalRef = ref<HTMLElement | null>(null);

useClickOutside(props.refs ? [...props.refs, modalRef] : [modalRef], () => {
    props.onClose();
});

</script>

<template>
    <article :class="{ 'hidden': !isOpen }" class="fixed inset-0 z-50 bg-black/25 flex justify-center items-center">
    </article>
    <transition name="modal">
        <div v-if="isOpen" class="fixed z-50 inset-0 bg-transparent flex justify-center items-center">
            <div ref="modalRef"
                class="bg-white max-h-[94vh] max-w-[80vw] overflow-y-auto rounded-3xl flex flex-col gap-6 p-8 z-10">
                <slot></slot>
            </div>
        </div>
    </transition>
</template>

<style scoped>
.modal-enter-active,
.modal-leave-active {
    transition: all 0.3s ease-in-out;
}

.modal-enter-from {
    opacity: 0;
    transform: translateY(-100%);
}

.modal-enter-to {
    opacity: 1;
    transform: translateY(0);
}

.modal-leave-from {
    opacity: 1;
    transform: translateY(0);
}

.modal-leave-to {
    opacity: 0;
    transform: translateY(-100%);
}
</style>