<script setup lang="ts">
defineProps({
    icon: {
        type: String,
        required: true
    },
    selected: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['click'])

const handleClick = () => {
    emit('click')
}

</script>

<template>
    <span @click="handleClick"
        class="flex justify-center items-center rounded-full px-2.5 py-1.5 border border-primary-300 cursor-pointer hover:bg-primary-200 transition-all duration-200"
        :class="`${selected ? 'bg-primary-800 hover:bg-primary-800' : ''}`">
        <i :class="`${icon} ${selected ? 'text-white' : 'text-primary-800'}`" class="text-xl"></i>
    </span>
</template>