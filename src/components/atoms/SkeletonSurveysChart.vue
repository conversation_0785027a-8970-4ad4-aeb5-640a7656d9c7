<script setup lang="ts">
</script>

<template>
  <div class="relative box-border flex justify-between mb-6">
    <div>
      <div class="skeletonHead w-96 h-4 mb-1"></div>
    </div>
  </div>

  <div class="grid grid-cols-2 gap-4">
    <div class="relative w-full flex justify-center items-center">
      <div class="w-[90%] aspect-[2] bg-gray-300 rounded-t-full animate-pulse"></div>
    </div>
    <div class="flex flex-col justify-between h-full py-4 2xl:space-y-10 space-y-1">
      <div class="flex flex-col items-center">
        <div class="flex items-center space-x-2 mb-2">
          <span class="skeletonHead w-3 h-3 rounded-full"></span>
          <p class="skeletonHead w-24 h-3"></p>
        </div>
        <p class="skeleton w-32 h-2 mb-2"></p>
      </div>

      <div class="flex flex-col items-center">
        <div class="flex items-center space-x-2 mb-2">
          <span class="skeletonHead w-3 h-3 rounded-full"></span>
          <p class="skeletonHead w-24 h-3"></p>
        </div>
        <p class="skeleton w-32 h-2 mb-2"></p>
      </div>

      <div class="flex flex-col items-center">
        <div class="flex items-center space-x-2 mb-2">
          <span class="skeletonHead w-3 h-3 rounded-full"></span>
          <p class="skeletonHead w-24 h-3"></p>
        </div>
        <p class="skeleton w-32 h-2 mb-2"></p>
      </div>
    </div>
  </div>

  <div class="2xl:mt-10 mt-5">
    <div class="skeletonHead w-48 h-4 2xl:mb-3 mb-1"></div>
    <div class="2xl:space-y-2 space-y-1">
      <p class="skeleton w-full h-3"></p>
      <p class="skeleton w-[90%] h-3"></p>
      <p class="skeleton w-[85%] h-3"></p>
    </div>
  </div>
</template>

<style scoped>
.skeleton {
  @apply bg-gray-300 rounded-full animate-pulse;
}
.skeletonHead {
  @apply bg-gray-400 rounded-full animate-pulse;
}
</style>
