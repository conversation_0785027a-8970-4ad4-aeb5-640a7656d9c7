<template>
    <div class="flex flex-col">
      <!-- mini charts -->
      <div class="flex space-x-32 mt-4">
        <div v-for="(miniChart, index) in miniCharts" :key="index" class="flex flex-col items-center select-none">
          <svg width="160" height="160" viewBox="-3 0 41 36" class="block max-w-[100%] max-h-[100%]">
            <path class="circle-bg" d="M18 2.0845
                 a 15.9155 15.9155 0 0 1 0 31.831
                 a 15.9155 15.9155 0 0 1 0 -31.831" stroke="#eee" stroke-width="6" />
            <path class="circle" :stroke="miniChart.color" :stroke-width="8" d="M18 2.0845
                 a 15.9155 15.9155 0 0 1 0 31.831
                 a 15.9155 15.9155 0 0 1 0 -31.831" :stroke-dasharray="miniChart.circumference"
              :stroke-dashoffset="miniChart.strokeDashoffset" stroke-linecap="round" />
            <text x="18" y="20.35" class="percentage fill-black" text-anchor="middle">{{ miniChart.percentage }}%</text>
          </svg>
          <div class="text-center mt-2 text-xs font-semibold">
              {{ miniChart.text }}
           </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref } from 'vue';
  const miniCharts = ref([
    { percentage: 81, color: '#40189D', nivel: 1, text: 'Institucionalización' },
    { percentage: 81, color: '#2AA88A', nivel: 2, text: 'Adquisición de bienes' },
    { percentage: 22, color: '#C860AB', nivel: 3, text: 'Adquisición de servicios' },
    { percentage: 22, color: '#F84646', nivel: 4, text: 'Contratación de personal' },
    { percentage: 62, color: '#6EB3D1', nivel: 5, text: 'Comunicación' },
    { percentage: 62, color: '#FFAB2D', nivel: 5, text: 'Accountability' }
  ].map(chart => ({
    ...chart,
    circumference: 100,
    strokeDashoffset: (100 - chart.percentage) / 100 * 100
  })));
  
  </script>
  
  <style scoped>
  .circle-bg {
    fill: none;
    stroke-width: 6;
  }
  
  .circle {
    fill: none;
    stroke-linecap: round;
    transition: stroke-dashoffset 0.5s;
  }
  
  .percentage {
    font-size: 0.5em;
  }
  
  .flex {
    display: flex;
  }
  
  .space-x-24 > * + * {
    margin-left: 1rem; 
  }
  
  .mt-4 {
    margin-top: 1rem;
  }
  
  .text-center {
    text-align: center;
  }
  
  .font-semibold {
    font-weight: 600;
  }
  </style>
  