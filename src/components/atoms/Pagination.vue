<script setup lang="ts">
import Button from './Button.vue';
defineProps({
    itemCount: {
        type: Number,
        required: true
    },
    totalPages: {
        type: Number,
        required: true
    },
    page: {
        type: Number,
        required: true
    },
    size: {
        type: Number,
        default: 12
    }
})

const emit = defineEmits(['next', 'previous', 'goToPage']);

const handleNext = () => {
    emit('next');
};

const handlePrevious = () => {
    emit('previous');
};

const goToPage = (page: number) => {
    emit('goToPage', page);
};

</script>

<template>
    <div v-if="totalPages > 1" class="flex items-center justify-end w-full">
        <div class="flex justify-between self-end items-center gap-x-6">
            <Button v-if="page > 0" class="border-none text-sm" variant="invert" @click="handlePrevious">
                <span class="flex items-center gap-3">
                    <span class="relative flex items-center mr-2.5">
                        <i class="fa-solid fa-chevron-left absolute left-0"></i>
                        <i class="fa-solid fa-chevron-left absolute left-1.5"></i>
                    </span>
                    Anterior
                </span>
            </Button>
            <div v-else></div>
            <div class="bg-primary-200 rounded-full flex items-center gap-1">
                <span
                    class="text-primary-800 font-semibold py-2 px-6 cursor-pointer hover:bg-primary-300 rounded-full transition-all duration-200"
                    v-for="n in totalPages" :key="n"
                    :class="[{ 'bg-primary-800 text-white rounded-full py-2 px-6 hover:bg-primary-800': n - 1 === page }]"
                    @click="goToPage(n - 1)">
                    {{ n }}
                </span>
            </div>
            <Button v-if="itemCount >= size" class="border-none text-sm" variant="invert" @click="handleNext">
                <span class="flex items-center gap-3">
                    Siguiente
                    <span class="relative flex items-center ml-2">
                        <i class="fa-solid fa-chevron-right absolute right-0"></i>
                        <i class="fa-solid fa-chevron-right absolute right-1.5"></i>
                    </span>
                </span>
            </Button>
            <div v-else></div>
        </div>

    </div>

</template>