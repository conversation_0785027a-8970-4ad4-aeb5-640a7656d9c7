<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import Button from '@/components/atoms/Button.vue';
const activeSection = ref<string>('inicio');

const links = [
  { name: 'Inicio', href: 'inicio' },
  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: 'principios' },
  { name: 'Servic<PERSON>', href: 'servicios' },
  { name: '<PERSON><PERSON>', href: 'contacto' }
];

const scrollToSection = async (id: string) => {
  const section = document.getElementById(id);
  if (section) {
    section.scrollIntoView({ behavior: 'smooth', block: 'start' });
    setTimeout(() => {
      activeSection.value = id;
    }, 300);
  }
};

const updateActiveSection = () => {
  requestAnimationFrame(() => {
    const sections = document.querySelectorAll('section');
    let currentSection = '';

    sections.forEach((section) => {
      const rect = section.getBoundingClientRect();
      const middleOfScreen = window.innerHeight / 2;

      if (rect.top <= middleOfScreen && rect.bottom >= middleOfScreen) {
        currentSection = section.id;
      }
    });

    if (currentSection && activeSection.value !== currentSection) {
      activeSection.value = currentSection;
    }
  });
};

onMounted(() => {
  window.addEventListener('scroll', updateActiveSection);
});

onUnmounted(() => {
  window.removeEventListener('scroll', updateActiveSection);
});
</script>

<template>
  <nav class="fixed top-0 left-0 w-full bg-background z-50">
    <div class="flex items-center justify-between p-3 px-5"> 
      <div class="flex items-center space-x-2">
        <img src="@/assets/logos/Logo-svg-v1_2.svg" alt="Logo" class="h-8" /> 
        <span class="header-2">e-transparencia</span>
      </div>
      <ul class="flex space-x-14 justify-center">
        <li v-for="link in links" :key="link.href">
          <a
            :href="'#' + link.href"
            @click.prevent="scrollToSection(link.href)"
            :class="[
              '2xl:body-1 transition-colors',
              activeSection === link.href ? 'text-black font-bold' : 'text-neutral-400 hover:text-black'
            ]"
          >
            {{ link.name }}
          </a>
        </li>
      </ul>
      <router-link to="/login">
        <Button variant="primary" class="body-3 h-10 w-34">Iniciar Sesión</Button>
      </router-link>
    </div>
  </nav>
</template>
