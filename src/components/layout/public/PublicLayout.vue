<template>
  <section class="flex h-full w-full flex-col gap-0">
    <main class="p-0">
      <slot></slot>
    </main>
  </section>
</template>

<script setup lang="ts">
</script>

<style scoped>
#page {
  display: grid;
  width: 100%;
  height: 100vh;
  grid-template-areas:
    "nav nav nav"
    "main main main"
    "main main main"
    "footer footer footer";
  grid-template-rows: 10vh 1fr 1fr;
  grid-template-columns: 95vw 1fr 1fr;
  gap: 10px;
  background-color: #ffffff;
}
</style>