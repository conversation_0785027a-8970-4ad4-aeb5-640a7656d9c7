<template>
   <footer class="bg-primary-400 text-white py-10 w-full">
    <div class="px-4 md:px-8 lg:px-12 xl:px-16">
      <div class="flex flex-col md:flex-row justify-between items-start mb-8">
        <div class="mb-6 md:mb-0 flex flex-col items-center gap-2">
  <img 
    src="@/assets/logos/Logo-whitev2.png" 
    alt="Logo" 
    class="h-16 md:h-20"
  />
  <div class="flex mt-2 w-full justify-around">
    <img 
      src="@/assets/logos/anid_rojo_azul.png" 
      alt="Logo adicional 1" 
      class="h-10"
    />
    <img 
      src="@/assets/logos/ufro-logo.png" 
      alt="Logo adicional 2" 
      class="h-10"
    />
  </div>
        </div>
        <div class="flex flex-col items-start">
          <div class="-mb-8">
            <p class="text-xl font-semibold font-montserrat">Prueba nuestros</p>
            <p class="text-xl font-semibold font-montserrat">servicios</p>
          </div>
          <div class="flex gap-4">
              <router-link to="/login"><Button :variant="'outline'" class="mt-12 h-9 w-44 2xl:header-1 font-bold 2xl:h-10 2xl:w-48">Iniciar sesión</Button></router-link>
              <router-link to="/register"> <Button :variant="'outline'" class="mt-12 h-9 w-44 2xl:header-1 font-bold 2xl:h-10 2xl:w-48">Regístrate</Button></router-link>               
          </div>
        </div>
      </div>
      <div class="border-t border-white my-6"></div>
      <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
        <div class="flex items-center mb-4 md:mb-0">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
          <span><EMAIL></span>
        </div>
        <div class="flex flex-wrap gap-x-6 gap-y-2">
          <a href="#" class="hover:underline">Inicio</a>
          <a href="#principios" class="hover:underline">Principios</a>
          <a href="#servicios" class="hover:underline">Servicios</a>
          <a href="#contacto" class="hover:underline">Contáctanos</a>
        </div>
      </div>
    </div>
  </footer>
</template>
<script setup lang="ts">
  import Button from '@/components/atoms/Button.vue';
</script>
