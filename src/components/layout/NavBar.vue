<script setup lang="ts">
import { ref, watch } from 'vue';
import { type Route } from '@/router/routes';
import { routesByRole } from '@/router/routes';
import { useRoute } from 'vue-router';

const currentTab = ref<Route>(routesByRole[0]);
const currentTabIndex = ref(0);

const route = useRoute();

const setCurrentTab = (tab: Route, index: number) => {
  currentTab.value = tab;
  currentTabIndex.value = index;
};

const isActive = (tab: Route) => {
  return currentTab.value.path === tab.path;
};

watch(() => route.path, () => {
  const tab = routesByRole.find((tab) => tab.path === route.path);
  if (tab) {
    setCurrentTab(tab, routesByRole.indexOf(tab));
  }
});

</script>


<template>
  <nav class="[grid-area:nav] flex flex-col justify-between h-screen text-black py-8 inverted-border-radius__navbar">
    <div class="flex flex-col w-full gap-6">
      <div class="flex w-full justify-center mb-12 px-3">
        <picture class="flex w-48 h-20 overflow-hidden items-center">
          <img alt="logo" class="w-full h-full object-cover" src="@/assets/logos/nav-logo.png" />
        </picture>

      </div>
      <ul class="flex flex-col gap-12">
        <li v-for="(tab, index) in routesByRole" :key="tab.meta.name" :class="{
          'transform transition-all duration-300 ': true,
          'translate-y-2': currentTab.meta.name !== tab.meta.name && currentTabIndex < index,
          '-translate-y-2': currentTab.meta.name !== tab.meta.name && currentTabIndex > index
        }">
          <router-link :to="{ path: tab.path }" :class="isActive(tab) ? 'text-purple-950'
            : 'text-white/60 hover:text-white'" class="flex max-w-full justify-center items-center relative ml-6 font-semibold 
            h-6 transition-all duration-300" @click="setCurrentTab(tab, index)">
            <span class="flex py-4 w-full items-center justify-center"
              :class="isActive(tab) ? 'inverted-border-radius' : 'bg-transparent'">
              <div class="flex w-44 justify-start">
                <span class="2xl:mr-6 xl:mr-8">
                  <i :class="tab.meta.icon + (isActive(tab) ? ' text-purple-700' : ' text-white')"></i>
                </span>
                <h3 class="z-10 font-bold 2xl:text-base xl:text-sm">{{ tab.meta.name }}</h3>
              </div>
            </span>
          </router-link>
        </li>
      </ul>
    </div>

    <div class="flex w-full justify-center font-semibold text-xs">
      <span class="flex flex-col gap-1">
        <p class="text-white">Universidad de La Frontera </p>
        <span class="text-white/50">&copy; 2024 </span>
      </span>
    </div>
  </nav>
</template>


<style scoped>
/* estilos para poder crear un pseudoelemento que simule un borde redondeado invertido */
/* es más fácil de entender con css puro que con tailwindcss */
.inverted-border-radius__navbar {
  position: relative;
  max-width: 100%;
  background-color: #40189D;
  border-radius: 0 0 0 0;
}

.inverted-border-radius__navbar::after {
  content: "";
  position: absolute;
  background-color: transparent;
  height: 30px;
  width: 30px;
  border-radius: 0;
  top: 0;
  right: -30px;
  border-top-left-radius: 25px;
  box-shadow:
    0 -24px 0 0 #40189D,
    -18px 0 0 0 #40189D;

}


.inverted-border-radius {
  position: relative;
  max-width: 100%;
  background-color: #F2F2F2;
  border-radius: 85px 0 0 85px;
}

.inverted-border-radius::before,
.inverted-border-radius::after {
  content: "";
  position: absolute;
  background-color: transparent;
  height: 20px;
  width: 30px;
  border-radius: 0;
}

.inverted-border-radius::before {
  top: -20px;
  right: 0;
  border-bottom-right-radius: 25px;
  box-shadow:
    0 18px 0 0 #F2F2F2,
    18px 0 0 0 #F2F2F2;
}

.inverted-border-radius::after {
  bottom: -20px;
  right: 0;
  border-top-right-radius: 25px;
  box-shadow:
    0 -16px 0 0 #F2F2F2,
    -16px 0 0 0 transparent,
    0 16px 0 0 transparent,
    16px 0 0 0 #F2F2F2;
}
</style>