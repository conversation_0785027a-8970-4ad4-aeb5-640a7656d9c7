<template>
  <section id="page">
    <Header></Header>
    <Navbar></Navbar>
    <main id="main" class="[grid-area:main] box-border">
      <slot></slot>
    </main>
  </section>
</template>

<script setup lang="ts">
import Navbar from './NavBar.vue';
import Header from './Header.vue';
</script>

<style scoped>
@media (min-width: 1536px) {
  #page {
    display: grid;
    width: 100%;
    height: 100vh;
    grid-template-areas:
      "nav head head"
      "nav main main"
      "nav main main";
    grid-template-rows: 10vh 1fr 1fr;
    grid-template-columns: 16vw 1fr 1fr;
    gap: 10px;
    background-color: #F2F2F2;
  }
}

@media (min-width: 1280px) {
  #page {
    display: grid;
    width: 100%;
    height: 100vh;
    grid-template-areas:
      "nav head head"
      "nav main main"
      "nav main main";
    grid-template-rows: 10vh 1fr 1fr;
    grid-template-columns: 18vw 1fr 1fr;
    gap: 10px;
    background-color: #F2F2F2;
  }

}
</style>