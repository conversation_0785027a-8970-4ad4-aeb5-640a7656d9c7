<script setup lang="ts">
import { type Dimension, type Process } from '@/models/types';
import Card from '../atoms/Card.vue';
import { onMounted, ref } from 'vue';
import { useDimensions } from '@/hooks/useDimensions';
import { useAsyncFetch } from '@/hooks/useAsyncFetch';
import SummaryCardSkeleton from '../templates/SummaryCardSkeleton.vue';

const actualProcess = defineModel({
    type: Object as () => Process,
    required: true
});

const dimensions = ref<Dimension[]>([]);
const { handleGetDimensionsByProcessID } = useDimensions();

const fetchDimensions = async () => {
    dimensions.value = await handleGetDimensionsByProcessID(actualProcess.value.id);
};

await fetchDimensions();


</script>

<template>
    <Card class="flex flex-col gap-10 2xl:flex-1 xl:w-[60%] shadow-md">
        <div class="flex w-full justify-between">
            <h1 class="flex flex-col gap-4 text-xl font-semibold">
                Resultados obtenidos
                <span class="text-primary-800 text-base">e-Transparencia</span>
            </h1>
        </div>
        <h3 class="flex flex-col gap-4 text-xl font-semibold">Su institución obtuvo un nivel general de 4
            <p class="text-sm font-normal">Las dimensiones evaluadas de su institución lograron promediar un nivel 4, a
                continuación
                verá un detalle del
                nivel de cada dimensión obtenido.</p>
        </h3>
        <div class="w-full items-center gap-12 grid grid-cols-3 justify-center pt-4">
            <div :key="key" v-for="(dimension, key) in dimensions" class="flex flex-col gap-4 items-center">
                <span class="text-sm text-center">{{ dimension.name }}</span>
                <div class="h-20 w-20 rounded-full flex items-center justify-center"
                    :style="{ backgroundColor: dimension.color }">
                    <span class="h-16 w-16 rounded-full bg-white grid place-items-center">
                        <i :class="dimension.icon" class="text-3xl" :style="{ color: dimension.color }"></i>
                    </span>
                </div>
                <span class="text-sm font-semibold"> Nivel {{ dimension.level }}</span>
            </div>

        </div>
    </Card>
</template>