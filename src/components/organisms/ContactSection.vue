<template>
  <section id="contacto" class="flex justify-center items-center px-4 py-20 bg-background flex-col p-6 min-h-screen">
    <div class="container max-w-6xl flex flex-col lg:flex-row 2xl:ml-10 ml-28 items-center gap-8">
      <div class="w-full lg:w-1/2 flex justify-center items-center">
        <img src="@/assets/image/image-contact.png" class="2xl:w-5/6 w-7/12 h-auto object-contain rounded-lg shadow-md"
          alt="Contact image">
      </div>

      <div
        class="w-full lg:w-2/5 xl:w-5/12 2xl:w-1/2 flex items-center 2xl:ml-0 -ml-28 justify-center text-center lg:text-left">
        <div class="max-w-md lg:max-w-sm xl:max-w-md 2xl:max-w-none">
          <p class="text-lg text-gray-600">e-transparencia</p>
          <h2 class="header-6 xl:header-5 2xl:header-2 font-bold text-gray-900">Prueba tu transparencia</h2>
          <p class="mt-4 text-gray-700 2xl:body-2 body-3">
            La seguridad y confidencialidad de la información de tu empresa son nuestra prioridad. Protegemos tus datos
            con altos estándares de seguridad para evitar accesos no autorizados o usos indebidos. Cumplimos con
            normativas vigentes y mejoramos constantemente nuestras medidas de protección.
          </p>
          <router-link to="/register">
            <Button class="mt-12 h-9 w-44 2xl:header-1 font-bold 2xl:h-10 2xl:w-48">Solicitar registro</Button>
          </router-link>
          <ul class="mt-8 text-left text-sm text-gray-700 list-disc list-inside space-y-2">
            <a class="header-4">Instructivos</a>
            <li>
    Introducción general a e-transparencia 
    <span
      class="text-primary-400 hover:underline cursor-pointer"
      @click="handleDownloadManual('manual-plataforma-eTransparencia.pdf', 'introduction')"
    >
      (click aquí para descargar)
    </span>
  </li>
  <li>
    Manual de gestor institucional 
    <span
      class="text-primary-400 hover:underline cursor-pointer"
      @click="handleDownloadManual('manual-usuario-gestor.pdf', 'manager')"
    >
      (click aquí para descargar)
    </span>
  </li>
  <li>
    Manual de solicitud de cuenta 
    <span
      class="text-primary-400 hover:underline cursor-pointer"
      @click="handleDownloadManual('manual-solicitud-cuenta.pdf', 'password-recovery')"
    >
      (click aquí para descargar)
    </span>
  </li>
</ul>
        </div>
      </div>
    </div>
  </section>
</template>
<script setup lang="ts">
import Button from '../atoms/Button.vue';
import { useProcesses } from '@/hooks/process/useProcesses';
const {simpleDownloadManuals}=useProcesses();
import { useToast } from 'vue-toast-notification';

const $toast = useToast();
const handleDownloadManual = (manualName: string, urlManual: string) => {
  simpleDownloadManuals(manualName, urlManual);
  $toast.info('Descargando manual...');
};
</script>