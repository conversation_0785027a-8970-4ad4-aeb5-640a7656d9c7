<script setup lang="ts">
import Card from '../atoms/Card.vue'
import Button from '../atoms/Button.vue'
import { useProcessSteps } from '@/hooks/process/useProcessSteps'
import NumberStepIndicator from '../atoms/NumberStepIndicator.vue'
import type { Process } from '@/models/types'
import { useProcesses } from '@/hooks/process/useProcesses'
import { computed, onMounted, ref } from 'vue'
import { Logger } from '@/services/logger'
import { useRouter } from 'vue-router'
const props = defineProps({
  actualProcess: {
    type: Object as () => Process,
    required: true
  },
  currentStep: {
    type: Number,
    required: true
  }
})

const surveyLink = ref<string>('')
const buttonText = ref<string>('Copiar vínculo')
const { handleGetProcessSurveyLink } = useProcesses()
const { processSteps, fetchRecommendations } = useProcessSteps(ref(props.actualProcess), true)
const processStatus = computed(() => {
  const status = props.actualProcess.status
  return status === 'APPEALABLE' ? 'FINISHED' : status
})
const router = useRouter()

const goToResults = () => {
  router.push('/gestor/process')
}

const splitText = (text: string) => {
  const index = text.indexOf(' ')
  if (index === -1) {
    return { part1: text, part2: '' }
  }

  const part1 = text.slice(0, index)
  const part2 = text.slice(index + 1)

  return { part1, part2 }
}

const copySurveyLink = async () => {
  try {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      // Usar API moderna si está disponible
      await navigator.clipboard.writeText(surveyLink.value);
      buttonText.value = '¡Copiado!';
    } else {
      // Método alternativo para entornos sin clipboard API
      const textArea = document.createElement('textarea');
      textArea.value = surveyLink.value;
      textArea.style.position = 'fixed'; // Evitar que se mueva en el viewport
      textArea.style.opacity = '0'; // Ocultar el elemento
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);

      if (successful) {
        buttonText.value = '¡Copiado!';
      } else {
        throw new Error('Copia manual fallida');
      }
    }

    setTimeout(() => {
      buttonText.value = 'Copiar vínculo';
    }, 2000);
  } catch (error) {
    Logger.error('Error copying link', error);
    buttonText.value = 'Error al copiar';
    setTimeout(() => {
      buttonText.value = 'Copiar vínculo';
    }, 2000);
  }
};

const getStepState = (stepNumber: number) => {
  const status = props.actualProcess.status
  const mappedStatus = status === 'APPEALABLE' ? 'FINISHED' : status
  const currentStep = processSteps.value.find(s => s.status === mappedStatus)?.number || 1

  if (currentStep >= processSteps.value.length) {
    return 'completed'
  }

  if (stepNumber < currentStep) return 'completed'
  if (stepNumber === currentStep) return 'current'
  return 'upcoming'
}



onMounted(async () => {
  surveyLink.value = await handleGetProcessSurveyLink(props.actualProcess.id)
  await fetchRecommendations();
})
</script>

<template>
  <Card class="flex flex-col gap-8 2xl:flex-1 w-[100%] shadow-md">
    <div class="flex w-full justify-between">
      <h1 class="flex flex-col gap-2 text-3xl font-semibold">
        Proceso #PRC-00{{ actualProcess.id }}
        <span class="text-primary-800 text-lg">e-Transparencia</span>
      </h1>

      <div class="flex gap-4">
        <template v-if="actualProcess.status !== 'FINISHED' && actualProcess.status !== 'APPEALABLE'">
          <Button variant="invert" class="max-h-10 transition-all duration-200">
            <RouterLink :to="`/gestor/process/${actualProcess.id}/evidence`">
              Gestionar evidencias
            </RouterLink>
          </Button>
          <Button variant="invert" class="max-h-10 transition-all duration-200" @click="copySurveyLink">
            <span class="flex items-center gap-x-3 text-sm">
              <i class="fa-solid fa-link"></i>
              {{ buttonText }}
            </span>
          </Button>
        </template>

        <template v-else>
          <Button variant="primary" class="max-h-10 transition-all duration-200" @click="goToResults">
            Ver resultados
          </Button>
        </template>
      </div>
    </div>

    <!-- pasos del proceso -->
    <div
      class="flex w-full items-center 2xl:gap-20 xl:gap-12 2xl:p-5 xl:py-4 border-t border-b border-black/5 overflow-x-auto">
      <div v-for="step in processSteps" :key="step.title" class="flex items-center gap-5">
        <NumberStepIndicator :number="step.number" />
        <span class="font-semibold text-lg">
          <p class="text-black/50 font-medium text-base">
            {{ splitText(step.buttonText ?? '').part1 }}
          </p>
          <span class="capitalize"> {{ splitText(step.buttonText ?? '').part2 }}</span>
        </span>
      </div>
    </div>

    <section class="flex flex-col gap-6">
      <h3 class="flex flex-col gap-3 text-xl font-semibold">
        Paso a paso
        <p class="text-sm font-medium">
          Iniciar un proceso de autodiagnóstico debe hacerse de manera sistemática y organizada. A
          continuación podrá ver todos los pasos a seguir desde el inicio hasta el fin de su
          proceso.
        </p>
      </h3>
      <div class="flex flex-col gap-3">
        <div v-for="step in processSteps" :key="step.number"
          class="flex items-center gap-3 border-b border-black/5 py-2 cursor-pointer transition-all duration-300"
          :class="{
            'text-black': getStepState(step.number) !== 'completed',
            'opacity-40': getStepState(step.number) === 'upcoming',
          }">
          <template v-if="getStepState(step.number) === 'completed'">
            <i class="fa-solid fa-check text-green-500 text-lg px-2"></i>
          </template>
          <template v-else>
            <NumberStepIndicator :number="step.number" :active="getStepState(step.number) === 'upcoming'" />
          </template>

          <p class="font-semibold" :class="{
            'text-black/60': getStepState(step.number) === 'current',
          }">
            {{ step.title }}
          </p>
        </div>
      </div>
    </section>
  </Card>
</template>

