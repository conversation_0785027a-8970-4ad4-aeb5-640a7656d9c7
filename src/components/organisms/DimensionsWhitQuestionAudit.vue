<script setup lang="ts">
import { ref } from 'vue';
import Card from '../atoms/Card.vue';
import Button from '../atoms/Button.vue';
import type { Dimension, EvidenceStatus } from '@/models/types';
import AccordeonQuestion from '../atoms/AccordeonQuestion.vue';
import { EVIDENCE_STATUSES_MAP } from '@/mappers/evidenceMappers';

const props = defineProps({
    dimensions: {
        type: Array as () => Dimension[],
        required: true
    },
    selectable: {
        type: Boolean,
        default: false
    },
    effect: {
        type: String as () => 'tab' | 'progress',
        default: 'tab',
    },
    showEvidenceStatusesLabel: {
        type: Boolean,
        default: false
    }
})

const actualDimension = defineModel<Dimension>();

const currentDimensionIndex = ref(props.dimensions.findIndex(dimension => dimension.name === actualDimension.value?.name))

const handleSelectDimension = (dimension: Dimension) => {
    if (props.selectable) {
        actualDimension.value = dimension;
        currentDimensionIndex.value = props.dimensions.findIndex(d => d.name === dimension.name);
    }
}

const isDimensionClickable = (index: number) => {
    if (props.effect === 'progress') {
        return index <= currentDimensionIndex.value;
    }
    return props.selectable;
}

const getClassForDimension = (index: number) => {
    const baseClass = 'flex items-center gap-4 text-base font-semibold hover:text-black transition-all duration-300 select-none';
    const isActive = actualDimension.value?.name === props.dimensions[index]?.name;
    const isCompleted = props.effect === 'progress' && index < currentDimensionIndex.value;
    const textColorClass = isActive ? 'text-black' : 'text-black/40';
    const completedClass = isCompleted ? 'text-green-600' : '';

    return [
        baseClass,
        textColorClass,
        completedClass,
        isDimensionClickable(index) ? 'cursor-pointer' : 'cursor-not-allowed'
    ];
}

const handleNextDimension = () => {
    if (currentDimensionIndex.value < props.dimensions.length - 1) {
        currentDimensionIndex.value++;
        actualDimension.value = props.dimensions[currentDimensionIndex.value];
    }
}


const handlePrevDimension = () => {
    if (currentDimensionIndex.value > 0) {
        currentDimensionIndex.value--;
        actualDimension.value = props.dimensions[currentDimensionIndex.value];
    }
}

const evidenceStatuses = Object.entries(EVIDENCE_STATUSES_MAP).map(([key, value]) => ({
    status: key as EvidenceStatus,
    ...value
}));


</script>

<template>
    <section class="flex w-full gap-8">
        <Card class="flex flex-col gap-8 h-full 2xl:w-[25%] xl:w-[28%]">
            <slot name="title">
                <h3 class="text-xl font-bold">Dimensiones a evaluar</h3>
            </slot>
            <div class="border-t border-black/10"></div>
            <ul class="flex flex-col 2xl:gap-10 xl:gap-7">
                <li v-for="(dimension, index) in dimensions" @click="handleSelectDimension(dimension)" :key="index"
                    :class="getClassForDimension(index)" class="flex items-center gap-4 text-base font-semibold ">
                    <span class="w-6">
                        <i :class="dimension.icon" class="text-xl" :style="{ color: dimension.color }"></i>
                    </span>
                    <h3 class="2xl:text-base xl:text-sm" :class="currentDimensionIndex > index ? 'text-green-600' : ''">
                        {{ dimension.name }}
                    </h3>
                </li>
            </ul>
            <div class="border-t border-black/10"></div>
            <div>
                <slot name="dimension-footer">
                    <p class="text-justify 2xl:text-base xl:text-sm">{{ actualDimension?.description }}</p>
                </slot>
            </div>
        </Card>
        <Card class="flex flex-col h-full gap-8 flex-1 overflow-y-auto">
            <h3 class="2xl:text-xl xl:text-lg font-bold"> {{ actualDimension?.name }}</h3>
            <div class="border-t border-black/10"></div>
            <!-- según los requerimientos de negocio actuales, las preguntas no están consideradas
            pero eso podría cambiar en el futuro -->
            <!-- <AccordeonQuestion :questions="actualDimension?.questions ?? []" /> -->
            <div class="flex w-full items-center gap-20">
                <h3 class="font-semibold">Evidencias</h3>
                <div v-if="showEvidenceStatusesLabel" class="flex gap-6">
                    <span class="text-xl flex items-center gap-2" v-for="status in evidenceStatuses"
                        :key="status.status">
                        <i :class="status.class"></i>
                        <span class="text-sm font-medium">{{ status.label }}</span>
                    </span>

                </div>

            </div>
            <div class="flex w-full gap-6">
                <div v-for="evidence in actualDimension?.evidences" :key="evidence.title">
                    <slot name="evidences" :evidence="evidence"></slot>
                </div>
            </div>
            <div class="flex justify-between items-center">
                <div class="flex w-full justify-between items-center">
                    <Button variant="invert" @click="handlePrevDimension" class="self-start w-40"
                        v-if="dimensions.findIndex((d) => d.name === actualDimension?.name) > 0">
                        Volver
                    </Button>
                    <div v-else></div>
                    <Button @click="handleNextDimension"
                        v-if="dimensions.findIndex((d) => d.name === actualDimension?.name) < dimensions.length - 1"
                        class="w-40">
                        Siguiente
                    </Button>
                    <slot v-else name="end-button"></slot>
                </div>
            </div>
        </Card>
    </section>

</template>