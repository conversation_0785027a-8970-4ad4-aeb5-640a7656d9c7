<script setup lang="ts">
import Card from '@/components/atoms/Card.vue'
import LevelDistributionChart from '@/components/atoms/LevelDistributionChart.vue'
import { ref } from 'vue';
import { useInstitutions } from '@/hooks/useInstitutions';
import type { LevelPercentage } from '@/models/types';
import ListSkeleton from '../templates/ListSkeleton.vue';
import InstitutionsByLevelList from '../molecules/InstitutionsByLevelList.vue';

const level = ref(1);
const levelDistribution = ref<LevelPercentage[]>([])
const { handleGetLevelDistribution } = useInstitutions();

const handleMiniChartClick = (newLevel: number) => {
    level.value = newLevel;
};

const fetchDistribution = async () => {
    levelDistribution.value = await handleGetLevelDistribution();
};

await fetchDistribution();

</script>

<template>
    <Card class="flex w-full justify-between">
        <LevelDistributionChart :level="level" v-if="levelDistribution.length > 0"
            :levelDistribution="levelDistribution" class="flex-1" :onMiniChartClick="handleMiniChartClick"
            :size="200" />
        <div class="h-full w-[1px] bg-black/10 mx-16"></div>
        <section class="flex flex-col gap-6 flex-1">
            <h2 class="text-lg font-bold">Instituciones en nivel {{ level }}</h2>
            <Suspense :timeout="0">
                <template #default>
                    <InstitutionsByLevelList :level="level" :key="level" />
                </template>
                <template #fallback>
                    <ListSkeleton :count="5" />
                </template>
            </Suspense>
        </section>
    </Card>
</template>