<script setup lang="ts">
import Button from '../atoms/Button.vue';
import type { Process } from '@/models/types';
import { addMonthsToDate } from '@/helpers/dateHelpers';
import { formatDateForChile } from '@/helpers/formatters';
import { ref, watch } from 'vue';
import AuditorPicker from '../molecules/AuditorPicker.vue';
import { useProcesses } from '@/hooks/process/useProcesses';
import 'vue-toast-notification/dist/theme-sugar.css';
import { useToast } from 'vue-toast-notification';
import { useAsyncFetch } from '@/hooks/useAsyncFetch';
import EmployeesAccordion from '../molecules/EmployeesAccordion.vue';
import { Logger } from '@/services/logger';
const $toast = useToast();
const props = defineProps({
    process: {
        type: Object as () => Process,
        required: true,
    }
});
const employeeDetails = ref<any[]>([])

const { handleAcceptProcess, handleRejectProcess } = useProcesses();
const selectedAuditorId = ref<number>(-1);
const showRejectModal = ref(false);
const isRejectLoading = ref(false); 
const acceptSelectedProcess = async () => {
    if (selectedAuditorId.value !== -1) {
        try {
            $toast.info('Este proceso podría demorar, por favor espere...');
            await handleAcceptProcess(props.process.id, selectedAuditorId.value);
            $toast.success('Proceso aprobado correctamente');
            Logger.info(`Process with ID: ${props.process.id} successfully approved`);
            
                window.location.reload();
        } catch (error) {
            console.trace('Error en aceptar proceso', error);
            $toast.error('Error al aceptar el proceso');
            Logger.error(`Error accepting process with ID: ${props.process.id}: ${error}`);
        }
    }else {
        Logger.warn('An auditor has not been selected for the process');
    }
};
const generateEmployeeDetails = () => {
    const emails = typeof props.process.employeesEmails === 'string'
        ? props.process.employeesEmails.split(',').map(email => email.trim()).filter(email => email.length > 0)
        : props.process.employeesEmails;

    const names = typeof props.process.employeesNames === 'string'
        ? props.process.employeesNames.split(',').map(name => name.trim()).filter(name => name.length > 0)
        : props.process.employeesNames;

    const ruts = typeof props.process.employeesRut === 'string'
        ? props.process.employeesRut.split(',').map(rut => rut.trim()).filter(rut => rut.length > 0)
        : props.process.employeesRut;

    // Log para debug
    Logger.debug('Employee data lengths:', {
        emails: emails?.length || 0,
        names: names?.length || 0,
        ruts: ruts?.length || 0
    });

    // Usar la longitud del array más largo para asegurar que no se pierdan empleados
    const maxLength = Math.max(
        emails?.length || 0,
        names?.length || 0,
        ruts?.length || 0
    );

    employeeDetails.value = Array.from({ length: maxLength }, (_, index) => ({
        id: index + 1,
        email: emails?.[index] || '',
        name: names?.[index] || '',
        rut: ruts?.[index] || '',
    })).filter(employee => employee.email || employee.name || employee.rut); // Filtrar empleados completamente vacíos

    Logger.debug('Generated employee details:', {
        totalEmployees: employeeDetails.value.length,
        sampleEmployee: employeeDetails.value[0]
    });
};


const { fetchData: handleSubmit, isLoading } = useAsyncFetch(acceptSelectedProcess);
const openRejectModal = () => {
    showRejectModal.value = true;
};

const confirmRejectProcess = async () => {
    isRejectLoading.value = true; 
    try {
        console.log("RECHAZADO", props.process.id)
        await handleRejectProcess(props.process.id);
        $toast.error('Proceso rechazado con éxito');
        setTimeout(() => {
            window.location.reload();
        }, 2000);
    } catch (error) {
        Logger.error(`Error rejecting process with ID: ${props.process.id}: ${error}`);
    } finally {
        isRejectLoading.value = false; 
        showRejectModal.value = false;
    }
};

watch(() => props.process, () => {
    selectedAuditorId.value = -1;
});
generateEmployeeDetails();
</script>

<template>
    <section class="flex flex-col flex-1 gap-6">
        <h3 class="flex flex-col gap-2 font-semibold text-lg">{{ process.simpleInstitutionDTO.name }}
            <p class="text-sm font-normal"> Solicita someterse a {{ process.name }}</p>
            <hr class="my-4 border-black/10" />
        </h3>
        <div class="flex flex-col gap-4">
            <div class="flex justify-between">
                <div class="flex flex-col gap-3">
                    <span class="font-semibold flex flex-col gap-2">Fecha de inicio
                        <span type="text" class="bg-transparent text-black/50 px-2 py-4">
                            {{ formatDateForChile(process.startDate) }}
                        </span>
                    </span>
                </div>
                <div class="flex flex-col gap-2 px-4">
                    <span class="font-semibold flex flex-col gap-2">Fecha de término
                        <span type="text" class="bg-transparent text-black/50 px-2 py-4">
                            {{ formatDateForChile(process.endDate) }}
                        </span>
                    </span>
                </div>
                <AuditorPicker v-model="selectedAuditorId" />
            </div>
            <EmployeesAccordion :employees="employeeDetails">{{ process.name }}</EmployeesAccordion>
        </div>
        <div class="flex justify-between gap-4 mt-6 box-border py-6">
            <button @click="openRejectModal"
                    class="text-red-500 px-8 py-3 font-bold rounded-full border border-red-500">Rechazar</button>
            <Button :loading="isLoading" :disabled="selectedAuditorId === -1" @click="handleSubmit"
                    class="px-8">Aceptar</Button>
        </div>

        <!-- Modal de Rechazo -->
        <div v-if="showRejectModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div class="bg-white p-6 rounded-lg shadow-lg max-w-md">
                <h2 class="text-lg font-semibold mb-4">¿Estás seguro de que deseas rechazar este proceso?</h2>
                <div class="flex justify-end gap-4">
                    <button @click="confirmRejectProcess"
                            :disabled="isRejectLoading"
                            class="bg-red-500 text-white px-4 py-2 rounded-full flex items-center gap-2">
                        <span class="flex items-center">
                            <svg v-if="isRejectLoading" class="animate-spin mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                            </svg>
                            Confirmar
                        </span>
                    </button>
                    <button @click="showRejectModal = false"
                            :disabled="isRejectLoading"
                            class="border border-gray-300 px-4 py-2 rounded-full">Cancelar</button>
                </div>
            </div>
        </div>
    </section>
</template>

<style>
.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
