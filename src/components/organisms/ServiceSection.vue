<template>
  <section id="servicios" class="bg-primary-900 p-4 py-20">
    <div class="text-center mb-8">
      <h1 class="2xl:header-3 header-5 font-bold text-primary-50">NUESTROS SERVICIOS</h1>
      <p class="mt-4 body-2  text-primary-50">La seguridad y confidencialidad de la información de su institución es nuestra </p>
      <p class="body-2  text-primary-50">máxima prioridad. Nos tomamos muy en serio la protección.</p>
    </div>
    <div class="grid gap-8 px-4 md:grid-cols-2 lg:grid-cols-4 py-20">
      <Card
        v-for="(card, index) in cards"
        :key="index"
         class="flex flex-col items-center justify-center bg-white shadow-md rounded-lg p-4 h-80 2xl:h-[500px]"
      >
        <div class="flex flex-col items-center text-center">
          <div class="relative">
            <div class="relative z-10 mb-6">
                <img :src="card.icon" alt="icon" class="w-16 2xl:w-28 h-16 2xl:h-28">
            </div>
          </div>
          <h2 class="mt-4 text-sm 2xl:text-xl font-semibold">{{ card.title }}</h2>
          <p class="mt-4 text-xs 2xl:text-lg">{{ card.body }}</p>
        </div>
      </Card>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Card from '../atoms/Card.vue';
import grafico from '@/assets/icons/grafico.png';
import encuesta from '@/assets/icons/encuesta.png';
import reporte from '@/assets/icons/reporte-de-negocios.png';
import seguimiento from '@/assets/icons/seguimiento.png';

const cards = ref([
  { title: 'Encuestas', body: 'Obtén información clave sobre satisfacción, compromiso y necesidades de tu equipo con nuestras encuestas a empleados.', icon: encuesta },
  { title: 'Seguimiento', body: 'Visualiza datos clave con gráficos dinámicos y reportes interactivos para tomar decisiones informadas para mejorar la transparencia de tu organización.', icon: seguimiento },
  { title: 'Gráficos y estadísticas', body: 'Nuestro servicio presentan datos en gráficos atractivos y declarativos, resumiendo de forma amigable el estado y progreso de tu organización.', icon: grafico },
  { title: 'Reportes', body: 'Nuestro servicio presentan datos en gráficos intuitivos y, al finalizar el proceso, recibirás un reporte en PDF con un resumen completo del análisis.', icon: reporte },
]);
</script>

<style scoped>
.bg-lila {
  background-color: #8e44ad;
}
</style>
