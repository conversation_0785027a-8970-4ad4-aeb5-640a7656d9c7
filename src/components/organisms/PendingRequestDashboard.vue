<template>
    <div class="flex h-full w-full">
        <Card class="flex flex-col gap-5 w-full h-min items-center p-6">
            <div class="flex flex-col items-center justify-center">
                <i class="fa-solid fa-clock text-orange-500 text-6xl 2xl:text-8xl mb-4"></i>
                <h3 class="2xl:header-4 header-7 font-bold text-center">Solicitud en revisión</h3>
            </div>

            <p class="max-w-[80%] text-center 2xl:body-3 body-5">
                Su solicitud para iniciar un nuevo proceso ha sido enviada exitosamente y se encuentra pendiente de 
                aprobación por parte del administrador. Recibirá una notificación una vez que su solicitud sea revisada 
                y aprobada.
            </p>
            
            <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 max-w-[80%]">
                <div class="flex items-start gap-3">
                    <i class="fa-solid fa-info-circle text-orange-600 text-lg mt-0.5"></i>
                    <div class="text-sm text-orange-800">
                        <p class="font-semibold mb-1">¿Qué sigue?</p>
                        <ul class="list-disc list-inside space-y-1 text-xs">
                            <li>El administrador revisará su solicitud</li>
                            <li>Recibirá una notificación del estado de su solicitud</li>
                            <li>Una vez aprobada, podrá iniciar su proceso de autodiagnóstico</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="flex gap-4 mt-7 justify-center w-full">
                <RouterLink :to="{ path: `/gestor/record` }"
                    class="bg-white border border-neutral-950 px-6 py-3 rounded-md cursor-pointer inline-flex items-center text-sm gap-4 w-full max-w-[420px] justify-center min-h-[64px]">
                    <i class="fa-solid fa-building 2xl:text-xl text-base"></i>
                    <div class="flex flex-col leading-tight text-left">
                        <span class="font-medium 2xl:text-base text-sm">Ver institución</span>
                        <span class="text-xs text-neutral-700">Revisa los procesos anteriores y el historial</span>
                    </div>
                </RouterLink>
            </div>
        </Card>
    </div>
</template>

<script setup lang="ts">
import Card from '../atoms/Card.vue';
</script>
