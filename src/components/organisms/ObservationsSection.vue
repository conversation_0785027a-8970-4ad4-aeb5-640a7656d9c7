<template>
  <Card class="flex flex-col flex-1">
    <div class="flex flex-col">
      <h3 class="text-lg font-semibold">Observaciones</h3>
      <p class="text-sm text-gray-500">Sugerencias realizadas por el auditor.</p>
      <p v-if="totalComments > 0" class="mt-8 text-lg text-primary-900 font-semibold">
        Este proceso tiene {{ totalComments }} observación(es) disponibles:
      </p>
      <p v-else class="mt-8 text-lg text-gray-500 font-semibold">
        No hay observaciones disponibles para este proceso.
      </p>
      <section v-if="totalComments > 0" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-8 text-white">
        <div v-for="(section, index) in sectionsWithComments" :key="index">
            <ObservationCard 
              :title="section.name" 
              :observations="section.commentsCount" 
              :iconClass="section.icon"
              bgColor="primary-700" 
              @click="toggleSection(section.name)" 
              :expanded="expandedSection === section.name" 
            />
        </div>
      </section>
    </div>
  </Card>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import ObservationCard from '@/components/molecules/ObservationCard.vue';
import Card from '@/components/atoms/Card.vue';
import { useAuth } from '@/hooks/useAuth';
import { useEvidencesData } from '@/hooks/useEvidencesData';

const { authData } = useAuth();
const { sectionsWithComments, totalComments, expandedSection, fetchEvidenceData, toggleSection } = useEvidencesData();

const actualInstitutionID = ref(-1);

watch(
  authData,
  async (newValue) => {
    if (newValue && newValue.simpleInstitutionDTO) {
      actualInstitutionID.value = newValue.simpleInstitutionDTO.id;
      await fetchEvidenceData(actualInstitutionID.value);
    } else {
      console.error('Error: authData or simpleInstitutionDTO is null or nonexistent');
    }
  },
  { immediate: true }
);
</script>
