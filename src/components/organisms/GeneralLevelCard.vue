<template>
  <Card class="flex flex-col gap-8 h-full flex-1">
    <div class="flex flex-col w-full">
      <p class="text-lg font-semibold">Nivel general</p>
      <p class="text-sm text-gray-500">Basado en el proceso #PRC-{{ processId }}</p>
      <h1 class="justify-center text-center text-4xl font-bold mt-20">{{ level }}</h1>
      <StarRating :level="level" />
      <hr class="border-t-2 border-gray-200 my-4" />
      <p class="mt-4 font-semibold">¿Qué significa tener {{ level }}?</p>
      <p class="mt-2 text-base text-justify text-gray-500">
        <strong class="text-primary-700">Un Nivel {{ level }}</strong> {{ levelDescription }}.
      </p>
    </div>
  </Card>
</template>

  
  <script setup lang="ts">
  import { computed, defineProps } from 'vue'
  import Card from '@/components/atoms/Card.vue'
  import StarRating from '@/components/atoms/StarRating.vue'
  import levels from '../../../public/level.json'

  const props = defineProps<{ level: number; institutionID: number; processId: number }>()
  const levelDescription = computed(() => {
  const levelInfo = levels.Levels.find(item => Number(item.level) === props.level)
  return levelInfo ? levelInfo.descripcion : "Descripción no disponible"
})
  </script>
  