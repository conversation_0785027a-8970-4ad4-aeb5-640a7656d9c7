<script setup lang="ts">
import ListSkeleton from '../templates/TableListSkeleton.vue';


defineProps({
    items: {
        type: Array as unknown as any,
        required: true
    },
    headers: {
        type: Array as () => string[],
        required: true,
    },
    emptyMessage: {
        type: String,
        default: 'No se encontraron elementos.'
    },
    isLoading: {
        type: Boolean,
        default: false
    }
})

</script>

<template>
    <ListSkeleton v-if="isLoading" :count="7" />
    <div v-else class="flex flex-col gap-6">
        <slot name="header"></slot>
        <table v-if="items.length > 0"
            class="overflow-x-auto shadow-md sm:rounded-lg w-full text-sm text-left rtl:text-right text-gray-500 ">
            <thead class="text-sm text-black font-semibold bg-gray-50">
                <tr>
                    <th v-for="header in headers" :key="header" scope="col" class="px-6 py-3">
                        {{ header }}
                    </th>
                </tr>
            </thead>
            <tbody>
                <slot v-for="item in items" :key="item.id" :item="item">
                </slot>
            </tbody>

        </table>
        <p v-else class="ml-4 mt-4 text-black/70 p-4">
            {{ emptyMessage }}
        </p>
    </div>
</template>