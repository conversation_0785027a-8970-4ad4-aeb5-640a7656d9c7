<script setup lang="ts">
import { onMounted, ref } from 'vue';
import Button from '@/components/atoms/Button.vue';
import GridView from '@/components/organisms/GridView.vue';
import Card from '@/components/atoms/Card.vue';
import RecommendationsTab from '@/components/organisms/RecommendationsTab.vue';
import type { Process, Recommendation } from '@/models/types';
import { useProcesses } from '@/hooks/process/useProcesses';
import { useAuth } from '@/hooks/useAuth';
import { useAsyncFetch } from '@/hooks/useAsyncFetch';
import { useRecommendations } from '@/hooks/useRecommendations';

const props = defineProps<{
  processId?: number | null;
}>();

const recommendations = ref<Recommendation[]>([]);
const filteredRecommendations = ref<Recommendation[]>([]);
const actualProcess = ref<Process | null>(null);

const { authData } = useAuth();
const {
  handleGetRecommendationsByProcessID,
  handleGeneratePDFFile,
} = useRecommendations();
const { handleGetActualProcessByInstitutionID } = useProcesses();

const fetchRecommendations = async () => {
  let processIdToUse = props.processId;

  if (!processIdToUse) {
    actualProcess.value = await handleGetActualProcessByInstitutionID(authData.value!.simpleInstitutionDTO!.id);
    if (!actualProcess.value) {
      console.error('No se encontró un proceso activo para la institución.');
      return;
    }
    processIdToUse = actualProcess.value.id;
  }

  recommendations.value = await handleGetRecommendationsByProcessID(processIdToUse);
  filteredRecommendations.value = recommendations.value;
};

const generatePDFHandler = async () => {
  const processId = props.processId ?? actualProcess.value?.id;
  const institutionName = authData.value?.simpleInstitutionDTO?.name ?? 'Institución';
  if (processId) {
    await handleGeneratePDFFile(processId, institutionName);
  }
};

const { fetchData: generatePDF, isLoading: isLoadingPDF } = useAsyncFetch(generatePDFHandler);
const { fetchData, isLoading } = useAsyncFetch(fetchRecommendations);

onMounted(fetchData);

const onTabChange = (dimensionName: string) => {
  if (dimensionName === 'General') {
    filteredRecommendations.value = recommendations.value;
  } else {
    filteredRecommendations.value = recommendations.value.filter(
      (recommendation) => recommendation.dimension === dimensionName
    );
  }
};
</script>

<template>
  <section class="flex flex-col w-full gap-10">
    <RecommendationsTab :onTabChange="onTabChange">
      <template #download-button>
        <Button :loading="isLoadingPDF" class="w-40" color="primary" @click="generatePDF">
          Descargar
        </Button>
      </template>
    </RecommendationsTab>

    <section class="flex flex-col gap-8">
      <h3 class="font-semibold text-lg">Recomendaciones generales</h3>
      <GridView :isLoading="isLoading" :items="filteredRecommendations">
        <template #default="{ item }">
          <Card
            :key="(item as Recommendation).description"
            class="flex flex-col justify-between shadow-md 2xl:h-[26rem] xl:h-[26rem] p-0 relative"
          >
            <transition>
              <div
                :key="(item as Recommendation).dimension"
                class="absolute top-0 left-0 right-0 rounded-t-2xl h-16 w-full flex justify-center items-center text-white"
                :style="{ background: (item as Recommendation).color }"
              >
                {{ (item as Recommendation).dimension }}
              </div>
            </transition>

            <div class="flex flex-col h-[87%] w-full justify-between mt-12">
              <div class="flex flex-col h-full justify-between">
                <p class="flex flex-col gap-2.5 text-sm text-black/70 text-justify h-[90%] overflow-y-auto">
                  <span class="text-sm text-black/30 font-semibold">En lo que deberían mejorar</span>
                  {{ (item as Recommendation).description }}
                </p>
                <h3 class="text-sm font-semibold text-primary-800">
                  Recomendación para subir a nivel {{ (item as Recommendation).targetLevel }}
                </h3>
              </div>
            </div>
          </Card>
        </template>
      </GridView>
    </section>
  </section>
</template>

<style scoped>
.v-enter-active,
.v-leave-active {
  transition: opacity 0.5s;
}
.v-enter-from,
.v-leave-to {
  opacity: 0;
}
</style>
