<script lang="ts" setup>
import Button from '../atoms/Button.vue'
import Input from '../atoms/Input.vue'
import { ref, computed, watch, nextTick } from 'vue'
import useVuelidate from '@vuelidate/core'
import {
    recoveryPassword_rules,
    recoveryCode_rules,
    recoveryReset_rules
} from '@/models/validation/formRules'
import { usePasswordRecovery } from '@/hooks/usePasswordRecovery'
import { useToast } from 'vue-toast-notification'

const $toast = useToast()
const step = ref<'email' | 'code' | 'reset'>('email')
const formSubmitted = ref(false)
const apiError = ref('')
const successMessage = ref('')

// Referencias para los inputs del código
const codeInputs = ref<HTMLInputElement[]>([])
const codeValues = ref(['', '', '', '', '', ''])

const recoveryData = ref({
    email: '',
    confirmEmail: '',
    code: '',
    password: '',
    confirmPassword: ''
})

// Actualizar el código cuando cambian los valores individuales
watch(codeValues, (newValues) => {
    recoveryData.value.code = newValues.join('')
}, { deep: true })

const buttonText = computed(() => {
    if (step.value === 'email') {
        return apiError.value ? 'El correo no tiene una cuenta asociada' : 'Solicitar recuperación'
    }
    if (step.value === 'code') return 'Validar código'
    if (step.value === 'reset') return 'Actualizar contraseña'
    return 'Continuar'
})
const passwordRecovery = usePasswordRecovery()
const loading = ref(false)

// Setup validations
const $vEmail = useVuelidate(recoveryPassword_rules, recoveryData.value)
const $vCode = useVuelidate(recoveryCode_rules, recoveryData.value)
const $vReset = useVuelidate(recoveryReset_rules, recoveryData.value)

watch(() => recoveryData.value, () => {
    if (formSubmitted.value) {
        apiError.value = ''
    }
}, { deep: true })

const emailError = computed(() => ($vEmail.value.email.$errors[0]?.$message as string) || '')
const confirmEmailError = computed(
    () => ($vEmail.value.confirmEmail.$errors[0]?.$message as string) || ''
)
const codeError = computed(() => ($vCode.value.code.$errors[0]?.$message as string) || '')
const passwordError = computed(() => ($vReset.value.password.$errors[0]?.$message as string) || '')
const confirmPasswordError = computed(
    () => ($vReset.value.confirmPassword.$errors[0]?.$message as string) || ''
)

// Función para manejar la entrada de código
const handleCodeInput = (index: number, event: Event) => {
    const input = event.target as HTMLInputElement
    const value = input.value
    
    // Permitir solo un dígito
    if (value.length > 1) {
        codeValues.value[index] = value.slice(-1)
    } else {
        codeValues.value[index] = value
    }
    
    // Si se ingresó un valor y no es el último input, mover al siguiente
    if (value && index < 5) {
        nextTick(() => {
            codeInputs.value[index + 1].focus()
        })
    }
    
    // Verificar si todos los campos están llenos para enviar automáticamente
    if (codeValues.value.every(val => val !== '')) {
        nextTick(() => {
            handleValidateCode()
        })
    }
}

// Función para manejar el retroceso en los inputs de código
const handleCodeKeydown = (index: number, event: KeyboardEvent) => {
    // Si se presiona retroceso y el campo está vacío, mover al anterior
    if (event.key === 'Backspace' && !codeValues.value[index] && index > 0) {
        nextTick(() => {
            codeInputs.value[index - 1].focus()
        })
    }
}

// Función para pegar código
const handleCodePaste = (event: ClipboardEvent) => {
    event.preventDefault()
    const pastedData = event.clipboardData?.getData('text')
    
    if (pastedData && /^\d{6}$/.test(pastedData)) {
        for (let i = 0; i < 6; i++) {
            codeValues.value[i] = pastedData[i]
        }
        
        // Verificar si todos los campos están llenos para enviar automáticamente
        if (codeValues.value.every(val => val !== '')) {
            nextTick(() => {
                handleValidateCode()
            })
        }
    }
}

const handleSendEmail = async () => {
    formSubmitted.value = true
    apiError.value = ''
    successMessage.value = ''

    const isValid = await $vEmail.value.$validate()
    if (!isValid) return

    loading.value = true
    try {
        await passwordRecovery.sendCode(recoveryData.value.email)
        successMessage.value = 'Código enviado correctamente. Por favor revisa tu correo.'
        $toast.success('Código de recuperación enviado')
        step.value = 'code'
        
        // Limpiar los valores del código
        codeValues.value = ['', '', '', '', '', '']
        recoveryData.value.code = ''
        
        // Enfocar el primer input del código
        nextTick(() => {
            if (codeInputs.value[0]) {
                codeInputs.value[0].focus()
            }
        })
    } catch (err: any) {
        apiError.value = err.message || 'Error al enviar el código de recuperación'
        $toast.error(apiError.value)
        setTimeout(() => {
            apiError.value = ''
        }, 3000)
    } finally {
        loading.value = false
    }
}

const handleValidateCode = async () => {
    formSubmitted.value = true
    apiError.value = ''
    successMessage.value = ''

    // Actualizar el código completo
    recoveryData.value.code = codeValues.value.join('')

    const isValid = await $vCode.value.$validate()
    if (!isValid) return

    loading.value = true
    try {
        await passwordRecovery.validateCode(recoveryData.value.email, recoveryData.value.code)
        successMessage.value = 'Código validado correctamente. Ahora puedes crear una nueva contraseña.'
        $toast.success('Código validado correctamente')
        step.value = 'reset'
    } catch (err: any) {
        apiError.value = err.message || 'Código de recuperación inválido'
        $toast.error(apiError.value)
    } finally {
        loading.value = false
    }
}

const handleResetPassword = async () => {
    formSubmitted.value = true
    apiError.value = ''
    successMessage.value = ''

    const isValid = await $vReset.value.$validate()
    if (!isValid) return

    loading.value = true
    try {
        await passwordRecovery.changePassword(
            recoveryData.value.email,
            recoveryData.value.password,
            recoveryData.value.confirmPassword
        )
        successMessage.value = 'Contraseña actualizada con éxito. Ya puedes iniciar sesión con tu nueva contraseña.'
        $toast.success('Contraseña actualizada con éxito')
        setTimeout(() => {
            window.location.href = '/login'
        }, 3000)
    } catch (err: any) {
        apiError.value = err.message || 'Error al actualizar la contraseña'
        $toast.error(apiError.value)
    } finally {
        loading.value = false
    }
}

const goBack = () => {
    if (step.value === 'code') {
        step.value = 'email'
    } else if (step.value === 'reset') {
        step.value = 'code'
    }
    formSubmitted.value = false
    apiError.value = ''
    successMessage.value = ''
}
</script>

<template>
    <div class="flex items-center justify-center">
        <div class="w-full flex justify-center">
            <div class="w-full max-w-md p-8 bg-white rounded-lg">
                <div class="flex flex-col h-fit w-full gap-0 *:font-montserrat">
                    <h2 class="2xl:header-1 header-3 font-bold text-center leading-none">Recuperación</h2>
                </div>

                <form @submit.prevent="
                    step === 'email'
                        ? handleSendEmail()
                        : step === 'code'
                            ? handleValidateCode()
                            : handleResetPassword()
                    " class="flex flex-col gap-4 mt-6">

                    <!-- Paso 1: Email -->
                    <div v-if="step === 'email'" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Correo electrónico
                                <span
                                    :class="formSubmitted && emailError ? 'text-red-600' : 'text-primary-400'">*</span>
                            </label>
                            <Input :errorMessage="formSubmitted ? emailError : ''" variant="outline" type="email"
                                v-model="recoveryData.email" placeholder="Ingrese su correo electrónico"
                                :showErrorIcon="false"
                                class="block w-full shadow-sm sm:text-sm" required autofocus />
                            <p v-if="formSubmitted && emailError" class="mt-1 text-sm text-red-600">{{ emailError }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Confirmar correo electrónico
                                <span
                                    :class="formSubmitted && confirmEmailError ? 'text-red-600' : 'text-primary-400'">*</span>
                            </label>
                            <Input :errorMessage="formSubmitted ? confirmEmailError : ''" variant="outline" type="email"
                                v-model="recoveryData.confirmEmail" placeholder="Confirme su correo electrónico"
                                :showErrorIcon="false"
                                class="block w-full shadow-sm sm:text-sm" required />
                            <p v-if="formSubmitted && confirmEmailError" class="mt-1 text-sm text-red-600">{{
                                confirmEmailError }}</p>
                        </div>
                    </div>

                    <!-- Paso 2: Código -->
                    <div v-if="step === 'code'" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Código de recuperación
                                <span :class="formSubmitted && codeError ? 'text-red-600' : 'text-primary-400'">*</span>
                            </label>
                            
                            <!-- Nuevo input de código con 6 cuadros -->
                            <div class="flex justify-between gap-2 mt-2">
                                <template v-for="(_, index) in 6" :key="index">
                                    <input
                                        type="text"
                                        inputmode="numeric"
                                        maxlength="1"
                                        :ref="el => { if (el) codeInputs[index] = el as HTMLInputElement }"
                                        v-model="codeValues[index]"
                                        @input="handleCodeInput(index, $event)"
                                        @keydown="handleCodeKeydown(index, $event)"
                                        @paste="index === 0 ? handleCodePaste($event) : null"
                                        class="w-12 h-12 text-center border rounded-md text-lg font-medium focus:border-primary-600 focus:ring-1 focus:ring-primary-600 outline-none"
                                        :class="formSubmitted && codeError ? 'border-red-500' : 'border-gray-300'"
                                    />
                                </template>
                            </div>
                            
                            <p v-if="formSubmitted && codeError" class="mt-1 text-sm text-red-600">{{ codeError }}</p>
                        </div>

                        <div class="text-sm text-gray-600">
                            <p>Se ha enviado un código de recuperación a <strong>{{ recoveryData.email }}</strong></p>
                            <p class="mt-2">Si no lo has recibido, revisa tu carpeta de spam o solicita un nuevo código.
                            </p>
                        </div>

                        <div class="flex justify-between">
                            <button type="button" @click="goBack" class="text-primary-600 hover:underline text-sm">
                                Cambiar correo electrónico
                            </button>
                            <button type="button" @click="handleSendEmail"
                                class="text-primary-600 hover:underline text-sm">
                                Reenviar código
                            </button>
                        </div>
                    </div>

                    <!-- Paso 3: Reset -->
                    <div v-if="step === 'reset'" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Nueva contraseña
                                <span
                                    :class="formSubmitted && passwordError ? 'text-red-600' : 'text-primary-400'">*</span>
                            </label>
                            <Input :errorMessage="formSubmitted ? passwordError : ''" variant="outline"
                                v-model="recoveryData.password" type="password" placeholder="Ingrese nueva contraseña"
                                :showErrorIcon="false"
                                class="block w-full shadow-sm sm:text-sm" required autofocus />
                            <p v-if="formSubmitted && passwordError" class="mt-1 text-sm text-red-600">{{ passwordError
                                }}</p>
                            <div class="mt-2 text-xs text-gray-600">
                                <p>La contraseña debe contener:</p>
                                <ul class="list-disc pl-5 mt-1 space-y-1">
                                    <li :class="recoveryData.password.length >= 8 ? 'text-green-600' : ''">
                                        Mínimo 8 caracteres
                                    </li>
                                    <li :class="recoveryData.password.match(/[A-Z]/) ? 'text-green-600' : ''">
                                        Al menos una mayúscula
                                    </li>
                                    <li :class="recoveryData.password.match(/[a-z]/) ? 'text-green-600' : ''">
                                        Al menos una minúscula
                                    </li>
                                    <li :class="recoveryData.password.match(/[0-9]/) ? 'text-green-600' : ''">
                                        Al menos un número
                                    </li>
                                    <li :class="recoveryData.password.match(/[^A-Za-z0-9]/) ? 'text-green-600' : ''">
                                        Al menos un carácter especial
                                    </li>
                                    <li :class="!recoveryData.password.match(/\s/) ? 'text-green-600' : ''">
                                        Sin espacios
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Confirmar contraseña
                                <span
                                    :class="formSubmitted && confirmPasswordError ? 'text-red-600' : 'text-primary-400'">*</span>
                            </label>
                            <Input :errorMessage="formSubmitted ? confirmPasswordError : ''" variant="outline"
                                v-model="recoveryData.confirmPassword" type="password"
                                :showErrorIcon="false"
                                placeholder="Confirme la contraseña" class="block w-full shadow-sm sm:text-sm"
                                required />
                            <p v-if="formSubmitted && confirmPasswordError" class="mt-1 text-sm text-red-600">{{
                                confirmPasswordError }}</p>
                        </div>

                        <button type="button" @click="goBack" class="text-primary-600 hover:underline text-sm">
                            Volver al paso anterior
                        </button>
                    </div>
                    <Button :loading="loading" :variant="apiError ? 'missing' : 'primary'" type="submit"
                        class="w-full mt-6 rounded-md">
                        <span class="font-medium text-base">
                            {{
                                buttonText
                            }}
                        </span>
                    </Button>
                </form>

                <div class="w-full flex justify-start mt-10">
                    <router-link to="/login">
                        <button class="flex items-center text-primary-400 hover:underline text-sm">
                            <i class="fa-solid fa-arrow-right-to-bracket text-neutral-950 mr-1"></i>
                            Volver al inicio de sesión
                        </button>
                    </router-link>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Estilos para los inputs de código */
input[type="text"] {
    transition: all 0.2s ease;
}

input[type="text"]:focus {
    transform: scale(1.05);
    border-color: #6366f1;
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}
</style>