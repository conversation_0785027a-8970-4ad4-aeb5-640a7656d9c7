<script setup lang="ts">
import AddEmployeeFormModal from '@/components/molecules/AddEmployeeFormModal.vue';
import { ref } from 'vue'
import Button from '../atoms/Button.vue';
import { useEmployees } from '@/hooks/useEmployees';
import { useAsyncFetch } from '@/hooks/useAsyncFetch';
import 'vue-toast-notification/dist/theme-sugar.css';
import { useToast } from 'vue-toast-notification';
import { useProcesses } from '@/hooks/process/useProcesses';
const props = defineProps({
  canAction: {
    type: Boolean,
    required: true
  },
  onProcessRequest: {
    type: Function,
    required: true
  }
})

const $toast = useToast();
const isAddEmployeeModalOpen = ref(false);
const showTable = ref(false);
const fileInputRef = ref<HTMLInputElement | null>(null);
const { employees, handleEmployeesFileUpload, addEmployee, page, totalPages, handleGoToPage,removeEmployee ,handleNextPage, handlePreviousPage, totalEmployees, excelRawFile, fetchPageData, rawEmployees } = useEmployees();
const {simpleDownloadFormatEmployee}=useProcesses();
const handleSendRequest = async () => {
  try {
    await props.onProcessRequest(employees.value);
  } catch (error: any) {
    $toast.error('No se pudo enviar la solicitud. Si el problema persiste, comuníquese con el operador del servicio.');
  }
};

const { fetchData: handleSubmit, isLoading } = useAsyncFetch(handleSendRequest);

const handleEmployeesUpload = async (event: Event) => {
  try {
    await handleEmployeesFileUpload(event);
    fileInputRef.value!.value = '';
  } catch (error: any) {
    $toast.error('Error al importar funcionarios, ' + error.message);
  }
};


const handleClearEmployees = async () => {
  rawEmployees.value = [];
  totalEmployees.value = 0;
  page.value = 0;
  excelRawFile.value = null;

  await fetchPageData(0);

  if (fileInputRef.value) {
    fileInputRef.value.value = '';
  }

  $toast.success('Lista de empleados vaciada exitosamente');
};


const handleRemoveEmployee = (employeeToRemove: any) => {
  employees.value = employees.value.filter(employee => employee.rut !== employeeToRemove.rut);
  $toast.success(`Funcionario ${employeeToRemove.name} eliminado correctamente`);
  removeEmployee(employeeToRemove);
  if (employees.value.length === 0 && page.value > 0) {
    handlePreviousPage();
  }
};

const handleDownloadFormat = () => {
  simpleDownloadFormatEmployee();
  $toast.info('Descargando formato...');
};

const getPaginationRange = () => {
  const range = [];
  const maxVisiblePages = 5;

  if (totalPages.value <= maxVisiblePages) {
    for (let i = 0; i < totalPages.value; i++) {
      range.push(i);
    }
  } else {
    let start = Math.max(0, page.value - Math.floor(maxVisiblePages / 2));
    let end = Math.min(totalPages.value - 1, start + maxVisiblePages - 1);

    if (end === totalPages.value - 1) {
      start = Math.max(0, end - maxVisiblePages + 1);
    }

    if (start > 0) {
      range.push(0);
      if (start > 1) range.push(-1);
    }

    for (let i = start; i <= end; i++) {
      range.push(i);
    }

    if (end < totalPages.value - 1) {
      if (end < totalPages.value - 2) range.push(-1);
      range.push(totalPages.value - 1);
    }
  }

  return range;
};

const showEmptyTable = (opc:number) => {
  switch (opc) {
    case 1:
    showTable.value = true;
      break;
    default:
    showTable.value = false;
      break;
  }
  
};
</script>

<template>
  <div class="flex flex-col">
    <div v-if="!showTable && employees.length === 0" class="flex items-center justify-between">
      <Button variant="cancel">
        <router-link :to="{ path: '/gestor/requestProcess' }">
          Cancelar
        </router-link>
      </Button>
      <button @click="showEmptyTable(1)" class="bg-primary-900 text-white px-6 py-3 rounded-full cursor-pointer">
        Agregar Participantes
      </button>
    </div>

    <div v-if="showTable || employees.length > 0">

      <!-- Botones de gestión -->
      <div class="flex justify-between  items-center gap-3 mb-4">
        <p class="text-lg font-semibold text-gray-700">Lista de participantes</p>
        <div class="flex items-center gap-3">
          <label for="file-download"
            class="bg-white text-primary-900 border border-primary-900 px-4 py-2 rounded-md cursor-pointer inline-flex items-center text-sm">
            <i class="fa-solid fa-download mr-2"></i>Descargar Formato
          </label>
          <button id="file-download" class="hidden" @click="handleDownloadFormat"></button>
          <label for="file-upload-2"
            class="bg-white text-primary-900 border border-primary-900 px-4 py-2 rounded-md cursor-pointer inline-flex items-center text-sm">
            <i class="fa-solid fa-upload mr-2"></i>Importar
          </label>
          <input id="file-upload-2" type="file" ref="fileInputRef" class="hidden" @change="handleEmployeesUpload">

          <button
            class="bg-white text-primary-900 border border-primary-900 px-4 py-2 rounded-md cursor-pointer inline-flex items-center text-sm"
            size="sm" @click="isAddEmployeeModalOpen = true">
            <i class="fa-solid fa-plus mr-2"></i>Agregar
          </button>

          <button
            class="bg-red-100 text-red-700 border border-red-300 px-4 py-2 rounded-md cursor-pointer inline-flex items-center text-sm"
            @click="handleClearEmployees">
            <i class="fa-solid fa-trash-can mr-2"></i> Vaciar
          </button>
        </div>
      </div>

      <div class="overflow-x-auto rounded-2xl flex flex-col gap-4">
        <table class="w-full text-sm text-left rtl:text-right text-gray-700">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left">ID</th>
              <th scope="col" class="px-6 py-3 text-left">Nombre</th>
              <th scope="col" class="px-6 py-3 text-left">Email</th>
              <th scope="col" class="px-6 py-3 text-left">Rut</th>
              <th scope="col" class="px-6 py-3 text-left">Acción</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <template v-if="employees.length > 0">
              <tr v-for="(item, index) in employees" :key="index">
                <td class="px-6 py-4 whitespace-nowrap">{{ (page * 12) + index + 1 }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ item.name }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ item.email }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ item.rut }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <button @click="handleRemoveEmployee(item)" class="text-red-600 hover:text-red-800 flex items-center"
                    type="button">
                    <i class="fa-solid fa-trash mr-1"></i> Eliminar
                  </button>
                </td>
              </tr>
            </template>
            <template v-else>
              <tr>
                <td colspan="5" class="px-6 py-10 text-center text-gray-500">
                  Para poder continuar es necesario ingresar participantes
                </td>
              </tr>
            </template>
          </tbody>
        </table>

        <div v-if="employees.length > 0" class="flex items-center justify-between px-2 py-3">
          <div class="flex items-center">
            <p class="text-gray-500 text-sm">
              Mostrando {{ employees.length > 0 ? page * 12 + 1 : 0 }} - {{ Math.min((page + 1) * 12, totalEmployees) }}
              de {{ totalEmployees }} funcionarios
            </p>
          </div>

          <div class="flex items-center space-x-2">
            <button @click="handlePreviousPage" :disabled="page === 0"
              class="px-3 py-1 rounded-md border border-gray-300 text-sm font-medium"
              :class="page === 0 ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-50'">
              <i class="fa-solid fa-chevron-left"></i>
            </button>

            <div class="hidden sm:flex space-x-1">
              <button v-for="pageNum in getPaginationRange()" :key="pageNum"
                @click="pageNum >= 0 ? handleGoToPage(pageNum) : null" :class="[pageNum === page ? 'bg-primary-900 text-white' : pageNum >= 0 ? 'bg-white text-gray-700 hover:bg-gray-50' : '',
                pageNum >= 0 ? 'px-3 py-1 rounded-md text-sm font-medium' : 'px-2 py-1 text-gray-500']"
                :disabled="pageNum < 0">
                {{ pageNum >= 0 ? pageNum + 1 : '...' }}
              </button>
            </div>
            <span class="sm:hidden text-sm text-gray-700">
              {{ page + 1 }} / {{ totalPages }}
            </span>

            <button @click="handleNextPage" :disabled="page >= totalPages - 1"
              class="px-3 py-1 rounded-md border border-gray-300 text-sm font-medium"
              :class="page >= totalPages - 1 ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-50'">
              <i class="fa-solid fa-chevron-right"></i>
            </button>
          </div>
        </div>

        <div class="flex items-center justify-between gap-3 mt-4">
          <Button @click="showEmptyTable(2)" variant="cancel">
            <router-link :to="{ path: '/gestor/requestProcess' }">
              Cancelar
            </router-link>
          </Button>
          <Button :loading="isLoading" :disabled="!canAction || employees.length === 0"
            @click="() => (canAction && employees.length > 0) && handleSubmit()">
            Enviar solicitud
          </Button>
        </div>
      </div>
    </div>

    <AddEmployeeFormModal :isOpen="isAddEmployeeModalOpen" :onClose="() => isAddEmployeeModalOpen = false"
      :onEmployeeAdded="addEmployee" />
  </div>
</template>

<style scoped>
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>