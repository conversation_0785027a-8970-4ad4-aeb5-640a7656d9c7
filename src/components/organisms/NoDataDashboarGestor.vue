<template>
    <div class="flex h-full w-full">
        <Card class="flex flex-col gap-5 w-full h-min items-center p-6">
            <div class="flex flex-col items-center justify-center">
                <img :src="file" alt="icon" class="w-16 2xl:w-24 h-16 2xl:h-24 mb-2" />
                <h3 class="2xl:header-4 header-7 font-bold text-center">Sin procesos activos</h3>
            </div>

            <p class="max-w-[80%] text-center 2xl:body-3 body-5">
                Para visualizar un registro de avance continuo, es necesario tener un proceso activo. Si se desea ver un
                proceso previamente activo, se puede acceder a la sección "Mi Institución". Para consultar procesos más
                antiguos, se debe ingresar a la sección de historial, donde se encuentran todas las instituciones.
            </p>
            <div class="flex gap-4 mt-7 justify-center">
                 <RouterLink :to="{ path: `/gestor/requestProcess` }"
          class="bg-white border border-neutral-950 px-4 py-2 rounded-md inline-flex items-center text-sm gap-4 w-[40%] justify-center"
        >
          <i class="fa-solid fa-plus 2xl:text-xl text-base"></i>
          <div class="flex flex-col leading-tight text-left">
            <span class="font-medium 2xl:text-base text-sm">Iniciar nuevo proceso</span>
            <span class="text-xs text-neutral-700">Crear un nuevo proceso y asigna funcionarios</span>
          </div>
        </RouterLink>
        
                <RouterLink :to="{ path: `/gestor/record` }"
                    class="bg-white  border border-neutral-950 px-4 py-2 rounded-md cursor-pointer inline-flex items-center text-sm gap-4 w-[40%]">
                    <i class="fa-solid fa-building 2xl:text-xl text-base"></i>
                    <div class="flex flex-col leading-tight">
                        <span class="font-medium 2xl:text-base text-sm">Ver institución</span>
                        <span class="text-xs text-neutral-700">Revisa los procesos anteriores y el historial</span>
                    </div>
                </RouterLink>
            </div>
        </Card>
    </div>
</template>

<script setup lang="ts">
import Card from '../atoms/Card.vue';
import file from '@/assets/icons/icon.png';
</script>