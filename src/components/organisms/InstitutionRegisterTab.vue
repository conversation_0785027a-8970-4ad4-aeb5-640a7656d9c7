<script setup lang="ts">
import { formatPhoneNumber } from '@/helpers/formatters';
import Button from '../atoms/Button.vue';
import InstitutionFormModal from '../molecules/InstitutionFormModal.vue';
import type { Institution, InstitutionRequest } from '@/models/types';
import { ref, watch } from 'vue';
import { extractCapitalizedInitials } from '@/helpers/stringHelpers';
import { useRequests } from '@/hooks/useRequests';
import { useAsyncFetch } from '@/hooks/useAsyncFetch';

const props = defineProps({
    institutionRequest: {
        type: Object as () => InstitutionRequest,
        required: true,
    }
})

const { markInstitutionRequestAsRead } = useRequests();
const isCreateInstitutionModalOpen = ref(false);
const institutionFromRequest = ref<Institution | null>(null);

const handleOpenCreateInstitutionModal = async () => {
    isCreateInstitutionModalOpen.value = true;
    await markInstitutionRequestAsRead(props.institutionRequest.id!);
}

watch(() => props.institutionRequest, (newVal) => {
    institutionFromRequest.value = {
        name: newVal.institutionName,
        email: newVal.managerEmail,
        phoneNumber: formatPhoneNumber(newVal.phoneNumber),
        acronym: extractCapitalizedInitials(newVal.institutionName),
    } as Institution;
}, { immediate: true });

</script>


<template>
    <section class="flex flex-col flex-1 gap-6">
        <h3 class="flex flex-col w-full gap-2 text-lg font-bold">{{ institutionRequest.institutionName }}
            <p class="text-sm font-normal"> Solicita el registro de una cuenta de ingreso al portal web.</p>
        </h3>
        <hr class=" border-black/10" />
        <div class="flex w-full gap-14">
            <div class="flex flex-col gap-4 text-black/50 font-semibold">
                <span class="text-black">Nombre de Institución</span>
                {{ institutionRequest.institutionName }}
            </div>
            <div class="flex flex-col gap-4 text-black/50 font-semibold">
                <span class="text-black">Correo</span>
                {{ institutionRequest.managerEmail }}
            </div>
            <div class="flex flex-col gap-4 text-black/50 font-semibold">
                <span class="text-black">Teléfono</span>
                {{ institutionRequest.phoneNumber }}
            </div>
        </div>

        <Button @click="handleOpenCreateInstitutionModal" class="w-60 mt-10">
            Crear Institución
        </Button>
        <InstitutionFormModal 
    :institution="institutionFromRequest ?? ({} as Institution)" 
    :isModalOpen="isCreateInstitutionModalOpen" 
    :onClose="() => { isCreateInstitutionModalOpen = false }" 
    :enable="true"
    :enableAuditor="false" 
/>
    </section>
</template>