<template>
  <div class="relative box-border flex justify-between">
    <div>
      <p class="2xl:header-4 header-7 m-0">Progreso en tiempo real</p>
      <p class="2xl:body-4 body-4 m-0">Desde {{ startDate }} hasta hoy</p>
    </div>
    <div class="flex flex-col">
      <div class="flex items-center space-x-2 ">
        <span class="2xl:w-4 2xl:h-4 w-2 h-2 rounded-full bg-primary-700"></span>
        <p class="2xl:body-2 body-3 text-gray-500">Quorum mínimo</p>
      </div>
      <div class="flex flex-col">
        <p class="px-6 2xl:body-3 body-4 font-bold leading-none">{{ surveyQuorum }} encuestas</p>
      </div>
    </div>
  </div>
  <div class="relative flex justify-center 2xl:ml-16 h-[65%] 2xl:mt-10" style="transform: translateX(-10%)">
    <canvas ref="chartCanvas" class="py-4 px-8 w-full"></canvas>
    <div class="absolute inset-0 flex flex-col justify-center items-center">
      <p class="2xl:header-2 header-7 font-semibold" style="white-space: nowrap">{{ respondedSurveys }} encuestas</p>
      <p class="2xl:body-1 body-4" style="white-space: nowrap">de {{ totalSurveys }} ya fueron respondidas</p>
    </div>

    <div class="absolute bottom-0 left-0 w-full flex justify-between">
      <p class="2xl:body-2 body-4 font-bold" style="transform: translateY(40%) translateX(500%)">0</p>
      <p v-if="respondedSurveys >= surveyQuorum" class="2xl:body-5 body-7 font-bold text-center text-neutral-700 absolute left-1/2 transform -translate-x-1/2 translate-y-[40%]">
       Actualmente ya se ha completado el quorum mínimo para continuar el proceso
      </p>
      <p class="2xl:body-2 body-4 font-bold" style="transform: translateY(40%) translateX(-100%)">
        {{ totalSurveys }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount } from 'vue'
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  DoughnutController,
  registerables
} from 'chart.js'
import { Logger } from '@/services/logger';
ChartJS.register(ArcElement, Tooltip, Legend, DoughnutController, ...registerables)

const props = defineProps<{
  totalAnswers: number
  answeredSurveys: number
  surveyQuorum: number
  startDate: string
  active: boolean
}>()
const totalSurveys = ref(props.totalAnswers)
const respondedSurveys = ref(props.answeredSurveys)

const chartCanvas = ref<HTMLCanvasElement | null>(null)
let chartInstance: ChartJS | null = null


const chartData = () => ({
  labels: ['Respuestas', 'Pendientes'],
  datasets: [
    {
      data: [
        respondedSurveys.value,
        totalSurveys.value - respondedSurveys.value
      ],
      backgroundColor: ['#34D399', '#E5E7EB'],
      borderRadius: 50,
    },
  ],
});

const chartOptions = {
  cutout: '93%',
  rotation: -90,
  circumference: 180,
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: { display: false },
    tooltip: { enabled: false },
  },
};


const createChart = () => {
  if (chartCanvas.value) {
    const ctx = chartCanvas.value.getContext('2d')
    if (!ctx) return

    try {
      chartInstance = new ChartJS(ctx, {
        type: 'doughnut',
        data: chartData(),
        options: chartOptions,
      });
    } catch (error) {
      Logger.error('Error creating chart:', error);
    }
  }
}

const destroyChart = () => {
  if (chartInstance) {
    chartInstance.destroy();
    chartInstance = null;
  }
};

onMounted(() => {
  createChart();

  const handleResize = () => {
    destroyChart();
    createChart();
  };

  window.addEventListener('resize', handleResize);

  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize);
    destroyChart();
  });
});

watch(respondedSurveys, () => {
  if (chartInstance) {
    chartInstance.data.datasets[0].data = [
      respondedSurveys.value,
      totalSurveys.value - respondedSurveys.value
    ]
    chartInstance.update()
  }
})
</script>