<script setup lang="ts">
import { ref, onMounted, nextTick, computed, onBeforeUnmount, onActivated, onDeactivated, defineProps } from 'vue';
import { Chart as ChartJS, ArcElement, <PERSON><PERSON><PERSON>, <PERSON>, DoughnutController, registerables } from 'chart.js';

ChartJS.register(Arc<PERSON><PERSON>, Toolt<PERSON>, Legend, DoughnutController, ...registerables);

let colorDaysPassed="";
let colorDaysLeft="";
const props = defineProps({
  startDate: { type: String, required: true },
  endDate: { type: String, required: true },
  currentDate: { type: String, required: true },
  daysPassed: { type: Number, required: true },
  daysLeft: { type: Number, required: true },
  active: {type: Boolean, required: true}
});

const chartCanvas = ref<HTMLCanvasElement | null>(null);
let chartInstance: ChartJS | null = null;

const parseDate = (dateString: string) => {
  const parts = dateString.split('/');
  return new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
};

const isValidDate = (date: Date) => !isNaN(date.getTime());

const totalDays = computed(() => {
  const start = parseDate(props.startDate);
  const end = parseDate(props.endDate);
  return isValidDate(start) && isValidDate(end) ? Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) : 0;
});

const elapsedPercentage = computed(() => Math.min(100, Math.round((props.daysPassed / totalDays.value) * 100)+1));
const remainingPercentage = computed(() => Math.max(0, 100 - elapsedPercentage.value));

if(props.active){
  colorDaysPassed = '#FB923C'
  colorDaysLeft = '#E5E7EB'
}
else{
  colorDaysPassed = '#BCBCBC'
  colorDaysLeft = '#E5E7EB'
}

const chartData = () => ({
  labels: ['Días transcurridos', 'Días restantes'],
  datasets: [
    {
      data: [props.daysPassed, props.daysLeft],
      backgroundColor: [colorDaysPassed,colorDaysLeft],
      borderWidth: 1
    }
  ]
});

const chartOptions = () => ({
  cutout: '90%',
  rotation: -90,
  circumference: 180,
  plugins: {
    legend: { display: false },
    tooltip: { enabled: false }
  },
  maintainAspectRatio: false
});

const createChart = async () => {
  await nextTick();
  if (chartCanvas.value) {
    const ctx = chartCanvas.value.getContext('2d');
    if (ctx) {
      chartInstance = new ChartJS(ctx, {
        type: 'doughnut',
        data: chartData(),
        options: chartOptions(),
      });
    }
  }
};


const destroyChart = () => {
  if (chartInstance) {
    chartInstance.destroy();
    chartInstance = null;
  }
};

const isTodayBeforeEndDate = () => {
  const today = new Date();
  const endDate = new Date(props.endDate);
  
  today.setHours(0, 0, 0, 0);
  endDate.setHours(0, 0, 0, 0);

  return today <= endDate;
};

onMounted(() => {
  createChart();

  const handleResize = () => {
    destroyChart();
    createChart();
  };

  window.addEventListener('resize', handleResize);

  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize);
    destroyChart();
  });
});

onActivated(() => {
  createChart();
});

onDeactivated(() => {
  destroyChart();
});
</script>

<template>
<h2 class="2xl:header-4 header-7">Avance del proceso de encuesta</h2>
  <div>
  <div class="grid grid-cols-2">
    <div class="relative -mt-14 w-100">
      <canvas ref="chartCanvas" class="w-full h-full"></canvas>
    <div v-if="props.active && isTodayBeforeEndDate()" class="absolute inset-0 flex flex-col justify-center items-center">
      <div class="py-4"></div>
        <p class="2xl:header-5 header-5 text-neutral-600">
          {{ daysLeft === 0 ? 'El proceso termina hoy' : 'Quedan' }}
        </p>
        <p class="2xl:header-4 header-5 font-bold">
          {{ daysLeft === 0 ? '' : daysLeft + ' días' }}
        </p>
      <div class="text-center">
        <p class="2xl:body-4 body-5 text-neutral-900">
          Fecha de inicio: {{ startDate }} <br />
          Fecha de término: {{ endDate }}
        </p>
      </div>
    </div>
      <div v-else class="absolute inset-0 flex flex-col justify-center items-center">
        <div class="py-4"></div>
        <p class="2xl:header-3 header-5 font-semibold text-neutral-600">Proceso</p>
        <p class="2xl:header-3 header-5 font-bold">Finalizado</p>
        <div class="text-center">
          <p class="2xl:body-3 body-4 text-neutral-900">
            Fecha de término: {{ endDate }}
          </p>
        </div>
      </div>
    </div>

    <div class="flex flex-col h-full justify-center -mt-6 items-center space-y-2">
  <div class="flex flex-col items-center">
    <div class="flex items-center 2xl:ml-7 ml-5 space-x-2">
      <span class="2xl:w-4 2xl:h-4 w-2 h-2 rounded-full" :style="{ backgroundColor: colorDaysPassed }"></span>
      <p class="2xl:body-2 body-4  text-neutral-700">Días transcurridos</p>
    </div>
    <p class="2xl:header-3 header-7 leading-none">{{ daysPassed }} días</p>
    <p class="2xl:body-3 body-4 text-neutral-700 leading-tight">{{ elapsedPercentage }}% del plazo</p>
  </div>

  <div class="flex flex-col items-center">
    <div class="flex items-center space-x-2">
      <span class="2xl:w-4 2xl:h-4 w-2 h-2 rounded-full" :style="{ backgroundColor: colorDaysLeft }"></span>
      <p class="2xl:body-1 body-3 text-neutral-700">Días restantes</p>
    </div>
    <p class="2xl:header-3 header-7 leading-none">{{ daysLeft }} días</p>
    <p class="2xl:body-3 body-4 text-neutral-700 leading-tight">{{ remainingPercentage }}% del plazo</p>
  </div>
</div>
  </div>

  <div class="2xl:-mt-14 -mt-5">
  <p v-if="props.active && isTodayBeforeEndDate()" class="2xl:header-7 body-4 font-bold text-neutral-800">
    El proceso de encuesta está en curso
  </p>
  <p v-else class="body-3 font-bold text-neutral-800">
    El proceso ha finalizado
  </p>
  
  <p v-if="props.active && isTodayBeforeEndDate()" class="2xl:body-4 body-5 font-semibold text-neutral-700 mt-3">
    Quedan <span class="text-accent-5-500">{{ daysLeft }} días</span> para que se cierre el
    proceso de encuesta. Una vez terminado, se enviará a uno de nuestros auditores para su
    evaluación. Si aún no consigue el quórum mínimo,
    <a class="text-primary-700">reenvíe las encuestas</a> a sus funcionarios.
  </p>
  <p v-else class="2xl:body-4 body-5 font-semibold text-neutral-700 mt-3 text-justify">
  Actualmente, el proceso de encuesta se encuentra <span class="text-accent-5-500">finalizado</span>. 
  Todas las respuestas recopiladas han sido registradas y el proceso ha llegado a su fin. 
  En este momento, el sistema está en espera de la creación de nuevos procesos de encuesta, 
  los cuales podrán ser configurados para su futura evaluación. 
</p>
</div>
  </div>
</template>

<style scoped>
.relative {
  width: 120%;
}
.disabled {
  opacity: 0.5;
  pointer-events: none;
}
</style>
