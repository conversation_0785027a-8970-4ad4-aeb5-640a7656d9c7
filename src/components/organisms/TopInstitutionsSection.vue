<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useAdmins } from '@/hooks/useAdmin';
import InstitutionWithDimensionsCard from "@/components/molecules/InstitutionWithDimensionsCard.vue";
import type { Institution } from '@/models/types';
import TopInstitutionSkeleton from '../templates/TopInstitutionSkeleton.vue';
import { Logger } from '@/services/logger';

const institutions = ref<Institution[]>([]);
const isLoading = ref(true); 
const hasError = ref(false);

const { dashboardGetStats } = useAdmins();

onMounted(async () => {
  try {
    const stats = await dashboardGetStats();
    institutions.value = stats.top;
    hasError.value = stats.top.length === 0;
  } catch (error) {
    hasError.value = true;
  } finally {
    isLoading.value = false; 
  }
});
</script>


<template>
  <section class="flex flex-col gap-6">
    <h3 class="text-lg font-semibold">Top 3 Instituciones</h3>

    <div v-if="isLoading" class="flex w-full justify-between gap-6">
      <TopInstitutionSkeleton :count="3" />
    </div>

    <div v-else-if="!hasError && institutions.length > 0" class="flex w-full justify-between gap-6">
      <InstitutionWithDimensionsCard 
        v-for="institution in institutions" 
        :key="institution.id"
        :institution="institution" 
        :isLoading="isLoading" />
    </div>

    <div v-else class="text-gray-500 text-sm italic">
      No hay datos disponibles para mostrar en este momento.
    </div>
  </section>
</template>


