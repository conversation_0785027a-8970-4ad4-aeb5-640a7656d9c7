<script setup lang="ts">
import { ref } from 'vue'
import IconButton from '../atoms/IconButton.vue';
import GridView from './GridView.vue';
import ListView from './ListView.vue';
import GridSkeleton from '../templates/GridSkeleton.vue';

defineProps({
    items: {
        type: Array,
        required: true
    },
    headers: {
        type: Array as () => string[],
        required: true,
    },
    label: {
        type: String,
        required: true
    },
    isLoading: {
        type: Boolean,
        default: false
    },
    isDate: {
        type: Boolean,
        default: true
    },
    isDateAscending:{
        type: Boolean,
        default: true
    }
})

const viewMode = ref('grid');

const changeToListMode = () => {
    viewMode.value = 'list';
}

const changeToGridMode = () => {
    viewMode.value = 'grid';
}
</script>


<template>
    <template v-if="isLoading">
        <GridSkeleton :count="9" />
    </template>
    <div v-else class="flex flex-col w-full gap-8">
        <div class="flex w-full justify-between items-center gap-6">
            <p class="ml-2 mt-2 text-black/70 font-semibold">
                {{ items.length > 0 ? `Mostrando ${items.length} ${label} ` : '' }}
            </p>
            <div class="flex items-center gap-4">
                <!-- filtro por fecha? -->
                <button v-if="isDate"
                    class="flex gap-3 items-center px-4 py-2 border rounded-full border-primary-300 text-primary-800 text-sm font-semibold"
                    @click="$emit('toggleDateSort')">
                    <i :class="!isDateAscending ? 'fa-solid fa-arrow-up-short-wide' : 'fa-solid fa-arrow-down-short-wide'" class="text-base"></i>
                    Fecha
                    <i class="ml-2 fa-solid fa-chevron-down"></i>
                </button>
                <!-- slot para opciones adicionales -->
                <slot name="options"></slot>
                <div v-if="items.length > 0" class="flex items-center gap-4">
                    <IconButton :selected="viewMode === 'list'" @click="changeToListMode" icon="fa-solid fa-list" />
                    <IconButton :selected="viewMode === 'grid'" @click="changeToGridMode"
                        icon="fa-solid fa-border-all" />
                </div>
            </div>
        </div>

        <GridView v-if="viewMode === 'grid'" :items="items">
            <template #default="{ item }">
                <slot name="grid" :item="item"></slot>
            </template>
        </GridView>
        <ListView v-else :items="items" :headers="headers">
            <template #default="{ item }">
                <slot name="list" :item="item"></slot>
            </template>
        </ListView>

    </div>

</template>
