<script setup lang="ts">
import Card from '@/components/atoms/Card.vue';
import Button from '../atoms/Button.vue';
import dimensions from '@/assets/JsonData/dimensions.json';
import { ref } from 'vue';

// (se podría exportar a un nuevo componente reutilizable)
const props = defineProps({
    onTabChange: {
        type: Function,
        required: true
    }
})

const tabs = ref<string[]>(["General", ...dimensions.map((dimension) => dimension.name)]);

const getDimensionByTab = (tab: string) => {
    // sección "general" que sólo existe en la vista de recomendaciones
    if (tab === "General") return {
        id: "general",
        name: "General",
        color: "#2AA873",
        description: "Los procesos de su institución deben ser sistemáticos, transparentes, alineados con directrices nacionales, y asegurar la responsabilidad y participación ciudadana."
    };
    return dimensions.find((dimension) => dimension.name === tab);
};

const selectedDimensionTab = ref<any>(getDimensionByTab("General"));

const handleSetSelectedDimensionTab = (tab: string) => {
    selectedDimensionTab.value = getDimensionByTab(tab);
    props.onTabChange(tab);
};

</script>

<template>
    <Card class="flex flex-col gap-8 w-full">
        <nav class="flex w-full items-center h-[10%] justify-between xl:gap-10 2xl:gap-0">
            <span v-for="tab in tabs" :key="tab"
                class="cursor-pointer select-none font-semibold hover:text-primary-900 transition-all duration-200 xl:text-center 2xl:text-nowrap xl:text-sm 2xl:text-base"
                :class="tab === selectedDimensionTab.name ? 'text-primary-900' : 'text-black/50'"
                @click="handleSetSelectedDimensionTab(tab)">
                {{ tab }}
            </span>
        </nav>
        <!-- tab de dimensión  -->
        <div class="flex w-full gap-10 h-full">

            <picture
                class="w-[65%] flex items-center justify-center min-h-full rounded-xl transition-all duration-500 relative overflow-hidden"
                :style="{ backgroundColor: selectedDimensionTab?.color }">
                <div class="text-white text-center 2xl:max-w-[60%] xl:max-w-[70%] flex flex-col xl:items-center gap-10">
                    <h3 class="text-2xl font-semibold font-serif ">Mejore su evaluación siguiendo las
                        recomendaciones
                    </h3>
                    <h2 v-if="selectedDimensionTab.name !== 'General'"
                        class="uppercase [letter-spacing:0.6em] text-2xl font-medium"> {{
                            selectedDimensionTab.name }}
                    </h2>
                </div>

                <!-- circulos decorativos de la carátula de la recomendación -->
                <div class="absolute rounded-full h-[610px] w-[620px] bg-transparent border border-yellow-200/60">
                </div>
                <div class="absolute rounded-full h-[640px] w-[620px] bg-transparent border border-yellow-200/60">
                </div>
                <div class="absolute rounded-full h-[660px] w-[620px] bg-transparent border border-yellow-200/60">
                </div>

            </picture>

            <div class="flex-1 flex flex-col gap-6 justify-between h-full">
                <div class="flex flex-col gap-4">
                    <h3 class="text-primary-900 font-semibold"> RECOMENDADO</h3>
                    <h2 class="font-bold text-lg"> Todo lo que debe saber sobre los procesos de su organización</h2>
                    <div class="flex flex-col gap-3">
                        <h4 class="text-sm font-semibold text-black/30">Información adicional</h4>
                        <p class="text-sm text-black/70 transition-all duration-500"> {{
                            selectedDimensionTab.description }}</p>
                    </div>
                </div>
                <slot name="download-button"></slot>
            </div>
        </div>

    </Card>

</template>
