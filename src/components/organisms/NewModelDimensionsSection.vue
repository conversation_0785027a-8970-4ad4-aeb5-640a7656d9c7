<script setup lang="ts">
import { useDimensions } from '@/hooks/useDimensions';
import Button from '../atoms/Button.vue';
import { ref } from 'vue';
import DimensionsWhitQuestionAudit from './DimensionsWhitQuestionAudit.vue';
import EvidenceTile from '../atoms/EvidenceTile.vue';
import type { Evidence } from '@/models/types';
import 'vue-toast-notification/dist/theme-sugar.css';
import { useToast } from 'vue-toast-notification';

const props = defineProps({
    onDimensionsUploaded: {
        type: Function,
        required: false
    }
})

const $toast = useToast();
const { handleDimensionsFileUpload, dimensions, actualDimension } = useDimensions();
const fileInputRef = ref<HTMLInputElement | null>(null);

const handleClick = () => {
    fileInputRef.value?.click();
};

const handleFileChange = async (event: Event) => {
    try {
        await handleDimensionsFileUpload(event);
        if (dimensions.value.length > 0 && props.onDimensionsUploaded) {
            const file = (event.target as HTMLInputElement).files?.[0];
            props.onDimensionsUploaded(file, dimensions.value);
        }
    } catch (error: any) {
        $toast.error('Error al importar dimensiones: ' + error.message);
    }
};

</script>

<template>
    <div class="flex flex-col gap-6">
        <div class="flex items-center w-full">
            <h3 class="pr-8 font-semibold text-gray-600">PREGUNTAS Y DIMENSIONES</h3>
            <div class="h-[1px] bg-black/10 flex-1"></div>
            <button class="pl-8 text-primary-800 font-bold">+ Importar Encuesta</button>
        </div>
        <div v-if="dimensions.length <= 0" class="flex items-center justify-center h-60">
            <Button @click="handleClick" class="w-60">Importar Encuesta</Button>
            <input type="file" ref="fileInputRef" @change="handleFileChange" style="display: none;" />
        </div>
        <DimensionsWhitQuestionAudit v-else v-model="actualDimension" :selectable="true" :dimensions="dimensions">
            <template #title>
                <h3 class="text-xl font-bold">Dimensiones a evaluar</h3>
            </template>
            <template #evidences="{ evidence }">
                <EvidenceTile :evidence="(evidence as Evidence)" :onEvidenceDelete="() => { }" />
            </template>
        </DimensionsWhitQuestionAudit>
    </div>

</template>