<script setup lang="ts">
import { getResponsesByDimension } from '@/services/api/dimensionsService';
import DonutChart from '../atoms/DonutChart.vue';
import { ref } from 'vue';

const props = defineProps({
    actualProcess: {
        type: Object,
        required: true
    },
    currentStep: {
        type: Number,
        required: true
    }
})


const responsesByDimension = ref(getResponsesByDimension(props.actualProcess.id, props.currentStep === 3));


</script>

<template>

    <h3 class="font-semibold text-lg">Respuestas conseguidas</h3>
    <div class="flex w-full justify-between items-center 2xl:px-8 px-0 flex-shrink-0">
        <DonutChart v-for="response in responsesByDimension" :key="response.dimension.id" :size="120"
            :percentage="response.responsePercentage" :color="response.dimension.color"
            :title="`${response.dimension.name}`" />

    </div>


</template>