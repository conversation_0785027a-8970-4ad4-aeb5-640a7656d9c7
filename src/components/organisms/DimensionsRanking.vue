<script setup lang="ts">
import { useAdmins } from '@/hooks/useAdmin';
import Card from '../atoms/Card.vue';
import { ref, watch } from 'vue';
import { useAuth } from '@/hooks/useAuth';
import type { DashboardKPIStats } from '@/models/types';
import RowSkeleton from '../templates/RowSkeleton.vue';
import { Logger } from '@/services/logger';

const colors = ['#FFAB2D', '#4E36E2', '#6EB4D2', '#2AA88A', '#C860AB', '#F84646'];

interface DimensionAverage {
    total: number;
    count: number;
    average: number;
}

const { dashboardGetStats } = useAdmins();
const { authData } = useAuth();

const isLoading = ref(true);
let dimensionAveragesMap: Record<string, DimensionAverage> = {};
let statsDimensions = ref<DashboardKPIStats | null>(null);

const fetchDashboardStatsData = async () => {
    try {
        const apiData = await dashboardGetStats();

        if (apiData && apiData.allDimensionLevels) {
            statsDimensions.value = apiData;
            apiData.allDimensionLevels.forEach(dimension => {
                dimensionAveragesMap[dimension.name] = {
                    total: 1, 
                    count: 1,
                    average: dimension.average || 1
                };
            });
            Logger.info("Data successfully obtained from the API");
        } else {
            Logger.error("API did not return expected data");
            assignDefaultValues();
        }
    } catch (error) {
        Logger.error("Error getting dashboard data", error);
        assignDefaultValues();
    } finally {
        isLoading.value = false;
        Logger.info("The process of obtaining data from the dashboard is complete");
    }
};

const assignDefaultValues = () => {
    Logger.info("Assigning default values ​to dimensions");
    const defaultDimensions = [
        'Rendición de Cuentas',
        'Comunicación',
        'Adquisición de bienes',
        'Adquisición de servicios',
        'Contratación de personal',
        'Institucionalización'
    ];

    defaultDimensions.forEach(dimension => {
        dimensionAveragesMap[dimension] = {
            total: 1, 
            count: 1,
            average: 1
        };
    });
    Logger.debug("Default values ​​assigned", dimensionAveragesMap);
};

const getIconClassForDimension = (dimension: string) => {
    const iconsByDimension = {
        'Rendición de Cuentas': 'fa-regular fa-handshake',
        'Comunicación': 'fa-regular fa-comment',
        'Adquisición de bienes': 'fa-solid fa-cart-shopping',
        'Adquisición de servicios': 'fa-solid fa-file-contract',
        'Contratación de personal': 'fa-solid fa-user-plus',
        'Institucionalización': 'fa-solid fa-building-columns',
    } as Record<string, string>;

    return iconsByDimension[dimension] || 'fa-solid fa-question';
};

const translateDimension = (dimension: string): string => {
    const translations = {
        "Rendición de Cuentas": "Rend. de Cuentas",
        "Comunicación": "Comunicación",
        "Adquisición de bienes": "Adq. de Bienes",
        "Adquisición de servicios": "Adq. de Servicios",
        "Contratación de personal": "C. Personal",
        "Institucionalización": "Institucionalización",
    } as Record<string, string>;

    return translations[dimension];
};

watch(
  authData,
  async (newValue) => {
    if (newValue) {
      try {
        await fetchDashboardStatsData();
      } catch (error) {
        Logger.error("Error getting dashboard data after authentication", error);
      }
    } else {
      Logger.error('Error: authData is not available.');
    }
  },
  { immediate: true }
);

</script>

<template>
    <div v-if="isLoading">
        <RowSkeleton :count="5" />
    </div>
    <section v-else-if="Object.keys(dimensionAveragesMap).length > 0" class="flex w-full flex-col gap-6">
        <h3 class="text-lg font-semibold">Dimensiones clasificadas de menor a mayor</h3>
        <div class="flex w-full gap-6">
            <Card v-for="(dimension, index) in Object.keys(dimensionAveragesMap)" 
                  :key="dimension" 
                  :decorationColor="colors[index % colors.length]" 
                  class="flex-1 min-w-0 flex flex-col justify-between gap-14 rounded-xl group hover:bg-gray-100 transition duration-300">
                <span class="flex flex-col gap-2">
                    <h3 class="text-sm text-black/70">Nivel promedio</h3>
                    <h2 class="text-3xl font-bold"> {{ dimensionAveragesMap[dimension].average }}</h2>
                </span>

                <div class="flex flex-col h-[35%] justify-start">
                    <span class="flex items-center gap-3 w-full">
                        <i class="font-semibold text-2xl" :class="getIconClassForDimension(dimension)"
                            :style="`color: ${colors[index % colors.length]}`"></i>
                        <h2 class="text-sm font-semibold whitespace-nowrap overflow-hidden text-ellipsis group-hover:whitespace-normal group-hover:overflow-visible"> 
                            {{ translateDimension(dimension) }}
                        </h2>
                    </span>
                </div>
            </Card>
        </div>
    </section>
    <div v-else>
        <p>No hay datos disponibles para mostrar.</p>
    </div>
</template>

<style scoped>
.group:hover h2 {
  white-space: normal;
  overflow: visible;
}
</style>
