<script setup lang="ts">
import Card from '@/components/atoms/Card.vue'
import Button from '@/components/atoms/Button.vue'
import { useAuth } from '@/hooks/useAuth'
import { useProcesses } from '@/hooks/process/useProcesses'
import { onMounted, ref } from 'vue'
import type { Process } from '@/models/types'
import { useAsyncFetch } from '@/hooks/useAsyncFetch'

/**
 * esto está hardcoded, pero debería ser extraído de los modelos de madurez disponibles
 * y al no estar éstos definidos actualmente (tienen datos de prueba), no se puede hacer
 */
const actualProcess = ref<Process>()
const { authData } = useAuth()
const { handleGetActualProcessByInstitutionID } = useProcesses()

const fetchActualProcess = async () => {
  actualProcess.value = await handleGetActualProcessByInstitutionID(
    authData.value!.simpleInstitutionDTO!.id
  ) ?? undefined;
}

const { fetchData } = useAsyncFetch(fetchActualProcess)

onMounted(async () => {
  await fetchData()
})
</script>

<template>
  <div class="flex h-full w-full">
    <Card v-if="!actualProcess" class="flex flex-col gap-5 w-full h-min">
      <div class="flex w-full items-center justify-between">
        <h3 class="text-xl font-bold">Proceso de Autodiagnóstico General</h3>
        <Button variant="primary">
          <router-link :to="{ path: '/gestor/requestProcess/new' }"> Solicitar </router-link>
        </Button>
      </div>
      <span class="font-semibold text-primary-800"> e-Transparencia</span>
      <p class="flex flex-col gap-4 text-sm">
        <span class="text-lg font-bold">Descripción del proceso</span>
        El proceso de autodiagnóstico en nuestra aplicación web permite a las instituciones evaluar
        su desempeño en diversas áreas mediante una serie de pasos intuitivos. Este proceso abarca
        seis dimensiones: Rendición de Cuentas, Adquisición de Bienes, Adquisición de Servicios,
        Comunicación, Contratación de Personal e Institucionalización. Cada dimensión requiere la
        presentación de evidencias para ser aprobada, y estas evidencias deben cumplir con criterios
        de aprobación específicos relacionados con cada área.
      </p>
      <div class="flex flex-col gap-2">
        <span class="flex items-center gap-5 font-semibold text-base">
          <i class="fa-solid fa-check text-green-600 text-xl"></i>
          <p class="border-b border-black/10 py-3 w-[30%]">6 dimensiones</p>
        </span>
        <span class="flex items-center gap-5 font-semibold text-base">
          <i class="fa-solid fa-check text-green-600 text-xl"></i>
          <p class="border-b border-black/10 py-3 w-[30%]">54 preguntas</p> </span
        ><span class="flex items-center gap-5 font-semibold text-base">
          <i class="fa-solid fa-check text-green-600 text-xl"></i>
          <p class="border-b border-black/10 py-3 w-[30%]">18 evidencias</p>
        </span>
      </div>
    </Card>
  </div>
</template>
