<script setup lang="ts">
import GridSkeleton from '../templates/GridSkeleton.vue';


defineProps({
    items: {
        type: Array,
        required: true
    },
    emptyMessage: {
        type: String,
        default: 'No se encontraron elementos.'
    },
    isLoading: {
        type: <PERSON><PERSON>an,
        default: false
    }
})

</script>

<template>
    <GridSkeleton v-if="isLoading" :count="6"></GridSkeleton>
    <div v-else class="grid 2xl:grid-cols-3 xl:grid-cols-2 gap-8">
        <template v-if="items.length > 0">
            <template v-for="item in items" :key="item.id">
                <slot :item="item"></slot>
            </template>
        </template>
        <p v-else class="ml-4 mt-4 text-black/70">
            {{ emptyMessage }}
        </p>
    </div>
</template>