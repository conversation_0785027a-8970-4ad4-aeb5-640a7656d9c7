<script setup lang="ts">
import Card from '../atoms/Card.vue';


defineProps({
    count: {
        type: Number,
        required: true
    }
})

</script>

<template>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <Card role="status" v-for="index in Array.from({ length: count }, (_, i) => i + 1)" :key="index"
            class="flex justify-between bg-gray-200 shadow-sm animate-pulse h-36">
            <div class="h-full flex flex-col justify-center rounded-full">
                <div class="bg-gray-300 h-12 w-12 rounded-xl"></div>
            </div>
            <div class="flex flex-col h-full gap-y-10 justify-center w-10 text-right text-white">
                <div class="w-full h-3 bg-gray-300 rounded-full"></div>
                <div class="w-full h-2 bg-gray-300 rounded-full"></div>
            </div>
        </Card>
    </div>
</template>