<script setup lang="ts">
import Card from '../atoms/Card.vue';
import TextSkeleton from './TextSkeleton.vue';

</script>

<template>
    <div class="flex w-full h-full gap-10 animate-pulse">
        <Card class="flex flex-col h-full w-[30%] bg-gray-200 gap-8">
            <TextSkeleton class="w-full" />
            <div class="h-3"></div>
            <ul class="flex flex-col gap-10">
                <li class="flex items-center gap-4 w-[80%] h-4" v-for="n in Array.from({ length: 6 }, (_, i) => i + 1)"
                    :key="n">
                    <span class="h-7 w-7 rounded-lg bg-gray-300">
                    </span>
                    <div class="flex flex-col gap-2 w-full">
                        <span class="w-full h-1.5 rounded-lg bg-gray-300"></span>
                        <span class="w-[40%] h-1 rounded-lg bg-gray-400"></span>
                    </div>

                </li>
            </ul>
            <div class="h-3"></div>
            <div class="flex w-full gap-2 flex-wrap items-center">
                <div class="w-[20%] h-1.5 bg-gray-300 rounded-full"></div>
                <div class="w-[30%] h-1.5 bg-gray-300 rounded-full"></div>
                <div class="w-[35%] h-1.5 bg-gray-300 rounded-full"></div>
                <div class="w-[80%] h-1.5 bg-gray-300 rounded-full"></div>
                <div class="w-[30%] h-1.5 bg-gray-300 rounded-full"></div>
            </div>
        </Card>
        <Card class="flex flex-col gap-8 h-full w-[70%] bg-gray-200">
            <TextSkeleton class="w-full" />
            <div class="h-3"></div>
            <span class="w-[30%] h-1 rounded-lg bg-gray-300"></span>
            <span class="w-[10%] h-2 rounded-lg bg-gray-400"></span>
            <ul class="flex w-full gap-6">
                <li class="h-16 w-60 bg-gray-300 rounded-lg"></li>
                <li class="h-16 w-60 bg-gray-300 rounded-lg"></li>
                <li class="h-16 w-60 bg-gray-300 rounded-lg"></li>
            </ul>
        </Card>
    </div>

</template>