<script setup lang="ts">
defineProps({
    count: {
        type: Number,
        required: true
    }
})

</script>

<template>
    <ul class="animate-pulse flex flex-col gap-4">
        <li v-for="n in Array.from({ length: count }, (_, i) => i + 1)" :key="n" class="flex gap-6 items-center">
            <div class="flex w-8 h-8 rounded-full bg-gray-300"></div>
            <div class="flex flex-col w-full gap-2">
                <span class="w-10 h-1 bg-gray-400 rounded-lg"></span>
                <span class="w-[80%] h-1.5 bg-gray-300 rounded-lg"></span>
            </div>
        </li>
    </ul>
</template>