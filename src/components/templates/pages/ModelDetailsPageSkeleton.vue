<script setup lang="ts">
import TextSkeleton from '../TextSkeleton.vue';
import Card from '@/components/atoms/Card.vue';
</script>

<template>
    <Card class="flex flex-col justify-between animate-pulse h-80 bg-gray-200 shadow-sm">
        <div class="flex w-full items-center justify-between">
            <TextSkeleton />
            <div class="bg-gray-300 h-10 rounded-full w-40"></div>
        </div>

        <div class="bg-gray-300 w-80 h-4 rounded-full"></div>

        <div class="flex flex-col gap-3.5">
            <span class="bg-gray-300 h-2 rounded-full w-28"></span>
            <span class="bg-gray-300 h-2 rounded-full w-28"></span>
            <span class="bg-gray-300 h-2 rounded-full w-28"></span>
        </div>
    </Card>
</template>