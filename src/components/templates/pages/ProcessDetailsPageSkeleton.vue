<script setup lang="ts">
import Card from '@/components/atoms/Card.vue';
import TextSkeleton from '../TextSkeleton.vue';
import ListSkeleton from '../TableListSkeleton.vue';
</script>

<template>
    <div class="flex h-full flex-col gap-10">
        <Card class="flex flex-col justify-between gap-14 animate-pulse h-80 bg-gray-200 shadow-sm">

            <div class="bg-gray-300 w-40 h-2 rounded-full"></div>
            <div class="flex w-full justify-between px-10">
                <span class="flex justify-center items-center bg-gray-300 h-24 w-24 rounded-full"
                    v-for="n in Array.from({ length: 6 }, (_, i) => i + 1)" :key="n">
                    <span class="flex bg-white h-16 w-16 rounded-full"></span>

                </span>
            </div>

            <div class="flex w-full items-center justify-between">
                <TextSkeleton />
                <div class="bg-gray-300 h-10 rounded-full w-40"></div>
            </div>
        </Card>
        <div class="flex flex-1 w-full gap-10">
            <Card class="flex flex-col justify-between animate-pulse bg-gray-200 shadow-sm flex-1">
                <div class="flex w-full items-center justify-between">
                    <TextSkeleton />
                    <div class="bg-gray-300 h-10 rounded-full w-40"></div>
                </div>

                <div class="bg-gray-300 w-80 h-4 rounded-full"></div>

                <div class="flex flex-col gap-3.5">
                    <span class="bg-gray-300 h-2 rounded-full w-28"></span>
                    <span class="bg-gray-300 h-2 rounded-full w-28"></span>
                    <span class="bg-gray-300 h-2 rounded-full w-28"></span>
                </div>
            </Card>
            <Card class="flex flex-col gap-10 justify-between animate-pulse bg-gray-200 shadow-sm flex-1">
                <div class="flex w-full items-center justify-center">
                    <div class="bg-gray-300 h-3 rounded-full w-40"></div>
                </div>

                <ListSkeleton :count="2" />
            </Card>
        </div>
    </div>
</template>