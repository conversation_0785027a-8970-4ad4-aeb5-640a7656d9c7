<script setup lang="ts">
import Card from '@/components/atoms/Card.vue';
</script>

<template>
    <Card class="flex h-96 bg-gray-200 animate-pulse">
        <div class="flex flex-col gap-7 w-full justify-center items-center">
            <div class="h-40 w-40 rounded-full bg-gray-300"></div>
            <div class="flex flex-col h-10 w-full items-center gap-2">
                <div class="flex w-full gap-4 items-center justify-center">
                    <span v-for="n in Array.from({ length: 6 }, (_, i) => i + 1)" :key="n"
                        class="w-12 h-12 bg-gray-300 rounded-full">
                    </span>
                </div>

            </div>
        </div>
        <div class="h-full w-1 bg-gray-100 mx-20"></div>
        <div class="flex flex-col w-full gap-10">
            <div class="w-40 h-2 bg-gray-300 rounded-full"></div>
            <ul class="flex flex-col gap-4">
                <li v-for="n in Array.from({ length: 4 }, (_, i) => i + 1)" :key="n" class="flex gap-6 items-center">
                    <div class="flex w-8 h-8 rounded-full bg-gray-300"></div>
                    <div class="flex flex-col w-full gap-2">
                        <span class="w-10 h-1 bg-gray-400 rounded-lg"></span>
                        <span class="w-[80%] h-1.5 bg-gray-300 rounded-lg"></span>
                    </div>
                </li>
            </ul>
        </div>

    </Card>
</template>