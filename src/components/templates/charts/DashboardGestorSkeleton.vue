<script setup lang="ts">
import Card from '@/components/atoms/Card.vue';
import SkeletonArcChart from '@/components/atoms/SkeletonArcChart.vue';
import SkeletonSurveysChart from '@/components/atoms/SkeletonSurveysChart.vue';
import SkeletonRealTimeChart from '@/components/atoms/SkeletonRealTimeChart.vue';
import SkeletonBarChart from '@/components/atoms/SkeletonBarChart.vue';
</script>

<template>
 <section  class="flex flex-col w-full gap-5 py-8">
    <section class="flex w-full gap-5 flex-wrap">
      <Card class="flex flex-col h-full flex-1 min-w-[300px] aspect-[16/11]">
        <SkeletonArcChart />
      </Card>
      <Card class="flex flex-col h-full flex-1 min-w-[300px] aspect-[16/11]">
        <SkeletonSurveysChart />
      </Card>
    </section>
    <section class="flex w-full gap-5 flex-wrap">
      <Card class="flex flex-col h-full flex-1 min-w-[300px] aspect-[16/11]">
        <SkeletonRealTimeChart />
      </Card>
      <Card class="flex flex-col h-full flex-1 min-w-[300px] aspect-[16/11]">
        <SkeletonBarChart />
      </Card>
    </section>
  </section>
</template>


<style scoped>
.dots-animation span {
    display: inline-block;
    animation: bounce 1.5s infinite;
    font-size: 1.25rem;
}

.dots-animation span:nth-child(2) {
    animation-delay: 0.2s;
}

.dots-animation span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes bounce {

    0%,
    80%,
    100% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-8px);
    }
}
</style>