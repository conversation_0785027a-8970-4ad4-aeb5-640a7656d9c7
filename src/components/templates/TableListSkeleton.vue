<script setup lang="ts">

defineProps({
    count: {
        type: Number,
        required: true
    }
})

</script>

<template>
    <section class="flex flex-col gap-8 w-full h-full border border-gray-200 rounded-xl p-4">
        <div role="status" v-for="n in Array.from({ length: count }, (_, i) => i + 1)" :key="n"
            class="flex items-center justify-between animate-pulse border-b border-gray-300 px-3 py-5">
            <div>
                <div class="h-2.5 bg-gray-300 rounded-full w-24 mb-2.5"></div>
                <div class="w-32 h-2 bg-gray-300 rounded-full d"></div>
            </div>
            <div class="h-2.5 bg-gray-300 rounded-full  w-12"></div>
        </div>
    </section>
</template>