<script setup lang="ts">
import Card from '../atoms/Card.vue';


defineProps({
    count: {
        type: Number,
        required: true
    }
})

</script>


<template>
    <section class="grid 2xl:grid-cols-3 xl:grid-cols-2 gap-8 h-min w-full">
        <Card role="status" class="bg-gray-200 animate-pulse flex flex-col gap-8 max-h-min shadow-sm"
            v-for="n in Array.from({ length: count }, (_, i) => i + 1)" :key="n">
            <div class="flex flex-col gap-4 w-full">
                <div class="flex items-center justify-between w-full">
                    <div class="h-2 w-16 bg-gray-300 rounded-full mb-2"></div>
                    <div class="rounded-lg h-12 w-12 bg-gray-300"></div>
                </div>
                <div>
                    <div class="h-4 w-1/2 bg-gray-300 rounded-full mb-2"></div>
                    <div class="h-2 w-1/3 bg-gray-300 rounded-full mb-2"></div>
                </div>
            </div>
            <div class="flex w-full items-center justify-between">
                <div class="flex gap-4 items-center">
                    <div class="h-10 w-20 rounded-full bg-gray-300"></div>

                </div>
                <div class="w-20 h-2 rounded-full bg-gray-300"></div>

            </div>
            <span class="sr-only">Loading...</span>
        </Card>

    </section>
</template>