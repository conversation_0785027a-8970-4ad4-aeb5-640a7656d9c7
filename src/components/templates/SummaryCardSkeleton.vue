<script setup lang="ts">
import Card from '../atoms/Card.vue';
import TextSkeleton from './TextSkeleton.vue';
</script>

<template>

    <Card class="flex flex-col justify-between animate-pulse bg-gray-200 shadow-sm flex-1">
        <div class="flex w-full items-center justify-between">
            <TextSkeleton />
            <div class="bg-gray-300 h-10 rounded-full w-40"></div>
        </div>

        <div class="bg-gray-300 w-80 h-4 rounded-full"></div>

        <div class="flex flex-col gap-3.5">
            <span class="bg-gray-300 h-2 rounded-full w-28"></span>
            <span class="bg-gray-300 h-2 rounded-full w-28"></span>
            <span class="bg-gray-300 h-2 rounded-full w-28"></span>
        </div>
    </Card>
</template>