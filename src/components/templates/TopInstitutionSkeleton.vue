<script setup lang="ts">
import Card from '../atoms/Card.vue';
defineProps({
  count: {
    type: Number,
    default: 0
  }
})
</script>

<template>
  <Card class="flex-1 flex flex-col gap-8 bg-gray-200 shadow-sm animate-pulse" v-for="n in count" :key="n">
    <div class="flex w-full justify-center">
      <span class="flex items-center justify-center h-16 w-16 relative bg-gray-300 rounded-full"></span>
    </div>

    <h3 class="font-semibold text-black/80 text-transparent bg-gray-300 w-24 h-4 rounded mx-auto"></h3>
    <div class="flex gap-2 items-center text-black/60 text-xs justify-center">
      <i class="fas fa-location-dot text-gray-300"></i>
      <p class="text-transparent bg-gray-300 w-32 h-3 rounded"></p>
    </div>
  </Card>
</template>

<style scoped>
.animate-pulse {
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.6;
  }
}
</style>
