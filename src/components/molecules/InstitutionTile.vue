<script setup lang="ts">
import { ref } from 'vue';
import type { Institution } from '@/models/types';
import Button from '../atoms/Button.vue';
import IconButton from '../atoms/IconButton.vue';
import Tooltip from '../atoms/Tooltip.vue';
import { RouterLink } from 'vue-router';
import 'vue-toast-notification/dist/theme-sugar.css';
import { useToast } from 'vue-toast-notification';
const $toast = useToast();
defineProps({
    institution: {
        type: Object as () => Institution,
        required: true
    }
})
const copiedMessage = ref('');

const copyToClipboard = async (text: string) => {
    try {
        await navigator.clipboard.writeText(text);
        $toast.success('¡Copiado al portapapeles!');
        setTimeout(() => {
            copiedMessage.value = '';
        }, 2000);
    } catch (err) {
        copiedMessage.value = 'Error al copiar';
        setTimeout(() => {
            copiedMessage.value = '';
        }, 2000);
    }
}
</script>

<template>
    <tr class="odd:bg-white even:bg-gray-50 border-b">
        <!-- nombre y logo -->
        <th scope="row" class="px-6 py-4">
            <div class="flex items-center gap-3">
                <span class="flex text-white items-center justify-center w-9 h-9 rounded-md"
                    :style="{ backgroundColor: institution.color }">
                    {{ institution.acronym }}
                </span>
                {{ institution.name }}
            </div>
        </th>
        <!-- procesos -->
        <td class="px-6 py-4">{{ institution.nprocesses }}</td>
        <!-- nivel general -->
        <td class="px-6 py-4"> {{ institution.level }}</td>
        <!-- funcionarios -->
        <td class="px-6 py-4">{{ institution.nemployees }}</td>
        <!-- auditor -->
        <td class="px-6 py-4">{{ institution.auditor?.username ?? 'No Aplica' }}</td>
        <!-- contacto -->
        <td class="px-6 py-4">
            <span class="flex items-center gap-3">
                <Tooltip :text="institution.phoneNumber">
                    <IconButton 
                        icon="fa-solid fa-phone" 
                        @click="copyToClipboard(institution.phoneNumber)" />
                </Tooltip>
                <!-- WebSite?-->
                <Tooltip :text="institution.website">
                    <IconButton 
                        icon="fa-solid fa-globe" 
                        @click="copyToClipboard(institution.website)" />
                </Tooltip>
            </span>
            <div v-if="copiedMessage" class="text-sm text-green-500 mt-1">{{ copiedMessage }}</div>
        </td>
        <!-- acciones -->
        <td class="px-6 py-4">
            <Button variant="secondary">
                <RouterLink :to="{
                    path: `/admin/institutions/${institution.id}`
                }">
                    Ver detalle
                </RouterLink>
            </Button>
        </td>
    </tr>
</template>
