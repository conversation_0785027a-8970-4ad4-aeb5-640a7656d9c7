<script setup lang="ts">
import { useAuth } from '@/hooks/useAuth';
import { extractInitials } from '@/helpers/stringHelpers';
import { ref, watch } from 'vue';
import { useAuditors } from '@/hooks/useAuditors';
import { useInstitutions } from '@/hooks/useInstitutions';
import type { Auditor, Institution } from '@/models/types';
import { Logger } from '@/services/logger';
const userAuditor = ref<Auditor>()
const userInstitution = ref<Institution>()
const { handleGetAuditorByID } = useAuditors()
const { handleGetInstitutionByManagerID } = useInstitutions()
const fetchUserData = async (userId: number) => {
    try {
        Logger.info('Fetching auditor data', { userId });
        const apiData = await handleGetAuditorByID(userId);
        if (apiData) {
            userAuditor.value = apiData;
            Logger.info('Auditor data fetched successfully', apiData);
        } else {
            Logger.warn('No auditor data found', { userId });
        }
    } catch (error) {
        Logger.error('Error fetching auditor data', error);
    }
};
const fetchGestorData = async (userId: number) => {
    try {
        const apiData = await handleGetInstitutionByManagerID(userId);
        if (apiData) {
            userInstitution.value = apiData;
        } else {
            Logger.warn('No institution data found', { userId });
        }
    } catch (error) {
        Logger.error('Error fetching institution data', error);
    }
};
const { authData, logout } = useAuth();

watch(
    authData,
    async (newValue) => {
        if (!newValue) {
            Logger.warn('Auth data is missing or undefined');
            return;
        }

        const userId = newValue.id;
        const userRole = newValue.role;

        if (userRole === 'auditor') {
            await fetchUserData(userId);
        } else if (userRole === 'gestor') {
            await fetchGestorData(userId);
        } else {
            Logger.warn('Unknown user role', { userRole });
        }
    },
    { immediate: true }
);
</script>
<template>
    <div class="inline-flex justify-end bg-white p-3 2xl:h-16 h-10 rounded-lg shadow-md">
        <div class="flex flex-row items-center gap-3">
            <div class="flex flex-col items-end">
                <div class="font-semibold text-sm text-gray-700 whitespace-nowrap">{{ authData?.username }}</div>
                <div class="text-xs text-gray-500 cursor-pointer whitespace-nowrap" @click="logout">Cerrar sesión</div>
            </div>
            <span class="2xl:h-10 2xl:w-10 h-7 w-7 rounded-lg bg-primary-700 text-white flex justify-center items-center">
                {{ extractInitials(authData?.username ?? "") }}
            </span>
        </div>
    </div>
</template>
