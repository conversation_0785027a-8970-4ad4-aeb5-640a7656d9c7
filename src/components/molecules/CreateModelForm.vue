<script setup lang="ts">
import useVuelidate from '@vuelidate/core';
import Input from '../atoms/Input.vue';
import { createModelRules } from '@/models/validation/formRules';
import type { MaturityModelFormSchema } from '@/models/validation/formSchemas';

const modelFormData = defineModel<MaturityModelFormSchema>();
const $vModelFormData = useVuelidate(createModelRules, modelFormData.value!, { $scope: false });

const validateForm = async () => {
    return await $vModelFormData.value.$validate();
}

defineExpose({
    validateForm,
});

</script>

<template>
    <div class="flex flex-col gap-8">
        <div class="flex items-center w-full">
            <h3 class="pr-8 font-semibold text-gray-600">GENERAL</h3>
            <div class="h-[1px] bg-black/10 w-full"></div>
        </div>
        <div class="flex w-full gap-10">
            <div class="flex flex-col gap-3">
                <span class="font-semibold text-black/50">Nombre del modelo</span>
                <Input v-model="modelFormData!.name" class="w-80" />
            </div>
            <div class="flex flex-col gap-3">
                <span class="font-semibold text-black/50">Contexto de evaluación</span>
                <Input v-model="modelFormData!.evaluationContext" class="w-80" />
            </div>
        </div>
        <div class="flex flex-col gap-3.5">
            <span class="font-semibold text-black/50">Describa de manera extendida las aristas que evalúa este
                modelo</span>
            <textarea v-model="modelFormData!.description"
                class="w-full h-40 p-4 border border-black/30 rounded-xl resize-none outline-none focus:border-primary-600 transition-all duration-200"
                placeholder="Escriba aquí..."></textarea>
        </div>
    </div>

</template>