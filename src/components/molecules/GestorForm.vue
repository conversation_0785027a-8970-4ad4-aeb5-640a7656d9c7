<script setup lang="ts">
import Input from '../atoms/Input.vue';
import Button from '../atoms/Button.vue';
import { createGestorRules } from '@/models/validation/formRules';
import { formatPhoneNumber } from '@/helpers/formatters';
import { formatRUT  } from '@/helpers/formatters/rutFormatters';
import type { GestorFormSchema } from '@/models/validation/formSchemas';
import { useForm } from '@/hooks/useForm';
import { toRef, reactive, ref } from 'vue';

const props = defineProps({
    update: {
        type: Boolean,
        default: false
    },
    savedInstitutionData: {
        type: Object as () => GestorFormSchema,
        required: false
    },
    onNext: {
        type: Function,
        required: true
    }
});
const showPassword = ref(false)
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}
const cleanRut = (rut: string): string => {
  return rut.replace(/\./g, "").toUpperCase();
};
const emit = defineEmits(['prev']);
const originalData = reactive({
    ...props.savedInstitutionData,
    rut: formatRUT(props.savedInstitutionData?.rut ?? "")
});
const { formData: gestorData, $vForm: $vCreateGestor, handleSubmit, isLoading, hasChanges } = useForm(
    {
        initialDataRef: toRef(originalData),
        validationRules: createGestorRules(props.update),
        updateCondition: toRef(props.update),
        onSubmit: async (data) => {
            const newData = { ...data };
            if (newData.rut) {
                newData.rut = cleanRut(newData.rut);
            }
            const changedData: Partial<GestorFormSchema> = {};
            Object.keys(newData).forEach((key) => {
                if (newData[key as keyof GestorFormSchema] !== originalData[key as keyof GestorFormSchema]) {
                    changedData[key as keyof GestorFormSchema] = newData[key as keyof GestorFormSchema];
                }
            });
            await props.onNext(changedData);

            window.location.reload();
        }
    }
);

const handleBack = () => {
    emit('prev');
}

</script>


<template>
    <form @submit.prevent="handleSubmit" class="flex flex-col h-full w-full items-center justify-between px-4 py-8">
  <div class="w-full max-w-[800px] flex flex-col gap-8">
    <div class="grid grid-cols-[auto_1fr] items-center gap-6">
      <label class="font-semibold text-black/50 w-64">Nombre de Gestor Institucional</label>
      <Input :errorMessage="$vCreateGestor.username.$errors[0]?.$message.toString()" v-model="gestorData.username" />
    </div>
    <div class="grid grid-cols-[auto_1fr] items-center gap-6">
      <label class="font-semibold text-black/50 w-64">Email de Gestor Institucional</label>
      <Input :errorMessage="$vCreateGestor.email.$errors[0]?.$message.toString()" v-model="gestorData.email" />
    </div>
    <div class="grid grid-cols-[auto_1fr] items-center gap-6">
      <label class="font-semibold text-black/50 w-64">Teléfono de Gestor Institucional</label>
      <Input :formatter="formatPhoneNumber" :errorMessage="$vCreateGestor.phoneNumber.$errors[0]?.$message.toString()" v-model="gestorData.phoneNumber" />
    </div>
    <div class="grid grid-cols-[auto_1fr] items-center gap-6">
      <label class="font-semibold text-black/50 w-64">Cargo de Gestor Institucional</label>
      <Input :errorMessage="$vCreateGestor.position.$errors[0]?.$message.toString()" v-model="gestorData.position" />
    </div>
    <div class="grid grid-cols-[auto_1fr] items-center gap-6">
      <label class="font-semibold text-black/50 w-64">Rut de Gestor Institucional</label>
      <Input :formatter="formatRUT" :errorMessage="$vCreateGestor.rut.$errors[0]?.$message.toString()" v-model="gestorData.rut" />
    </div>
    <div v-if="update" class="grid grid-cols-[auto_1fr] items-center gap-6">
      <label class="font-semibold text-black/50 w-64">Nueva Contraseña</label>
      <div class="flex flex-col w-full">
        <div class="relative w-full">
          <Input 
            v-model="gestorData.password" 
            :type="showPassword ? 'text' : 'password'" 
            class="w-full"
          />
          <button type="button" @click="togglePasswordVisibility" class="text-sm text-gray-500 absolute right-4 top-1/2 transform -translate-y-1/2 z-10">
            <span v-if="showPassword">Ocultar</span>
            <span v-else>Mostrar</span>
          </button>
        </div>
        <div v-if="$vCreateGestor.password?.$errors[0]?.$message" class="text-red-500 text-sm mt-1">
          {{ $vCreateGestor.password?.$errors[0]?.$message.toString() }}
        </div>
      </div>
    </div>

    <div v-if="update" class="grid grid-cols-[auto_1fr] items-center gap-6">
      <label class="font-semibold text-black/50 w-64">Confirmar Contraseña</label>
      <div class="flex flex-col w-full">
        <div class="relative w-full">
          <Input 
            v-model="gestorData.confirmPassword" 
            :type="showPassword ? 'text' : 'password'" 
            class="w-full"
          />
          <button type="button" @click="togglePasswordVisibility" class="text-sm text-gray-500 absolute right-4 top-1/2 transform -translate-y-1/2 z-10">
            <span v-if="showPassword">Ocultar</span>
            <span v-else>Mostrar</span>
          </button>
        </div>
        <div v-if="$vCreateGestor.confirmPassword?.$errors[0]?.$message" class="text-red-500 text-sm mt-1">
          {{ $vCreateGestor.confirmPassword?.$errors[0]?.$message.toString() }}
        </div>
      </div>
    </div>
    <div class="flex justify-between mt-8">
      <Button v-if="!update" @click="handleBack" variant="invert" class="w-40">Volver</Button>
      <div v-else class="w-40"></div>
      <Button :loading="isLoading" :disabled="$vCreateGestor.$errors.length > 0 || (update && !hasChanges)" type="submit" variant="primary" class="w-40">
        {{ update ? 'Actualizar' : 'Crear' }}
      </Button>
    </div>
  </div>
</form>

</template>