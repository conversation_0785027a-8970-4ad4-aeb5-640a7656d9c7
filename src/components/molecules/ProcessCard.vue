<script setup lang="ts">
import type { Process } from '@/models/types';
import Button from '../atoms/Button.vue';
import Card from '../atoms/Card.vue';
import { formatDateForChile } from '@/helpers/formatters';

defineProps({
    process: {
        type: Object as () => Process,
        required: true,
    },
    path: {
        type: String,
        required: true,
    },
    buttonText: {
        type: String,
        required: true,
    },
    disabled: {
        type: Boolean,
        default: false,
    },
});

</script>

<template>
    <Card class="flex flex-col gap-12 max-h-min">
        <div class="flex flex-col gap-4">
            <div class="flex w-full justify-between">
                <span class="flex flex-col gap-1.5">
                    <p class="text-sm font-semibold text-black/50">
                        {{ process.employees?.length === 0 ? 'Sin' :
                            process.employees?.length }} funcionarios</p>
                    <h3 class="font-semibold">{{ process.simpleInstitutionDTO.name }}</h3>
                </span>
                <span class="h-12 w-12 p-2 rounded-lg flex items-center justify-center font-semibold text-white"
                    :style="{ backgroundColor: process.simpleInstitutionDTO.color }">
                    {{ process.simpleInstitutionDTO.acronym }}
                </span>
            </div>

            <p class="text-sm font-semibold text-primary-700">
                {{ formatDateForChile(process.startDate, "/") }} - {{
                    formatDateForChile(process.endDate, "/") }}
            </p>
        </div>

        <div class="flex w-full justify-between items-center">
            <router-link v-if="!disabled" :to="`${path}?startDate=${process.startDate}&endDate=${process.endDate}`">
    <Button variant="secondary">
        {{ buttonText }}
    </Button>
</router-link>
            <Button v-else :disabled="true" variant="secondary">
                {{ buttonText }}
            </Button>
            <slot name="options"></slot>
            <span class="text-sm font-semibold text-black/60">
                {{ process.simpleInstitutionDTO.city }}, Chile
            </span>
        </div>
    </Card>
</template>