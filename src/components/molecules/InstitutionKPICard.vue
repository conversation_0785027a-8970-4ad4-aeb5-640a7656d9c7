<script setup lang="ts">
import Card from '../atoms/Card.vue';

interface InstitutionKPICardProps {
    label: string;
    value: string;
    icon: string;
}

defineProps<InstitutionKPICardProps>();


</script>

<template>
    <Card class="flex flex-col justify-between h-[14rem] 2xl:body-2 body-4 shadow-lg px-6">
        <div class="flex flex-col 2xl:gap-8 gap-2 items-center">
            <p class="text-base">
                {{ label }}
            </p>
            <h1 class="text-3xl font-black">{{ value }}</h1>
        </div>

        <div class="flex w-full gap-4 items-center">
            <i :class="icon" class="font-bold"></i>
            <slot></slot>
        </div>
    </Card>
</template>