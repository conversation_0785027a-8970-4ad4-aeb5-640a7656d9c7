<script setup lang="ts">
import { useAuth } from '@/hooks/useAuth';
import { ref, watch } from 'vue';
import { useAuditors } from '@/hooks/useAuditors';
import { useInstitutions } from '@/hooks/useInstitutions';
import type { Auditor, Institution } from '@/models/types';
import AuditorFormModal from '@/components/molecules/AuditorFormModal.vue';
import InstitutionFormModal from '@/components/molecules/InstitutionFormModal.vue';
import { Logger } from '@/services/logger';
const userAuditor = ref<Auditor>()
const userInstitution = ref<Institution>()
const { handleGetAuditorByID } = useAuditors()
const { handleGetInstitutionByManagerID } = useInstitutions()
const fetchUserData = async (userId: number) => {
  try {
    Logger.info('Fetching auditor data', { userId });
    const apiData = await handleGetAuditorByID(userId);
    if (apiData) {
      userAuditor.value = apiData;
      Logger.info('Auditor data fetched successfully', apiData);
    } else {
      Logger.warn('No auditor data found', { userId });
    }
  } catch (error) {
    Logger.error('Error fetching auditor data', error);
  }
};
const fetchGestorData = async (userId: number) => {
  try {
    const apiData = await handleGetInstitutionByManagerID(userId);
    if (apiData) {
      userInstitution.value = apiData;
    } else {
      Logger.warn('No institution data found', { userId });
    }
  } catch (error) {
    Logger.error('Error fetching institution data', error);
  }
};
const selectedAuditor = ref<Auditor>();
const isAuditorModalOpen = ref<boolean>(false);
const isModalOpen = ref(false);
const selectedInstitution = ref<Institution>();
const handleEditAuditor = (auditor: Auditor) => {
  selectedAuditor.value = auditor;
  isAuditorModalOpen.value = true;
}
const handleEditInstitution = (institution: Institution) => {
  selectedInstitution.value = institution;
  isModalOpen.value = true;
}
const handleCloseModal = () => {
  isAuditorModalOpen.value = false;
  selectedAuditor.value = undefined;
}
const handleInstitutionCloseModal = () => {
  isModalOpen.value = false;
  selectedInstitution.value = undefined;
}
const { authData } = useAuth();
const handleSettingsClick = () => {
  if (authData?.value?.role === 'AUDITOR' && userAuditor.value) {
    handleEditAuditor(userAuditor.value);
  } else if (authData?.value?.role === 'MANAGER' && userInstitution.value) {
    handleEditInstitution(userInstitution.value);
  } else {
    Logger.warn('No editable data available or user role is unknown');
  }
};
watch(
  authData,
  async (newValue) => {
    if (!newValue) {
      Logger.warn('Auth data is missing or undefined');
      return;
    }

    const userId = newValue.id;
    const userRole = newValue.role;

    if (userRole === 'AUDITOR') {
      await fetchUserData(userId);
    } else if (userRole === 'MANAGER') {
      await fetchGestorData(userId);
    } else {
      Logger.warn('Unknown user role', { userRole });
    }
  },
  { immediate: true }
);
</script>

<template>
  <div v-if="authData?.role === 'AUDITOR' || authData?.role === 'MANAGER'"
    @click="handleSettingsClick"
    class="2xl:h-16 h-10 2xl:w-16 w-10 rounded-lg shadow-lg bg-white relative flex items-center justify-center cursor-pointer">
    <i class="fa-solid fa-gear 2xl:text-3xl text-xl text-black/80"></i>
  </div>

  <AuditorFormModal v-model="selectedAuditor" :isModalOpen="isAuditorModalOpen" :enable="false"
    @close="handleCloseModal" />

  <InstitutionFormModal :institution="selectedInstitution" :isModalOpen="isModalOpen" :enable="false"
    :enableAuditor="false" @close="handleInstitutionCloseModal" />
</template>