<script setup lang="ts">
import { ref } from 'vue';
import Modal from '../atoms/Modal.vue';
import Button from '../atoms/Button.vue';
import Input from '../atoms/Input.vue';
import { formatRUT } from '@/helpers/formatters';
import { addEmployeeRules } from '@/models/validation/formRules';
import useVuelidate from '@vuelidate/core';
import { useEmployees } from '@/hooks/useEmployees';
const newEmployeeFormData = ref({
    name: '',
    email: '',
    rut: ''
})

const $vCreateEmployee = useVuelidate(addEmployeeRules, newEmployeeFormData, { $scope: false });
const {addEmployee} = useEmployees();
const props = defineProps({
    isOpen: {
        type: Boolean,
        required: true
    },
    onClose: {
        type: Function,
        required: true
    },
    onEmployeeAdded: {
        type: Function,
        required: true
    }
})

const handleEmployeeAdded = async () => {
    const result = await $vCreateEmployee.value.$validate();
    if (result) {
        props.onEmployeeAdded(newEmployeeFormData.value);
        newEmployeeFormData.value = {
            name: '',
            email: '',
            rut: ''
        }
        addEmployee(newEmployeeFormData.value);
        props.onClose();
    }
}
</script>

<template>
    <Modal :isOpen="isOpen" :onClose="onClose">
        <div class="flex flex-col gap-5 max-h-[80vh] overflow-y-auto p-4">
            <h2 class="flex flex-col gap-4 text-2xl mb-4 font-bold text-left">Añadir funcionario
                <span class="border-b border-black/10"></span>
            </h2>
            <form @submit.prevent="handleEmployeeAdded" class="flex flex-col justify-between flex-1">
                <div class="flex flex-col gap-6">
                    <div class="flex flex-col w-full">
                        <label for="name" class="font-semibold text-black/50 mb-2">Nombre</label><span v-if="$vCreateEmployee.name.$errors[0]" class="text-red-600"> EL nombre es requerido</span>
                        <Input :errorMessage="$vCreateEmployee.name.$errors[0]?.$message.toString()"
                            v-model="newEmployeeFormData.name" class="w-80" id="name" />
                    </div>

                    <div class="flex flex-col w-full">
                        <label for="email" class="font-semibold text-black/50 mb-2">Email</label><span v-if="$vCreateEmployee.email.$errors[0]" class="text-red-600"> Correo inválido</span>
                        <Input :errorMessage="$vCreateEmployee.email.$errors[0]?.$message.toString()"
                            v-model="newEmployeeFormData.email" class="w-80" id="email" />
                    </div>

                    <div class="flex flex-col w-full">
                        <label for="rut" class="font-semibold text-black/50 mb-2">RUT</label><span v-if="$vCreateEmployee.rut.$errors[0]" class="text-red-600"> Rut inválido</span>
                        <Input :errorMessage="$vCreateEmployee.rut.$errors[0]?.$message.toString()"
                            :formatter="formatRUT" v-model="newEmployeeFormData.rut" class="w-80" id="rut" />
                    </div>
                </div>
                <div class="flex justify-center mt-4">
                    <Button :disabled="$vCreateEmployee.$errors.length > 0" type="submit" class="w-40">Añadir</Button>
                </div>
            </form>
        </div>
    </Modal>
</template>