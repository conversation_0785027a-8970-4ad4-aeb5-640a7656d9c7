<script setup lang="ts">
import ListSkeleton from '../templates/TableListSkeleton.vue';
import { useRequests } from '@/hooks/useRequests';
import type { InstitutionRequest } from '@/models/types/institution';
import { extractCapitalizedInitials } from '@/helpers/stringHelpers';
import { onMounted, type PropType } from 'vue';

const props = defineProps({
    selectedItem: {
        type: Object as PropType<InstitutionRequest | null>,
        required: true,
    },
    onSelectCallback: {
        type: Function as PropType<(institutionRequest: InstitutionRequest, type: 'InstitutionRequest') => void>,
        required: true,
    },
})

const handleClick = (institutionRequest: InstitutionRequest) => {
    props.onSelectCallback(institutionRequest, 'InstitutionRequest');
}

const { institutionsRequests, handleFetchInstitutionRequests, isLoading } = useRequests();

onMounted(() => {
    handleFetchInstitutionRequests();
});

</script>

<template>
    <ListSkeleton v-if="isLoading" class="border-none mt-6" :count="5" />
    <div v-else class="flex flex-col gap-6 py-4 overflow-y-auto">
        <h3 class="text-sm font-semibold text-primary-900">Nuevos</h3>
        <div class="flex flex-col" v-if="institutionsRequests.length > 0">
            <div @click="handleClick(institutionRequest)"
                :class="selectedItem && selectedItem.id === institutionRequest.id ? 'bg-primary-100' : ''"
                class="flex w-full gap-5 transition-all duration-200 border-b border-black/10 py-4 px-2 hover:bg-primary-100 cursor-pointer"
                v-for="institutionRequest in institutionsRequests" :key="institutionRequest.id">
                <div
                    class="bg-primary-800 flex justify-center items-center text-white font-semibold h-12 w-12 rounded-full">
                    {{ extractCapitalizedInitials(institutionRequest.institutionName) }}
                </div>
                <div class="flex flex-col gap-4">
                    <div class="flex flex-col gap-2">
                        <span class="font-semibold">{{ institutionRequest.institutionName }}</span>
                        <span class="text-sm">Solicita una cuenta de ingreso al portal web</span>
                    </div>
                    <span>{{ }}</span>
                </div>
            </div>
        </div>
        <span class="text-sm" v-else> No hay solicitudes de registro disponibles.</span>
    </div>

</template>