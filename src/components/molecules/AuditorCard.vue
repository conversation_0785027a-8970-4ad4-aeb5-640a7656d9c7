<script setup lang="ts">
import type { Auditor } from '@/models/types';
import { formatLabel } from '@/helpers/stringHelpers';
import Card from '../atoms/Card.vue';
import { defineProps } from 'vue';


defineProps({
  auditor: {
    type: Object as () => Auditor,
    required: true,

  },
});

</script>

<template>

  <Card class="flex flex-col gap-12 max-h-min">
    <div class="flex flex-col gap-4">
      <div class="flex w-full justify-between">
        <span class="flex flex-col gap-1.5">
          <p class="text-sm font-semibold text-black/50">
            {{ formatLabel(auditor.nauditsPerformed, 'auditoría', 'auditorías') }}</p>
          <h3 class="font-semibold">{{ auditor?.username ?? "John Doe" }}</h3>
        </span>
        <span :style="{ backgroundColor: auditor?.color ?? '#000000' }"
          class="h-12 w-12 p-2 rounded-lg flex items-center justify-center font-semibold text-white">
          {{ auditor.acronym }}
        </span>
      </div>

      <p class="text-sm font-semibold text-primary-700">
        {{ formatLabel(auditor.nassignedInstitutions, "proceso activo", "procesos activos") }} </p>
    </div>

    <div class="flex w-full justify-between items-center">
      <slot name="options"></slot>
      <span class="text-sm font-semibold text-black/60"> {{ auditor?.city }}</span>
    </div>
  </Card>

</template>
