<script setup lang="ts">
import Button from '../atoms/Button.vue';
import AuditorPicker from './AuditorPicker.vue';
import { useInstitutions } from '@/hooks/useInstitutions';
import type { Institution } from '@/models/types';
import { ref } from 'vue';

const props = defineProps({
    institution: {
        type: Object as () => Institution,
        required: false
    },
    onNext: {
        type: Function,
        required: true
    }
})

const { handleAssignAuditorToInstitution } = useInstitutions();
const selectedAuditorID = ref<number>(props.institution?.auditor?.id ?? -1);


const handleSubmit = async () => {
    if (props.institution) {
        await handleAssignAuditorToInstitution(props.institution.id, selectedAuditorID.value);
        props.onNext();
    }
}

</script>

<template>
    <form class="flex flex-col h-full w-full items-center justify-between" @submit.prevent="handleSubmit">
        <div class="flex flex-col w-full items-center gap-6 px-10">
            <label for="countries" class="self-start block mb-2 text-sm font-semibold text-gray-900">
                {{ institution?.auditor ? 'Cambiar' : 'Asignar' }} Auditor
            </label>
            <AuditorPicker class="w-full" v-model="selectedAuditorID" />
        </div>
        <div class="flex w-full justify-around items-center">
            <div class="w-[12.2rem]"></div>
            <Button :disabled="selectedAuditorID === -1" type="submit" variant="primary">
                Actualizar
            </Button>
        </div>
    </form>
</template>