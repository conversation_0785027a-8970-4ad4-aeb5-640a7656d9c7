<script setup lang="ts">
import type { MaturityModel } from '@/models/types/maturityModel';
import { formatDateForChile } from '@/helpers/formatters';
import { defineProps } from 'vue';

const props = defineProps<{
    maturityModel: MaturityModel;
}>();

</script>

<template>
    <tr class="odd:bg-white even:bg-gray-50 border-b">
        <!-- ID -->
        <th scope="row" class="px-6 py-4">
            <div class="flex items-center gap-3">
                #MOD-0{{ props.maturityModel.id }}
            </div>
        </th>
        <!-- nombre -->
        <td class="px-6 py-4">{{ props.maturityModel.name }}</td>
        <!-- fecha creación -->
        <td class="px-6 py-4"> {{ formatDateForChile(props.maturityModel.createdAt?.toString() ?? "") }}</td>
        <!-- dimensiones -->
        <td class="px-6 py-4">{{ 0 }}</td>
        <!-- preguntas -->
        <td class="px-6 py-4">{{ 0 }}</td>
        <!-- evidencias -->
        <td class="px-6 py-4">{{ 0 }}</td>
        <!-- estado -->
        <td class="px-6 py-4">
            <span class=" border-green-500 text-green-500 border flex items-center justify-center p-2 rounded-xl">
                Activo
            </span>
        </td>
    </tr>
</template>
