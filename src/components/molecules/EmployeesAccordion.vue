<script setup lang="ts">
import { ref, computed } from 'vue';

const props = defineProps<{
  employees: { id: number; name: string; email: string; rut: string }[];
}>();

const accordionOpen = ref<boolean>(false);
const currentPage = ref<number>(1);
const itemsPerPage = 8;

const totalPages = computed(() => Math.ceil(props.employees.length / itemsPerPage));

const paginatedEmployees = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return props.employees.slice(start, end);
});

const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

const toggleAccordion = () => {
  accordionOpen.value = !accordionOpen.value;
};
</script>

<template>
  <div class="accordion">
    <button
      @click="toggleAccordion"
      class="opacity-50 hover:opacity-100 transition-opacity duration-300 py-4 cursor-pointer flex items-center"
    >
      {{ accordionOpen ? '- Empleados' : '+ Empleados' }}
    </button>
    <div v-if="accordionOpen" class="py-4">
      <div class="flex flex-col gap-6">

        <div class="flex font-semibold">
          <div class="flex-1 px-2">Nombre</div>
          <div class="flex-1 px-2">Email</div>
          <div class="flex-1 px-2">Rut</div>
        </div>

        <div v-for="employee in paginatedEmployees" :key="employee.id" class="flex text-sm">
          <div class="flex-1 px-2">{{ employee.name }}</div>
          <div class="flex-1 px-2">{{ employee.email }}</div>
          <div class="flex-1 px-2">{{ employee.rut }}</div>
        </div>
      </div>

      <div class="flex items-center justify-between mt-16">
        <div class="text-sm">
          Mostrando {{ paginatedEmployees.length }} empleados de {{ props.employees.length }}
        </div>

        <div class="flex items-center gap-4">
          <button
            @click="goToPage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="text-purple-600 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"
          >
             Anterior
          </button>

          <span
            class="flex items-center justify-center box-border px-4 w-8 h-8 text-white bg-purple-600 rounded-full text-center"
          >
            {{ currentPage }}
          </span>

          <button
            @click="goToPage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="text-purple-600 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"
          >
            Siguiente
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.accordion button {
  width: 100%;
  text-align: left;
}
</style>
