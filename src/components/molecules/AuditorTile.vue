<script setup lang="ts">
import type { Auditor } from '@/models/types';
import { extractInitials } from '@/helpers/stringHelpers';
import Tooltip from '../atoms/Tooltip.vue';
import IconButton from '../atoms/IconButton.vue';
import Button from '../atoms/Button.vue';
import 'vue-toast-notification/dist/theme-sugar.css';
import { useToast } from 'vue-toast-notification';
import { ref } from 'vue';
const $toast = useToast();

defineProps({
    auditor: {
        type: Object as () => Auditor,
        required: true
    }
})
const copiedMessage = ref('');

const copyToClipboard = async (text: string) => {
    try {
        await navigator.clipboard.writeText(text);
        $toast.success('¡Copiado al portapapeles!');
        setTimeout(() => {
            copiedMessage.value = '';
        }, 2000);
    } catch (err) {
        copiedMessage.value = 'Error al copiar';
        setTimeout(() => {
            copiedMessage.value = '';
        }, 2000);
    }
}
</script>

<template>
    <tr class="odd:bg-white even:bg-gray-50 border-b">
        <!-- nombre y logo -->
        <th scope="row" class="px-6 py-4">
            <div class="flex items-center gap-3">
                <span :style="{ backgroundColor: auditor.color }"
                    class="flex text-white items-center justify-center w-9 h-9 rounded-md ">
                    {{ extractInitials(auditor.username) }}
                </span>
                {{ auditor.username }}
            </div>
        </th>
        <!-- auditorías -->
        <td class="px-6 py-4">{{ auditor.nauditsPerformed }}</td>
        <!-- procesos activos -->
        <td class="px-6 py-4">{{ 1 }}</td>
        <!-- ciudad -->
        <td class="px-6 py-4">{{ auditor.city }}</td>
        <!-- contacto -->
        <td class="px-6 py-4">
            <span class="flex items-center gap-3">
                <Tooltip :text="auditor.phoneNumber ?? 'No disponible'">
                    <IconButton icon="fa-solid fa-phone" @click="copyToClipboard(auditor.phoneNumber ?? '')"/>
                </Tooltip>
                <Tooltip :text="auditor.email">
                    <IconButton icon="fa fa-envelope" @click="copyToClipboard(auditor.email)"/>
                </Tooltip>
            </span>
            <div v-if="copiedMessage" class="text-sm text-green-500 mt-1">{{ copiedMessage }}</div>
        </td>
        <!-- acciones -->
        <td class="px-6 py-4">
            <RouterLink :to="{
                path: `/admin/auditors/${auditor.id}/processes`,
                state: {
                    title: `${auditor.username}`
                }
            }">
                <Button variant="secondary">
                    Ver procesos
                </Button>
            </RouterLink>
        </td>
    </tr>
</template>