<template>
  <p class="2xl:header-4 header-7">Rit<PERSON> de avance</p>
  <div class="mt-4 flex 2xl:space-x-16 space-x-4">
    <div class="flex flex-col items-center space-y-1">
      <div class="flex items-center space-x-1">
        <span class="2xl:w-4 w-2 2xl:h-4 h-2 rounded-full bg-accent-5-600"></span>
        <p class="2xl:body-2 body-4 text-neutral-800">Inicio de proceso</p>
      </div>
      <p class="2xl:body-2 body-4 text-neutral-950 font-bold">{{ startDate }}</p> 
    </div>
    
    <div class="flex flex-col items-center space-y-1">
      <div class="flex items-center space-x-1">
        <span class="2xl:w-4 w-2 2xl:h-4 h-2 rounded-full bg-gray-300"></span>
        <p class="2xl:body-2 body-4 text-neutral-800">Estimación de término</p>
      </div>
      <p class="2xl:body-2 body-4 text-neutral-950 font-bold">{{ estimationDate }}</p> 
    </div>
    
    <div class="flex flex-col items-center space-y-1">
      <div class="flex items-center space-x-1">
        <span class="2xl:w-4 w-2 2xl:h-4 h-2 rounded-full bg-primary-800"></span>
        <p class="2xl:body-2 body-4 text-neutral-800">Fin del Proceso</p>
      </div>
      <p class="2xl:body-2 body-4 text-neutral-950 font-bold">{{ endDate }}</p>
    </div>
  </div>
  
  <div ref="chartCanvas" :style="{ height: '100%' }"></div>

  
  <!--- CAMBIAR EL TEXTO POR ALGO MÁS CORTO-->
  <div :class="{ 'disabled': !props.active }" class="-mt-12">
  <p v-if="!props.active" class="text-m font-bold text-gray-700">
    El proceso ha finalizado
  </p>
  
  <p v-if="props.active" class="2xl:body-3 body-4 font-semibold text-gray-500 mt-3">
    Está por <span :class="messageColor">{{ message }}</span>
  </p>
  <p v-else class="text-sm font-semibold text-gray-500 mt-3">
    Actualmente, el proceso se encuentra <span class="text-primary-700">finalizado</span>. Estamos a la espera de coordinar y ejecutar nuevos procesos.
  </p>
</div>
</template>

<script setup lang="ts">
import { onMounted, ref, onBeforeUnmount, defineProps, computed } from 'vue'
import * as echarts from 'echarts'

const props = defineProps<{
  originalData: number[]
  projectionData: number[]
  estimationDate: string
  startDate: string
  endDate: string
  active: boolean
}>()

const is2xlScreen = computed(() => {
  return window.matchMedia("(min-width: 1536px)").matches;
});

const chartCanvas = ref<HTMLDivElement | null>(null)
let chartInstance: echarts.ECharts | null = null
const additionalDays = 5

const parseDate = (dateString: string) => {
  const parts = dateString.split('/')
  return new Date(`${parts[2]}-${parts[1]}-${parts[0]}`)
}

const message = computed(() => {
  const dateStartDate = parseDate(props.startDate)
  const estimationDate = parseDate(props.estimationDate)
  return dateStartDate > estimationDate ? 'delante del ritmo estimado.' : 'debajo del ritmo de estimado.'
})

const messageColor = computed(() => {
  const dateStartDate = parseDate(props.startDate)
  const estimationDate = parseDate(props.estimationDate)
  return dateStartDate > estimationDate ? 'text-green-600 font-bold' : 'text-red-600 font-bold'
})

const getDatesBetween = (start: Date, end: Date, additionalDays: number) => {
  const dates: string[] = []
  const currentDate = new Date(start)

  while (currentDate <= end) {
    dates.push(currentDate.toLocaleDateString('es-CL'))
    currentDate.setDate(currentDate.getDate() + 1)
  }

  for (let i = 1; i <= additionalDays; i++) {
    const futureDate = new Date(end)
    futureDate.setDate(end.getDate() + i)
    dates.push(futureDate.toLocaleDateString('es-CL'))
  }

  return dates
}

const createChart = () => {
  if (chartCanvas.value) {
    chartInstance = echarts.init(chartCanvas.value);
    chartInstance.resize();

    const dateStartDate = parseDate(props.startDate);
    const dateEndDate = parseDate(props.endDate);
    const datesArray = getDatesBetween(dateStartDate, dateEndDate, additionalDays);
    const updatedOriginalData = [0, ...props.originalData];
    const updatedProjectionData = [0, ...props.projectionData];

    const originalLineColor = message.value === 'delante del ritmo estimado.' ? 'green' : 'red';

    const option = {
      grid:{
     top: is2xlScreen.value ? '15%' : '15%',
      },
      xAxis: {
        type: 'category',
        data: datesArray,
        axisLabel: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        interval: 33,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: 'Proyección',
          data: updatedProjectionData, 
          type: 'line',
          smooth: true,
          lineStyle: {
            color: 'lightgray',
            width: 2,
            type: 'dashed'
          },
          symbol: 'none',
          showSymbol: false,
          markPoint: {
            symbol: 'circle',
            symbolSize: is2xlScreen.value ? 15 : 10,
            itemStyle: {
              color: 'lightgray'
            },
            label: {
              show: false
            },
            data: [
              {
                coord: [
                  updatedProjectionData.length - 1,
                  updatedProjectionData[updatedProjectionData.length - 1]
                ],
                name: 'Último Punto'
              }
            ]
          }
        },
        {
          name: 'Ritmo de avance',
          data: updatedOriginalData, 
          type: 'line',
          smooth: true,
          lineStyle: {
            color: originalLineColor, 
            width: 5
          },
          symbol: 'none',
          markLine: {
            silent: true,
            symbol: 'none',
            lineStyle: {
              type: 'dashed'
            },
            data: [
              {
                name: 'Fecha Inicial',
                xAxis: dateStartDate.toLocaleDateString('es-CL'),
                lineStyle: {
                  color: 'orange',
                  width: 2
                },
                label: {
                  show: false
                }
              },
              {
                name: 'Hoy',
                xAxis: new Date().toLocaleDateString('es-CL'),
                lineStyle: {
                  color: 'green',
                  width: 2
                },
                label: {
                  position: 'start',
                  verticalAlign: 'bottom',
                  formatter: 'Hoy',
                  fontSize: is2xlScreen.value ? 15 : 12,
                  color: 'grey',
                  distance: is2xlScreen.value ? 21 : 15
                }
              },
              {
                name: 'Fecha Final',
                xAxis: dateEndDate.toLocaleDateString('es-CL'),
                lineStyle: {
                  color: 'blue',
                  width: 2
                },
                label: {
                  show: false
                }
              }
            ]
          }
        }
      ]
    };

    chartInstance.setOption(option);
  }
};

const destroyChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
}

onMounted(() => {
  createChart()

  const handleResize = () => {
    destroyChart()
    createChart()
  }

  window.addEventListener('resize', handleResize)

  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize)
    destroyChart()
  })
})
</script>