<template>
    <div v-if="isVisible" class="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div class="bg-background rounded-lg shadow-lg p-6 w-full max-w-8xl h-[85vh] overflow-y-auto">
        <header class="flex justify-between items-center">
          <h3 class="text-2xl font-semibold mt-5">Nivel de dimensión actual</h3>
          <button @click="closeModal" class="text-3xl">&times;</button>
        </header>
        <p class="text-gray-500 text-lg mb-8">En mi institución</p>
  
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8">
          <div v-for="(dimension, index) in dimensions" :key="index" class=" bg-white p-8 rounded-lg">
            <div class="flex justify-between items-center mb-6">
              <div>
                <h4 class="font-semibold text-2xl">{{ dimension.title }}</h4>
                <p class="text-base text-gray-500">{{ dimension.description }}</p>
              </div>
              <div :class="`p-4 rounded-lg ${iconBackground(index)} icon-container`">
                <i :class="`fa-solid ${dimension.icon} text-3xl text-white`"></i>
              </div>
            </div>
            <div class="mt-8">
              <p class="text-3xl font-bold text-indigo-700">Nivel {{ dimension.level }}</p>
              <p class="text-base text-gray-500 mt-4">{{ dimension.levelDescription }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { defineProps, defineEmits } from 'vue';
  
  interface Dimension {
    title: string;
    description: string;
    level: number;
    levelDescription: string;
    icon: string;
  }
  
  const props = defineProps<{ isVisible: boolean }>();
  const emit = defineEmits(['close']);
  
  const iconBackground = (index: number) => {
    const colors = ['bg-purple-600', 'bg-green-600', 'bg-pink-500', 'bg-red-500', 'bg-blue-500', 'bg-yellow-500'];
    return colors[index % colors.length]; 
  };
  
  const dimensions: Dimension[] = [
    {
      title: 'Institucionalización',
      description: 'Proceso de alinear la visión y estrategias de transparencia con las directrices nacionales.',
      level: 5,
      levelDescription: 'La institución es un referente en términos de formalización y estandarización de procesos.',
      icon: 'fa-clipboard-list'
    },
    {
      title: 'Adquisición de bienes',
      description: 'Proceso de adquisición de bienes formalizado con métricas basadas en resultados históricos.',
      level: 3,
      levelDescription: 'Se siguen pautas y comparaciones de precios y calidad con regularidad.',
      icon: 'fa-shopping-bag'
    },
    {
      title: 'Adquisición de servicios',
      description: 'Proceso estandarizado para adquisición de servicios con control de calidad.',
      level: 3,
      levelDescription: 'El proceso incluye la comparación de proveedores y la verificación de calidad.',
      icon: 'fa-headset'
    },
    {
      title: 'Contratación de personal',
      description: 'Proceso controlado para contratación con algunos criterios de selección.',
      level: 2,
      levelDescription: 'Se aplican criterios, pero aún hay margen para mejorar la consistencia.',
      icon: 'fa-user-plus'
    },
    {
      title: 'Comunicación',
      description: 'Los canales de comunicación son informales y necesitan mejora.',
      level: 1,
      levelDescription: 'La falta de comunicación afecta la coordinación y los resultados.',
      icon: 'fa-bullhorn'
    },
    {
      title: 'Rendición de cuentas',
      description: 'Rendición de cuentas completamente transparente y con auditorías regulares.',
      level: 5,
      levelDescription: 'Los procesos están sujetos a auditorías regulares y abiertas al público.',
      icon: 'fa-file-invoice'
    }
  ];
  
  const closeModal = () => {
    emit('close');
  };
  </script>
  
  <style scoped>
  .icon-container {
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  </style>
  