<script setup lang="ts">
import Card from '../atoms/Card.vue';

defineProps({
    dimension: { type: Object, required: true },
})

</script>

<template>
    <Card class="flex flex-col gap-3 shadow-lg">
        <div class="flex justify-between">
            <div class="flex flex-col justify-between gap-1.5">
                <span class="text-sm text-black/70">Dimensión</span>
                <h3 class="font-semibold">{{ dimension.name }}</h3>
            </div>
            <div class="flex items-center justify-center h-12 w-12 rounded-md text-white text-xl"
                :style="{ backgroundColor: dimension.color }">
                <i :class="dimension.icon"> </i>
            </div>
        </div>
        <span class="text-purple-800 font-semibold"> Nivel {{ dimension.level }}</span>
        <p class="text-sm text-black/70 2xl:text-pretty xl:text-justify">
            {{ dimension.description }}
        </p>


    </Card>
</template>