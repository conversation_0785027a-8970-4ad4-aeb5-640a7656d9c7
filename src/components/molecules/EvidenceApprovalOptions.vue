<script setup lang="ts">
import { ref, watch } from 'vue';
import Button from '../atoms/Button.vue';
import Card from '../atoms/Card.vue';
import { useAsyncFetch } from '@/hooks/useAsyncFetch';
import { EvidenceStatus } from '@/models/types';

const props = defineProps({
  evidenceId: {
    type: Number,
    required: true
  },
  onConfirm: {
    type: Function,
    required: true
  },
  onModalClose: {
    type: Function,
    required: false
  }
})

const TITLE_MAP = {
  [EvidenceStatus[EvidenceStatus.REJECTED]]: {
    title: "Objetar Evaluación",
    description: "Justifique por qué esta evidencia no cumple con las condiciones requeridas."
  },
  [EvidenceStatus[EvidenceStatus.ACCEPTED_WITH_COMMENT]]: {
    title: "Aprobar con observación",
    description: "Mencione aquí los aspectos a mejorar o las observaciones notadas."
  },
  [EvidenceStatus[EvidenceStatus.ACCEPTED]]: {
    title: "",
    description: ""
  }
} as Record<string, { title: string, description: string }>;

const nextStep = defineModel<boolean>({ required: true })
const selectedOption = ref('');
const selectedOptionText = ref({
  title: "",
  description: ""
});
const comment = ref('');

const handleSelectedOption = async () => {
  if (selectedOption.value === EvidenceStatus[EvidenceStatus.REJECTED] || selectedOption.value === EvidenceStatus[EvidenceStatus.ACCEPTED_WITH_COMMENT]) {
    nextStep.value = true;
    if (comment.value.trim() !== '') {

      await props.onConfirm(props.evidenceId, selectedOption.value, comment.value, props.onModalClose);
    }
  } else if (selectedOption.value === EvidenceStatus[EvidenceStatus.ACCEPTED]) {
    await props.onConfirm(props.evidenceId, selectedOption.value, '', props.onModalClose);
  }
  else {
    nextStep.value = false;
  }
}

const { fetchData: onSubmit, isLoading } = useAsyncFetch(handleSelectedOption);

watch(selectedOption, (newValue) => {
  selectedOptionText.value = TITLE_MAP[newValue];
})

const shouldDisableButton = () => {
  return selectedOption.value === '' || (nextStep.value && selectedOption.value !== EvidenceStatus[EvidenceStatus.ACCEPTED] && comment.value.trim() === '');
}

</script>

<template>
  <h3 v-if="nextStep" class="flex flex-col w-full gap-4 text-2xl font-bold">{{ selectedOptionText.title }}
    <p class="text-sm font-normal"> {{ selectedOptionText.description }}</p>
  </h3>
  <Card :class="`${nextStep ? 'px-0 py-0' : ''}`" class="flex flex-col gap-4 text-black h-min">
    <div v-if="!nextStep" class="flex flex-col gap-4 text-black h-min">
      <div class="flex items-center gap-4">
        <input type="radio" id="rejected" name="decision" :value="EvidenceStatus[EvidenceStatus.REJECTED]"
          v-model="selectedOption" />
        <label for="rejected" class="cursor-pointer">Objetar</label>
      </div>
      <div class="flex items-center gap-4">
        <input type="radio" id="accepted-with-comment" name="decision"
          :value="EvidenceStatus[EvidenceStatus.ACCEPTED_WITH_COMMENT]" v-model="selectedOption" />
        <label for="accepted-with-comment" class="cursor-pointer">Aprobar con observación</label>
      </div>
      <div class="flex items-center gap-4">
        <input type="radio" id="accepted" name="decision" :value="EvidenceStatus[EvidenceStatus.ACCEPTED]"
          v-model="selectedOption" />
        <label for="accepted" class="cursor-pointer">Aprobar</label>
      </div>
    </div>
    <div v-else>
      <textarea v-model="comment" rows="15" class="w-full h-full resize-none border border-primary-700 rounded-xl p-4"
        placeholder="Escriba aquí..."></textarea>
    </div>
  </Card>
  <div :class="`${nextStep ? 'justify-between' : 'justify-end'} flex w-full`">
    <Button v-if="nextStep" @click="nextStep = false" variant="outline" class="w-40 bg-transparent">Atrás</Button>
    <Button :loading="isLoading" :disabled="shouldDisableButton()" @click="onSubmit" variant="invert"
      class="w-40">Siguiente</Button>
  </div>
</template>