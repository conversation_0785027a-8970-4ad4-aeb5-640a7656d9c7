<script setup lang="ts">
import { EVIDENCE_STATUSES_MAP } from '@/mappers/evidenceMappers';
import Button from '../atoms/Button.vue';
import { EvidenceStatus, type Evidence } from '@/models/types';
import { h, ref } from 'vue';

const props = defineProps({
    auditorMode: {
        type: Boolean,
        required: false,
        default: false
    },
    evidence: {
        type: Object as () => Evidence,
        required: true
    }
})


const isEvidenceModalOpen = ref(false);
const getIcon = () => {
    if (props.evidence.evidenceStatus) {
        const iconConfig = EVIDENCE_STATUSES_MAP[props.evidence.evidenceStatus];
        if (iconConfig) {
            return h('i', { class: iconConfig.class });
        }
    }
}

const openModal = () => {
    isEvidenceModalOpen.value = true;
}

const getTitle = (evidence: { approvalRequirements: { title: string }[] | string; id?: number }) => {
    if (!Array.isArray(evidence.approvalRequirements) || evidence.approvalRequirements.length === 0) {
        return 'Sin título';
    }
    const id = evidence.id ?? 0;
    const index = id % evidence.approvalRequirements.length;
    return evidence.approvalRequirements[index]?.title || 'Sin título';
};

</script>


<template>
    <div class="flex flex-col gap-4 border-[1px] border-primary-800 rounded-lg p-4 max-w-sm relative">
        <div v-if="props.auditorMode && evidence.evidenceStatus" class="absolute top-0.5 right-1">
            <component class="text-lg" :is="getIcon()" />
        </div>
        <div v-if="evidence.evidenceStatus === EvidenceStatus.APPEALED" class="absolute top-0.5 right-1">
            <i class="fa-regular fa-circle-check text-green-500"></i>
        </div>
        <div class="flex items-center gap-4">
            <span class="px-4 py-3 rounded-xl flex items-center justify-center bg-primary-100">
                <i class="fa-solid fa-file-arrow-up text-primary-800 2xl:text-2xl xl:text-xl"></i>
            </span>
            <h3 class="font-semibold 2xl:text-base xl:text-sm">
                {{ getTitle(evidence) }}
            </h3>
        </div>
        <div class="flex flex-col gap-3">
            <h4 class="text-primary-900 font-semibold">Observación dada por el auditor</h4>
            <p class="text-justify text-sm">
                {{ evidence.comment }}
            </p>
        </div>
        <div class="flex justify-end">
            <Button @click="openModal" class="h-10 w-28 text-sm">
                <slot name="button-text"></slot>
            </Button>
        </div>
    </div>
    <slot name="evidence-modal" :isEvidenceModalOpen="isEvidenceModalOpen"
        :onModalClose="() => { isEvidenceModalOpen = false }" :evidence="evidence" :dimensionName="'hola'">
    </slot>

</template>