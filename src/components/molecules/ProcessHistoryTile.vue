<script setup lang="ts">
import Button from '../atoms/Button.vue';
import type { Process } from '@/models/types';
import { formatDateForChile } from '@/helpers/formatters';
import { mapProcessStatusToButtonText, mapProcessStatusToColor } from '@/mappers';

const props = defineProps({
    process: {
        type: Object as () => Process,
        required: true
    }
})

const handleGetPath = (processStatus: string) => {
    if (processStatus === 'FINISHED') return `/gestor/record/${props.process.id}`;
    return `/gestor/process`;
}

</script>

<template>
    <tr>
        <td class="py-2 px-6 border-b">
            <span>
                <i :class="mapProcessStatusToColor(process.status)" class="fa-regular fa-circle-check text-2xl"></i>
            </span>

        </td>
        <td class="py-2 px-6 border-b font-bold">#PRC-00{{ process.id }}</td>
        <td class="py-2 px-6 border-b">{{ formatDateForChile(process.startDate) }}</td>
        <td class="py-2 px-6 border-b">{{ formatDateForChile(process.endDate) }}</td>
        <td class="py-2 px-6 border-b">{{ process.employees?.length ?? 0 }}</td>
        <td class="py-2 px-6 border-b font-bold">{{ 80 }} %</td>
        <td class="py-2 px-6 border-b font-bold">{{ 1 }}</td>
        <td class="py-2 px-6 border-b">
            <Button v-if="mapProcessStatusToButtonText(process.status) !== null" variant="secondary">
                <RouterLink :to="{ path: handleGetPath(process.status) }" class="text-sm">
                    {{ mapProcessStatusToButtonText(process.status) }}
                </RouterLink>
            </Button>
        </td>
    </tr>

</template>