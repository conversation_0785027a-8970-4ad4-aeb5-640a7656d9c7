<script setup lang="ts">
import { useRequests } from '@/hooks/useRequests';
import type { Process } from '@/models/types';
import { onMounted, type PropType, computed } from 'vue';
import ListSkeleton from '../templates/TableListSkeleton.vue';

const { processesRequests, handleFetchProcessesRequests, isLoading } = useRequests();

const props = defineProps({
    selectedItem: {
        type: Object as PropType<Process | null>,
        required: true,
    },
    onSelectCallback: {
        type: Function as PropType<(process: Process, type: 'Process') => void>,
        required: true,
    },
})

const handleClick = (process: Process) => {
    props.onSelectCallback(process, 'Process');
}

onMounted(() => {
    handleFetchProcessesRequests();
});

</script>


<template>
    <ListSkeleton class="border-none mt-6" v-if="isLoading" :count="5" />
    <div v-else class="flex flex-col gap-6 py-4 overflow-y-auto">
        <h3 class="text-sm font-semibold text-primary-900">Entrantes</h3>
        <div class="flex flex-col" v-if="processesRequests.length > 0">
            <div @click="handleClick(process)"
                :class="selectedItem && selectedItem.id === process.id ? 'bg-primary-100' : ''"
                class="flex gap-5 w-full cursor-pointer hover:bg-primary-100 py-4 px-2 transition-all duration-200 border-b border-black/10"
                v-for="process in processesRequests" :key="process.id">
                <div :style="{ backgroundColor: process.simpleInstitutionDTO.color }"
                    class="flex justify-center items-center text-white font-semibold h-12 w-12 rounded-full">
                    {{ process.simpleInstitutionDTO.acronym }}
                </div>
                <div class="flex flex-col gap-2 flex-1">
                    <div class="flex flex-col gap-1">
                        <span class="font-semibold">{{ process.simpleInstitutionDTO.name }}</span>
                    </div>
                    <span class="text-sm">Solicita someterse a un {{ process.name }}</span>
                </div>
            </div>
        </div>
        <span class="text-sm" v-else> No hay solicitudes de procesos disponibles.</span>
    </div>
</template>