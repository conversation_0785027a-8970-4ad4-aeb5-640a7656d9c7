<template>
  <p class="text-xl font-semibold">Historial de niveles obtenidos</p>
  
  <div class="flex flex-col items-center mt-6 space-y-2">
      <div class="flex space-x-4">
          <div class="flex items-center space-x-1">
              <span class="w-4 h-4 rounded-full bg-black"></span>
              <p class="text-m text-gray-500">{{ ResponseTrendingData?.[0]?.processName }}</p>
          </div>
          <div class="flex items-center space-x-1">
              <span class="w-4 h-4 rounded-full bg-purple-500"></span>
              <p class="text-m text-gray-500">Institucionalización</p>
          </div>
          <div class="flex items-center space-x-1">
              <span class="w-4 h-4 rounded-full bg-green-500"></span>
              <p class="text-m text-gray-500">Adquisición de bienes</p>
          </div>
          <div class="flex items-center space-x-1">
              <span class="w-4 h-4 rounded-full bg-purple-300"></span>
              <p class="text-m text-gray-500">Adquisición de servicios</p>
          </div>
      </div>
      <div class="flex space-x-4">
          <div class="flex items-center space-x-1">
              <span class="w-4 h-4 rounded-full bg-red-500"></span>
              <p class="text-m text-gray-500">Contratación de personal</p>
          </div>
          <div class="flex items-center space-x-1">
              <span class="w-4 h-4 rounded-full bg-blue-500"></span>
              <p class="text-m text-gray-500">Comunicación</p>
          </div>
          <div class="flex items-center space-x-1">
              <span class="w-4 h-4 rounded-full bg-yellow-500"></span>
              <p class="text-m text-gray-500">Rendición de Cuentas</p>
          </div>
      </div>
  </div>
  
  <div ref="chartCanvas" style="height: 400px;"></div>
</template>

<script setup lang="ts">
import { onMounted, ref, type Ref } from 'vue';
import * as echarts from 'echarts';
import {
  type Institution,
  type Process,
  type ResponseTrending
} from '@/models/types/index';
import { useProcesses } from '@/hooks/process/useProcesses';
import { useAuth } from '@/hooks/useAuth';
import { useRoute } from 'vue-router';
import { useInstitutions } from '@/hooks/useInstitutions';
import { Logger } from '@/services/logger';
const { handleGetInstitutionByManagerID } = useInstitutions();
const actualInstitutionID = ref(-1);
const { trendingGetChart } = useInstitutions();
const { authData } = useAuth();
const { handleGetActualProcessByInstitutionID, handleGetProcessByID } = useProcesses();
const actualProcess = ref<Process>();
const actualInstitution = ref<Institution>();
const route = useRoute();
const ResponseTrendingData = ref<ResponseTrending[]>([]);

let levelData: Ref<number[][]> = ref([]);

const processChartData = () => {
  if (!ResponseTrendingData.value) return;

  const dimensionLevelsData: Record<string, number[]> = {};
  const institutionLevels: number[] = [];

  ResponseTrendingData.value.forEach((process) => {
    institutionLevels.push(process.institutionLevel);

    process.dimensionLevels.forEach((dimension) => {
      if (!dimensionLevelsData[dimension.name]) {
        dimensionLevelsData[dimension.name] = [];
      }
      dimensionLevelsData[dimension.name].push(dimension.level);
    });
  });

  levelData.value = Object.values(dimensionLevelsData).map((level) =>
    level.filter(value => typeof value === 'number' && !isNaN(value))
  );
  levelData.value.push(institutionLevels.filter(value => typeof value === 'number' && !isNaN(value)));
};

const handleFetchProcess = async () => {
  if (route.params.processID && parseInt(route.params.processID as string)) {
    const processID = parseInt(route.params.processID as string);
    const institutionID = parseInt(authData.value?.simpleInstitutionDTO?.id as unknown as string);
    actualProcess.value = await handleGetProcessByID(processID);
    actualInstitution.value = await handleGetInstitutionByManagerID(institutionID);
    return;
  } else if (authData.value?.simpleInstitutionDTO) {
    actualProcess.value = await handleGetActualProcessByInstitutionID(
      authData.value!.simpleInstitutionDTO!.id
    ) ?? undefined;
    const apiData = await trendingGetChart(authData.value!.simpleInstitutionDTO!.id);
    if (apiData && apiData.result) {
      ResponseTrendingData.value = apiData.result as unknown as ResponseTrending[];
      processChartData();
      renderChart();
    } else {
      Logger.error('Invalid API response:', apiData);
    }
  }
};

onMounted(async () => {
  if (authData.value && authData.value.simpleInstitutionDTO) {
    actualInstitutionID.value = authData.value.simpleInstitutionDTO.id;
    try {
      handleFetchProcess();
    } catch (error) {
      Logger.error('Error fetching process data:', error);
    }
  }
});

const chartCanvas = ref<HTMLDivElement | null>(null);

const renderChart = () => {
  if (chartCanvas.value) {
    const chartInstance = echarts.init(chartCanvas.value);
    const seriesData = levelData.value.map((data, index) => {
      const colors = [
        "rgb(168, 85, 247)", 
        "rgb(34, 197, 94)", 
        "rgb(192, 132, 252)", 
        "rgb(239, 68, 68)",
        "rgb(59, 130, 246)", 
        "rgb(234, 179, 8)",
        "black",
      ];
      return {
        name: `Nivel ${index + 1}`,
        data,
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 2,
          type: 'dashed',
          color: colors[index] || `hsl(${index * 60}, 100%, 50%)`, 
        },
        symbol: 'none',
      };
    });

    const option = {
      xAxis: {
        type: 'category',
        data: Array.from({ length: levelData.value[0].length }, (_, i) => i + 1),
        axisLabel: {
          show: false,
        },
        axisTick: {
          show: true,
        },
      },
      yAxis: {
        type: 'value',
        min: 1,
        max: 5, 
        interval: 1,
      },
      series: seriesData, 
    };

    chartInstance.setOption(option);
    window.addEventListener('resize', () => {
      chartInstance.resize();
    });
    Logger.info('Chart rendered successfully.');
  } else {
    Logger.warn('Chart canvas is not available.');
  }
  
};
</script>


<style scoped>
.echarts {
width: 100%;
height: 100%;
}
</style>
