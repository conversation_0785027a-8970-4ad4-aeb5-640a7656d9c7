<script lang="ts" setup>
import Button from '../atoms/Button.vue';
import { useAsyncFetch } from '@/hooks/useAsyncFetch';
import Input from '../atoms/Input.vue';
import { useAuth } from '@/hooks/useAuth';
import { loginRules } from '@/models/validation/formRules';
import useVuelidate from '@vuelidate/core';
import { ref, watch } from 'vue';

const loginData = ref({
  username: '',
  password: ''
})
const showPassword = ref<boolean>(false);
const $vLogin = useVuelidate(loginRules, loginData.value);

const togglePassword = () => {
  showPassword.value = !showPassword.value;
};

const { handleLogin } = useAuth();
const { fetchData, isLoading, error } = useAsyncFetch(handleLogin)

const handleSubmit = async () => {
  const result = await $vLogin.value.$validate();
  if (result) {
    await fetchData({ email: loginData.value.username, password: loginData.value.password });
  }
};
watch(error, (newError) => {
  if (newError) {
    setTimeout(() => {
      error.value = null;
    }, 2000);
  }
});
</script>

<template>
  <div class="flex items-center justify-center">
    <div class="w-full flex justify-center">
      <div class="w-full max-w-md p-4 2xl:p-8 bg-white rounded-lg ">
        <div class="flex flex-col h-fit w-full gap-0 *:font-montserrat">
          <h2 class="2xl:header-1 header-4 2xl:mt-0 mt-4 font-bold text-center leading-none">Inicio de sesión</h2>
          <p class="2xl:header-8 body-4 text-center text-gray-600">Ingrese las credenciales de su cuenta</p>
        </div>
        <form @submit.prevent="handleSubmit" class="flex flex-col gap-4">
          <div class="flex flex-col">
            <div class="relative mt-5 2xl:mt-10">
              <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Nombre de Usuario <span v-if="$vLogin.username.$errors[0]" class="text-red-600"> (EL nombre es requerido)</span>
              <span v-else class="text-primary-400">*</span></label>
              <Input :errorMessage="$vLogin.username.$errors[0]?.$message.toString()" variant="outline" type="text"
              :showErrorIcon="false"
                v-model="loginData.username" id="email" placeholder="      Ingrese su nombre de usuario" required
                autofocus class="block w-full shadow-sm sm:text-sm" />
              <i v-if="!loginData.username"
                class="fa-regular fa-user absolute inset-y-11 left-3 flex items-center text-sm leading-5 text-gray-500"></i>
            </div>
          </div>

          <div class="flex flex-col">
            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Contraseña <span v-if="$vLogin.password.$errors[0]" class="text-red-600"> (La contraseña es requerida)</span>
              <span v-else class="text-primary-400">*</span></label>
            <div class="relative">
              <Input :errorMessage="$vLogin.password.$errors[0]?.$message.toString()" variant="outline"
                :type="showPassword ? 'text' : 'password'" v-model="loginData.password" id="password" required autofocus
                :showErrorIcon="false"
                placeholder="       Ingrese su contraseña" class="block w-full shadow-sm sm:text-sm" />
              <i v-if="!loginData.password"
                class="pi pi-lock absolute inset-y-1 left-3 flex items-center text-sm leading-5 text-gray-500"
                style="color: gray"></i>
              <button type="button" @click="togglePassword"
                class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500 hover:text-gray-700 focus:outline-none">
                <i :class="showPassword ? 'fa-solid fa-eye-slash' : 'fa-solid fa-eye'" class="text-sm"></i>
              </button>
            </div>
            <div class="flex justify-end mt-2">
              <router-link to="/recoveryPassword">
                <a class="text-sm text-primary-400 underline hover:underline">
              Recuperar contraseña
            </a>
              </router-link>
           
          </div>
          </div>
        

          <div v-if="error" class="mt-8">
            <Button :loading="isLoading" :variant="'missing'" type="submit"
              class="font-normal w-full px-4 py-1">
              <span class="font-medium text-base">{{ error.message }}</span>
            </Button>
          </div>
          <div v-else class="mt-8">
            <Button :loading="isLoading" type="submit"
              class="font-normal w-full px-4 py-1 text-white bg-primary-900 border border-transparent rounded-md">
              <span class="font-medium text-base">Iniciar sesión</span>
            </Button>
          </div>
        </form>

        <div class="flex flex-col items-center justify-center mt-2 text-sm text-gray-600">
          <p>¿No tiene una cuenta? <router-link :to="{ path: '/register' }"
            class="text-primary-400 underline hover:underline">Solicitar una cuenta.</router-link></p>
        </div>
        
        <div class="w-full flex justify-start mt-20">
          <a href="/" class="flex items-center text-primary-400 hover:underline">
            <i class="fa-solid fa-arrow-left text-neutral-950 mr-1"></i>
            Volver a la página principal
          </a>
        </div>
      </div>
    </div>
  </div>
</template>
