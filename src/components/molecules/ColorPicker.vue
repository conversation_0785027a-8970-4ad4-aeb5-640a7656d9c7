<script setup lang="ts">
import Modal from '../atoms/Modal.vue';
import colors from '@/assets/theme/colors.json';

defineProps({
    isOpen: {
        type: Boolean,
        required: true
    },
    onClose: {
        type: Function,
        required: true
    },
    selectedColor: String,
    onColorChange: {
        type: Function,
        required: true
    }
})

</script>

<template>
    <Modal :isOpen="isOpen" :onClose="onClose">
        <div class="flex flex-col gap-8 w-[34vw]">
            <h3 class="font-semibold"> Seleccione un color</h3>
            <div class="flex flex-wrap justify-center gap-3">
                <span @click="onColorChange(color)" v-for="color in colors" :key="color"
                    class="flex items-center justify-center w-10 h-10 cursor-pointer rounded-md"
                    :style="{ backgroundColor: color }">
                    <i v-if="color === selectedColor" class="fa-solid fa-check text-white"></i>
                </span>

            </div>
        </div>

    </Modal>

</template>