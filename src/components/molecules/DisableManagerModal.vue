<script setup lang="ts">
import Modal from '../atoms/Modal.vue';
import Button from '../atoms/Button.vue';
import Input from '../atoms/Input.vue';
import useVuelidate from '@vuelidate/core';
import type { Gestor } from '@/models/types';
import { ref, toRefs } from 'vue';
import { createGestorRules } from '@/models/validation/formRules';
import { formatPhoneNumber, formatRUT } from '@/helpers/formatters';
import { useManagers } from '@/hooks/useManagers';
import type { GestorFormSchema } from '@/models/validation/formSchemas';
import { useToast } from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-sugar.css';

const $toast = useToast();
const props = defineProps({
    manager: {
        type: Object as () => Gestor,
        required: true
    },
    isModalOpen: {
        type: Boolean,
        required: true
    },
    onClose: {
        type: Function,
        required: true
    }
});

const { manager } = toRefs(props);

const newManagerFormData = ref<GestorFormSchema>({
    username: '',
    email: '',
    position: '',
    rut: '',
    password: '',
    phoneNumber: '',
    confirmPassword: ''
});

const rules = createGestorRules(true);
const $vCreateNewManager = useVuelidate(rules, newManagerFormData.value);
const { handleDisableManager } = useManagers();

const handleSubmit = async () => {
    const result = await $vCreateNewManager.value.$validate();
    if (result) {
        try {
            const institutionId = props.manager.institution?.id ?? 0;
            await handleDisableManager(institutionId, props.manager.id, newManagerFormData.value);
            $toast.success('Gestor deshabilitado correctamente');
            window.location.reload();
        } catch (error) {
            $toast.error('Error al deshabilitar el gestor');
        }
    }
}
const passwordVisible = ref(false);
const confirmPasswordVisible = ref(false);

const togglePasswordVisibility = (field: 'password' | 'confirmPassword') => {
    if (field === 'password') {
        passwordVisible.value = !passwordVisible.value;
    } else {
        confirmPasswordVisible.value = !confirmPasswordVisible.value;
    }
};

</script>

<template>
    <Modal :isOpen="isModalOpen" :onClose="onClose">
        <form @submit.prevent="handleSubmit" class="flex flex-col gap-20 text-base font-normal">
            <div class="flex flex-col gap-6">
                <h3 class="flex flex-col gap-2 text-xl font-bold">Deshabilitar Gestor Institucional
                    <p class="text-sm font-medium">
                        Esta acción es irreversible y deshabilitará al gestor institucional actual de esta institución.
                        Debe
                        asignar un nuevo gestor para realizar esta acción.
                    </p>
                </h3>
                <div class="w-full border-b border-black/10"></div>
                <section class="flex w-full gap-20">
                    <div class="flex flex-col gap-6">
                        <span class="font-semibold ">Gestor actual</span>
                        <div class="flex flex-col gap-5">
                            <div class="flex items-center justify-start gap-8 w-full font-normal">
                                <span class="font-semibold text-black/50 w-20">Nombre</span>
                                <Input v-model="manager.username" :disabled="true" class="w-80" />
                            </div>
                            <div class="flex items-center justify-start gap-8 w-full font-normal">
                                <span class="font-semibold text-black/50 w-20">Email</span>
                                <Input :disabled="true" v-model="manager.email" class="w-80" />
                            </div>

                        </div>
                    </div>

                    <div class="flex flex-col gap-6">
                        <span class="font-semibold ">Nuevo Gestor</span>
                        <div class="flex flex-col gap-5">
                            <div class="flex items-center justify-start gap-8 w-full font-normal">
                                <span class="font-semibold text-black/50 w-20">Nombre</span>
                                <Input :errorMessage="$vCreateNewManager.username.$errors[0]?.$message.toString()"
                                    v-model="newManagerFormData.username" class="w-80" />
                            </div>
                            <div class="flex items-center justify-start gap-8 w-full font-normal">
                                <span class="font-semibold text-black/50 w-20">Email</span>
                                <Input :errorMessage="$vCreateNewManager.email.$errors[0]?.$message.toString()"
                                    v-model="newManagerFormData.email" class="w-80" />
                            </div>
                            <div class="flex items-center justify-start gap-8 w-full font-normal">
                                <span class="font-semibold text-black/50 w-20">Teléfono</span>
                                <Input :formatter="formatPhoneNumber"
                                    :errorMessage="$vCreateNewManager.phoneNumber.$errors[0]?.$message.toString()"
                                    v-model="newManagerFormData.phoneNumber" class="w-80" />
                            </div>
                            <div class="flex items-center justify-start gap-8 w-full font-normal">
                                <span class="font-semibold text-black/50 w-20">Cargo</span>
                                <Input :errorMessage="$vCreateNewManager.position.$errors[0]?.$message.toString()"
                                    v-model="newManagerFormData.position" class="w-80" />
                            </div>
                            <div class="flex items-center justify-start gap-8 w-full font-normal">
                                <span class="font-semibold text-black/50 w-20">Rut</span>
                                <Input :formatter="formatRUT"
                                    :errorMessage="$vCreateNewManager.rut.$errors[0]?.$message.toString()"
                                    v-model="newManagerFormData.rut" class="w-80" />
                            </div>
                            <div class="flex items-center justify-start gap-8 w-full font-normal">
                                <span class="font-semibold text-black/50 w-20">Contraseña</span>
                                <div class="relative w-80">
                                    <Input :type="passwordVisible ? 'text' : 'password'"
                                        :errorMessage="$vCreateNewManager.password.$errors[0]?.$message.toString()"
                                        v-model="newManagerFormData.password" class="w-full" />
                                    <span
                                        class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 cursor-pointer"
                                        @click="togglePasswordVisibility('password')">
                                        {{ passwordVisible ? 'Ocultar' : 'Mostrar' }}
                                    </span>
                                </div>
                            </div>
                            <div class="flex items-center justify-start gap-8 w-full font-normal">
                                <span class="font-semibold text-black/50 w-20">Confirmar Contraseña</span>
                                <div class="relative w-80">
                                    <Input :type="confirmPasswordVisible ? 'text' : 'password'"
                                        :errorMessage="$vCreateNewManager.confirmPassword.$errors[0]?.$message.toString()"
                                        v-model="newManagerFormData.confirmPassword" class="w-full" />
                                    <span
                                        class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 cursor-pointer"
                                        @click="togglePasswordVisibility('confirmPassword')">
                                        {{ confirmPasswordVisible ? 'Ocultar' : 'Mostrar' }}
                                    </span>
                                </div>
                            </div>

                        </div>
                    </div>

                </section>
            </div>
            <Button type="submit" class="w-80 self-center">Deshabilitar y Reemplazar</Button>
        </form>
    </Modal>
</template>