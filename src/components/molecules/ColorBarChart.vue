<template>
  <div class="flex flex-col items-center w-full h-auto">
    <p class="text-justify text-gray-500">
      El siguiente gráfico presenta una visualización clara de los niveles de madurez alcanzados en
      cada una de las dimensiones evaluadas. Estos niveles reflejan el grado alcanzado según las
      respuestas de sus funcionarios.
    </p>

    <div class="flex justify-between w-full mt-8">
  <div class="flex flex-col">
    <div class="flex items-center mb-6">
      <div class="w-4 h-4 rounded-full mr-2" style="background-color: #4E36E2;"></div>
      <span>Institucionalización</span>
    </div>
    <div class="flex items-center mb-6">
      <div class="w-4 h-4 rounded-full mr-2" style="background-color: #2AA88A;"></div>
      <span>Adquisición de servicios</span>
    </div>
    <div class="flex items-center mb-6">
      <div class="w-4 h-4 rounded-full mr-2" style="background-color: #C860AB;"></div>
      <span>Comunicación</span>
    </div>
  </div>
  <div class="flex flex-col items-start">
    <div class="flex items-center mb-6">
      <div class="w-4 h-4 rounded-full mr-2" style="background-color: #F84646;"></div>
      <span>Adquisición de bienes</span>
    </div>
    <div class="flex items-center mb-6">
      <div class="w-4 h-4 rounded-full mr-2" style="background-color: #6EB4D2;"></div>
      <span>Contratación de personal</span>
    </div>
    <div class="flex items-center mb-6">
      <div class="w-4 h-4 rounded-full mr-2" style="background-color: #FFAB2D;"></div>
      <span>Rendición de cuentas</span>
    </div>
  </div>
</div>


    <div ref="echartsContainer" class="mt-4 echarts w-full h-96"></div>
    <div class="text-center text-xl font-bold text-gray-500 mt-4">Dimensiones</div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, nextTick } from 'vue'
import * as echarts from 'echarts'
import { Logger } from '@/services/logger';
const echartsContainer = ref<HTMLDivElement | null>(null)
const props = defineProps<{
  levelData: number[];
}>();

onMounted(() => {
  nextTick(() => {
    if (!props.levelData || props.levelData.length === 0) {
      Logger.warn('No data available for the chart', props.levelData);
      return;
    }

    const chart = echarts.init(echartsContainer.value!);
    const barColors = ['#4E36E2', '#2AA88A', '#C860AB', '#F84646', '#6EB4D2', '#FFAB2D'];

    chart.setOption({
      tooltip: {},
      grid: {
        left: '5%',
        right: '5%',
        top: '10%',
        bottom: '10%',
      },
      xAxis: {
        type: 'category',
        name: 'Dimensiones',
        nameGap: 35,
        axisLabel: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
        name: 'Niveles',
        nameLocation: 'middle',
        nameRotate: 90,
        min: 0,
        max: 5,
        nameTextStyle: {
          fontSize: 20,
          color: '#333',
        },
        nameGap: 30,
      },
      series: [
        {
          name: 'Nivel de Dimensión',
          type: 'bar',
          data: props.levelData,
          barWidth: '40%',
          itemStyle: {
            borderRadius: [20, 20, 0, 0],
            color: (params: any) => {
              return barColors[params.dataIndex];
            },
          },
        },
      ],
    });
  });
});

</script>

<style scoped>
.echarts {
  width: 100%;
  height: 300px;
}
</style>
