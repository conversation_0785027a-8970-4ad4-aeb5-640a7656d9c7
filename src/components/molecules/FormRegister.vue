<script setup lang="ts">
import Button from '../atoms/Button.vue';
import { institutionRegisterRequestRules } from '@/models/validation/formRules';
import Input from '../atoms/Input.vue';
import type { InstitutionRequestFormSchema } from '@/models/validation/formSchemas';
import { formatPhoneNumber } from '@/helpers/formatters';
import { requestInstitutionRegister } from '@/services/api/institutionService';
import useVuelidate from '@vuelidate/core';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useToast } from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-sugar.css';
import { useAsyncFetch } from '@/hooks/useAsyncFetch';

const $toast = useToast();
const router = useRouter();
const approve = ref<boolean>(true);
const institutionData = ref<InstitutionRequestFormSchema>({
  managerEmail: '',
  institutionName: '',
  phoneNumber: '',
});

const handleSendRegisterRequest = async () => {
  try {
    await requestInstitutionRegister(institutionData.value);
    $toast.success('Solicitud enviada correctamente');
    router.push({ path: '/login' });
  } catch (error) {
    approve.value = false;
    setTimeout(() => {
      approve.value = true;
    }, 2000);
  }
}

const { fetchData: sendRegisterRequest, isLoading } = useAsyncFetch(handleSendRegisterRequest)
const $vCreateRegisterRequest = useVuelidate(institutionRegisterRequestRules, institutionData.value);

const handleSubmit = async () => {
  const result = await $vCreateRegisterRequest.value.$validate();
  if (result) {
    await sendRegisterRequest();
  }
};
</script>

<template>
  <div class="flex items-center justify-center">
    <div class="w-full flex justify-center">
      <div class="w-full max-w-md p-8 bg-white rounded-lg ">
        <div class="flex flex-col h-fit w-full gap-0 *:font-montserrat">
          <h2 class="2xl:header-1 header-4 font-bold text-center leading-none">Solicitud de registro</h2>
          <p class="2xl:header-8 body-3 text-center text-gray-600">Ingrese los datos de su institución</p>
        </div>

        <form @submit.prevent="handleSubmit" class="flex flex-col gap-4">
          <div class="flex flex-col gap-1 mt-10">
            <label for="email" class="block text-sm font-medium text-gray-700">Correo electrónico <span
                v-if="$vCreateRegisterRequest.managerEmail.$errors[0]" class="text-red-600"> (Este campo es
                requerido)</span>
              <span v-else class="text-primary-400">*</span></label>
            <Input :errorMessage="$vCreateRegisterRequest.managerEmail.$errors[0]?.$message.toString()"
              variant="outline" v-model="institutionData.managerEmail" type="email" id="email"
              :showErrorIcon="false"
              placeholder="<EMAIL>" required autofocus class="block w-full shadow-sm sm:text-sm" />
          </div>

          <div class="flex flex-col gap-1">
            <label for="name" class="block text-sm font-medium text-gray-700">Nombre de la institución <span
                v-if="$vCreateRegisterRequest.institutionName.$errors[0]" class="text-red-600"> (Este campo es
                requerido)</span>
              <span v-else class="text-primary-400">*</span></label>
            <Input :errorMessage="$vCreateRegisterRequest.institutionName.$errors[0]?.$message.toString()"
              variant="outline" v-model="institutionData.institutionName" type="text" id="name" required autofocus
              :showErrorIcon="false"
              placeholder="Mi institución" class="block w-full shadow-sm sm:text-sm" />
          </div>

          <div class="flex flex-col gap-1">
            <label for="phone" class="block text-sm font-medium text-gray-700">Telefono de contacto <span
                v-if="$vCreateRegisterRequest.phoneNumber.$errors[0]" class="text-red-600"> (Este campo es
                requerido)</span>
              <span v-else class="text-primary-400">*</span></label>
            <Input :errorMessage="$vCreateRegisterRequest.phoneNumber.$errors[0]?.$message.toString()" variant="outline"
              :formatter="formatPhoneNumber" v-model="institutionData.phoneNumber" type="text" id="phone" required
              :showErrorIcon="false"
              autofocus placeholder="9 12345678" class="block w-full shadow-sm sm:text-sm" />
          </div>

          <div>
            <div v-if="approve" class="mt-8">
              <Button :loading="isLoading" type="submit"
                class="font-normal w-full px-4 py-1 text-white bg-primary-900 border border-transparent rounded-md">
                <span class="font-medium text-base">Enviar solicitud</span>
              </Button>
            </div>
            <div v-else class="mt-8">
              <Button :variant="'missing'" :loading="isLoading" type="submit" class="font-normal w-full px-4 py-1">
                <span class="font-medium text-sm">Su organización ya cuenta con una solicitud</span>
              </Button>
            </div>
          </div>
        </form>
        <div class="flex flex-col items-center justify-center mt-2 text-sm text-gray-600">
          <p class="text-sm text-center text-gray-600">
            ¿Ya tienes una cuenta? <router-link :to="{ path: '/login' }" class="text-primary-900 underline">Iniciar
              sesión</router-link>
          </p>
        </div>
        <div class="w-full flex justify-start mt-28">
          <a href="/" class="flex items-center text-primary-400 hover:underline">
            <i class="fa-solid fa-arrow-left text-neutral-950 mr-1"></i>
            Volver a la página principal
          </a>
        </div>
      </div>
    </div>
  </div>
</template>
