<script setup lang="ts">
import Modal from '../atoms/Modal.vue'
import Button from '../atoms/Button.vue'
import Input from '../atoms/Input.vue'
import { computed, ref, watch } from 'vue'
import { createAuditorRules, updateAuditorRules } from '@/models/validation/formRules'
import type { AuditorFormSchema } from '@/models/validation/formSchemas'
import { useAuditors } from '@/hooks/useAuditors'
import AcronymCircleAvatar from './AcronymCircleAvatar.vue'
import { extractCapitalizedInitials } from '@/helpers/stringHelpers'
import type { Auditor } from '@/models/types'
import { formatPhoneNumber } from '@/helpers/formatters'
import { useForm } from '@/hooks/useForm'
import { mapAuditorToAuditorFormSchema } from '@/mappers'
import { useToast } from 'vue-toast-notification'
const toast = useToast()
const props = defineProps({
  isModalOpen: {
    type: Boolean,
    required: true
  },
  onClose: {
    type: Function,
    required: true
  },
  enable: {
    type: Boolean,
    required: true
  }
})

const auditor = defineModel<Auditor | undefined>({ required: true })
const auditorParsedData = ref()
const isEditMode = ref<boolean>(false)
const showPassword = ref(false)
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

watch(
  auditor,
  (newAuditor) => {
    if (newAuditor) {
      isEditMode.value = true
      auditorParsedData.value = mapAuditorToAuditorFormSchema(newAuditor)
    } else {
      isEditMode.value = false
      auditorParsedData.value = {}
    }
  },
  { deep: true }
)

const { handleCreateAuditor, handleUpdateAuditor, handleToggleAuditorStatus } = useAuditors()
const handleClose = () => {
  props.onClose()
}
const validationRules = computed(() =>
  auditor.value ? updateAuditorRules.value : createAuditorRules.value
)

const {
  formData: auditorFormData,
  $vForm: $vCreateAuditor,
  handleSubmit,
  isLoading,
  hasChanges
} = useForm<AuditorFormSchema>({
  initialDataRef: auditorParsedData,
  validationRules: validationRules,
  updateOnlyModified: true,
  updateCondition: isEditMode,
  onSubmit: async (data) => {
    if (auditor.value) {
      await handleUpdateAuditor(auditor.value.id, data)
      props.onClose()
    } else {
      await handleCreateAuditor(data)
      props.onClose()
    }
  }
})

const handleColorSelected = (color: string) => {
  auditorFormData.color = color
}
const reloadPage = () => {
  toast.info('Auditor deshabilitado correctamente', {
    duration: 3000,
  })
  window.location.reload()
}

const onNameChange = (newName: string) => {
  auditorFormData.acronym = extractCapitalizedInitials(newName)
}
</script>

<template>
  <Modal :isOpen="isModalOpen" :onClose="onClose">
    <div class="flex flex-col gap-8 2xl:w-[50vw] 2xl:h-[70vh] xl:w-[56vw] xl:h-[73vh] relative">
      <button
        class="absolute top-4 right-4 w-8 h-8 rounded-full bg-red-600 text-white text-2xl flex items-center justify-center font-bold"
        @click="handleClose"
      >
        &times;
      </button>

      <h3 class="flex flex-col gap-2.5 text-xl font-bold">
        {{ auditor ? 'Editar Auditor' : 'Crear Nuevo Auditor' }}
        <p class="text-sm font-normal">
          {{
            auditor
              ? 'Editar los datos y credenciales del auditor'
              : 'Genera la cuenta del nuevo auditor junto a sus credenciales.'
          }}
        </p>
      </h3>

      <div class="border-t border-black/20"></div>

      <div class="flex w-full h-full gap-10">
        <AcronymCircleAvatar
          :color="auditorFormData.color"
          class="z-20"
          :acronym="auditorFormData.acronym"
          :onColorSelected="handleColorSelected"
        />
        <form @submit.prevent="handleSubmit" class="flex flex-col gap-8 w-full h-full">
          <div class="flex items-center gap-8 w-full">
            <span class="font-semibold text-black/50 w-[20%]">Nombre</span>
            <Input
              @input="onNameChange($event.target.value)"
              :errorMessage="$vCreateAuditor.username.$errors[0]?.$message.toString()"
              v-model="auditorFormData.username"
              class="w-[75%]"
            />
          </div>
          <div class="flex items-center gap-8 w-full">
            <span class="font-semibold text-black/50 w-[20%]">Email</span>
            <Input
              :errorMessage="$vCreateAuditor.email.$errors[0]?.$message.toString()"
              v-model="auditorFormData.email"
              class="w-[75%]"
            />
          </div>
          <div class="flex items-center gap-8 w-full">
            <span class="font-semibold text-black/50 w-[20%]">Teléfono</span>
            <Input
              :formatter="formatPhoneNumber"
              :errorMessage="$vCreateAuditor.phoneNumber.$errors[0]?.$message.toString()"
              v-model="auditorFormData.phoneNumber"
              class="w-[75%]"
            />
          </div>
          <div class="flex items-center gap-8 w-full">
            <span class="font-semibold text-black/50 w-[20%]">Ciudad</span>
            <Input
              :errorMessage="$vCreateAuditor.city.$errors[0]?.$message.toString()"
              v-model="auditorFormData.city"
              class="w-[75%]"
            />
          </div>
          <div class="flex items-center gap-8 w-full">
            <span class="font-semibold text-black/50 w-[20%]">Acrónimo</span>
            <Input
              :errorMessage="$vCreateAuditor.acronym.$errors[0]?.$message.toString()"
              v-model="auditorFormData.acronym"
              class="w-[75%]"
            />
          </div>
          <div v-if="!auditor" class="flex items-center gap-8 w-full">
            <span class="font-semibold text-black/50 w-[20%]">Contraseña</span>
            <div class="relative w-[75%]">
              <Input
                :errorMessage="$vCreateAuditor.password.$errors[0]?.$message.toString()"
                v-model="auditorFormData.password"
                :type="showPassword ? 'text' : 'password'"
                class="w-full"
              />
              <button
                type="button"
                @click="togglePasswordVisibility"
                class="text-sm leading-5 text-gray-500 absolute right-6 top-1/2 transform -translate-y-1/2"
              >
                <span v-if="showPassword">Ocultar</span>
                <span v-else>Mostrar</span>
              </button>
            </div>
          </div>
          <div v-if="!auditor" class="flex items-center gap-8 w-full">
            <span class="font-semibold text-black/50 w-[20%]">Confirmar contraseña</span>
            <div class="relative w-[75%]">
              <Input
                :errorMessage="$vCreateAuditor.confirmPassword.$errors[0]?.$message.toString()"
                v-model="auditorFormData.confirmPassword"
                :type="showPassword ? 'text' : 'password'"
                class="w-full"
              />
              <button
                type="button"
                @click="togglePasswordVisibility"
                class="text-sm leading-5 text-gray-500 absolute right-6 top-1/2 transform -translate-y-1/2"
              >
                <span v-if="showPassword">Ocultar</span>
                <span v-else>Mostrar</span>
              </button>
            </div>
          </div>
          <div class="flex w-full justify-around">
            <Button
              v-if="auditor && props.enable"
              @click="
                () => {
                  handleToggleAuditorStatus(auditor!.id)
                  onClose()
                  reloadPage()
                }
              "
              variant="danger"
            >
              Deshabilitar
            </Button>
            <div v-else></div>
            <Button
              type="submit"
              class="w-40"
              :loading="isLoading"
              :disabled="$vCreateAuditor.$errors.length > 0 || (auditor && !hasChanges)"
            >
              {{ auditor ? 'Actualizar' : 'Crear' }}
            </Button>
          </div>
        </form>
      </div>
    </div>
  </Modal>
</template>
