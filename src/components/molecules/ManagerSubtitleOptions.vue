<script setup lang="ts">
import UpdateManagerModal from './UpdateManagerModal.vue';
import type { Gestor } from '@/models/types';
import { fetchManagerByID } from '@/services/api/managerService';
import { ref } from 'vue';
import DisableManagerModal from './DisableManagerModal.vue';

const props = defineProps({
    managerId: {
        type: Number,
        required: true
    }
})

const manager = ref<Gestor>();
const showDisableModal = ref<boolean>(false);
const showEditModal = ref<boolean>(false);

const fetchManager = async () => {
    manager.value = await fetchManagerByID(props.managerId);
}

await fetchManager();
</script>

<template>
    <div class="text-sm font-semibold absolute top-10 flex items-center gap-5">
        <span>
            Gestionada por {{ manager?.username }}
        </span>
        <span class="flex items-center gap-3">
            <i @click="showEditModal = true"
                class="fa-solid fa-pen cursor-pointer hover:text-primary-800 transition-all duration-200"></i>
            <i @click="showDisableModal = true"
                class="fa-solid fa-trash text-red-400 hover:text-red-600 cursor-pointer transition-all duration-200"></i>
        </span>
    </div>
    <DisableManagerModal v-if="manager" :manager="manager" :isModalOpen="showDisableModal"
        :onClose="() => { showDisableModal = false }" />
    <UpdateManagerModal v-if="manager" :manager="manager" :isModalOpen="showEditModal"
        :onClose="() => { showEditModal = false }" />
</template>