<script setup lang="ts">
import Modal from '@/components/atoms/Modal.vue';
import Button from '../atoms/Button.vue';
import { Logger } from '@/services/logger';
const props = defineProps({
    onSend: {
        type: Function,
        required: true
    }
})

const isSendSurveysModalOpen = defineModel<boolean>();

const closeModal = () => {
    isSendSurveysModalOpen.value = false;
};

const handleSendSurveys = () => {
    try {
        props.onSend();
        closeModal();
    } catch (error) {
        Logger.error('Error fetching auditor data:', error);
    }
}

</script>

<template>
    <Modal :isOpen="isSendSurveysModalOpen ?? false" :onClose="closeModal">
        <div class="flex w-[50vw] flex-col gap-12 p-6">
            <h1 class="text-center font-semibold text-3xl">¿Está seguro que desea enviar las encuestas?</h1>
            <p class="text-center px-8">Una vez que haya enviado sus encuestas, no podrá modificar las evidencias que ha
                subido. Esta acción es irreversible, por lo que cualquier cambio que desee realizar debe hacerse antes
                de enviar las encuestas.</p>

            <div class="flex w-full justify-evenly items-center">
                <Button @click="closeModal" variant="invert">
                    Cancelar
                </Button>
                <Button @click="handleSendSurveys" class="px-8">
                    Enviar
                </Button>
            </div>
        </div>
    </Modal>
</template>