<script setup lang="ts">
import { computed } from 'vue';
import type { Tab } from '@/hooks/useTabs';

const props = defineProps({
    tabs: {
        type: Array as () => Tab[],
        required: true,
    },
    activeTab: {
        type: String,
        required: true,
    },
    onTabChange: {
        type: Function,
        required: false,
    },
    effect: {
        type: String as () => 'tab' | 'progress',
        default: 'tab',
    },
    auditorEnable: {
        type: Boolean,
        required: false,
    },
});

const filteredTabs = computed(() => 
    props.auditorEnable ? props.tabs : props.tabs.slice(0, -1)
);

const isTabPressed = (tabName: string) => {
    if (props.effect === 'progress') {
        const activeIndex = props.tabs.findIndex(tab => tab.name === props.activeTab);
        const tabIndex = props.tabs.findIndex(tab => tab.name === tabName);
        return tabIndex <= activeIndex;
    }
    return props.activeTab === tabName;
};

const canClickTab = props.effect === 'tab' && props.onTabChange;

const handleTabClick = (tabName: string) => {
    if (canClickTab) {
        props.onTabChange(tabName);
    }
};
</script>

<template>
    <div class="flex w-full justify-between items-center gap-10">
        <span 
            v-for="tab in filteredTabs" 
            :key="tab.name" 
            class="flex text-sm flex-col gap-1 flex-1 py-2 px-2 select-none"
            @click="handleTabClick(tab.name)"
            :class="{
                '': activeTab === tab.name,
                'cursor-pointer hover:bg-primary-50 transition-all duration-300 rounded-md': canClickTab,
            }"
        >
            {{ tab.name }}
            <span 
                :class="{
                    'bg-primary-700': isTabPressed(tab.name),
                    'bg-primary-200': !isTabPressed(tab.name),
                }" 
                class="h-1 w-full rounded-full">
            </span>
        </span>
    </div>
</template>


<style scoped>
.tab-indicator-enter-active,
.tab-indicator-leave-active {
    transition: width 0.5s, background-color 0.5s;
}

.tab-indicator-enter-from,
.tab-indicator-leave-to {
    width: 0;
    background-color: #d8d4ff;
}
</style>