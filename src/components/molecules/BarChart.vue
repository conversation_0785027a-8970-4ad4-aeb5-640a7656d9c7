<template>
  <div class="flex justify-between items-center">
    <h2 class="2xl:header-4 header-7">Media de encuestas respondidas</h2>
    <button class="2xl:w-40 w-24 h-10 2xl:body-3 body-5 font-semibold text-primary-400 border border-black/30 hover:border-primary-900 hover:bg-primary-50 rounded-xl" @click="isModalVisible = true">Ver histórico</button>
  </div>
  <div class="mt-4 flex flex-col items-start space-y-2">

  <div class="flex items-center 2xl:space-x-4 space-x-2 w-full">
    <div class="2xl:w-7 w-5 h-1 border-b-2 border-dashed border-green-600"></div>
    <p class="2xl:body-3 body-5 text-black">Media real de encuestas respondidas</p>
  </div>

  <div class="flex items-center 2xl:space-x-4 space-x-2 w-full">
    <div class="2xl:w-7 w-5 h-1 border-b-2 border-dashed border-purple-500"></div>
    <p class="2xl:body-3 body-5 text-black">Media diaria de encuestas respondidas</p>
  </div>
</div>

  <!-- Modal -->
  <div v-if="isModalVisible" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
    <div class="flex flex-col bg-white p-6 w-[60%] h-[75%] rounded-2xl shadow-lg">
      <div class="flex justify-between items-center">
        <h2 class="2xl:header-5 header-7 font-semibold">Media de encuestas respondidas</h2>
        <Button @click="isModalVisible = false" class="mt-4">Cerrar</Button>
      </div>
      <HistoricModal 
        :totalAnswers="totalAnswers" 
        :endDate="endDate" 
        :startDate="startDate"
        :mean="mean"
        :idealAverage="idealAverage"
        :lastTenDays="lastTenDays"
      />
    </div>
  </div>

  <div ref="echartsContainer" class="echarts w-full h-full"></div>
  
  <div class="flex flex-col 2xl:mt-0 mt-2 items-end space-y-1">
      <div class="flex items-center space-x-1">
        <span class="2xl:w-4 w-2 2xl:h-4 h-2 rounded-full bg-primary-800"></span>
        <p class="2xl:body-4 body-6 text-black">Encuestas Respondidas</p>
      </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref, onBeforeUnmount, computed } from 'vue'
import * as echarts from 'echarts'
import Button from '../atoms/Button.vue';
import HistoricModal from './HisstoricModal.vue';

const is2xlScreen = computed(() => {
  return window.matchMedia("(min-width: 1536px)").matches;
});

const echartsContainer = ref<HTMLDivElement | null>(null)
  let chartInstance: echarts.ECharts | null = null; 

const isModalVisible = ref(false)
const props = defineProps<{
  totalAnswers: number
  startDate: string
  endDate: string
  mean: number
  idealAverage: number
  lastTenDays: { day: number; month: string; responses: number; dayName: string }[]
}>();

const lastSevenDaysNames = ref<string[]>([]);
const lastSevenDaysResponses = ref<number[]>([]);


const createChart = () => {
  if (echartsContainer.value) {
    const chart = echarts.init(echartsContainer.value!);
  
  const lastSevenDays = props.lastTenDays.slice(0, 7);
  const reversedDays = lastSevenDays.reverse();

  lastSevenDaysNames.value = reversedDays.map(day => day.dayName);
  lastSevenDaysResponses.value = reversedDays.map(day => day.responses);

  const meanActual = props.mean;
  const meanExpected = props.idealAverage;
  chart.setOption({
    tooltip: {},
    grid: {
      left: is2xlScreen.value ? '5%' : '8%',
      right: '0%',
      top: is2xlScreen.value ? '10%' : '10%',
      bottom: is2xlScreen.value ? '8%' : '12%'
    },
    xAxis: {
      type: 'category',
      data: lastSevenDaysNames.value 
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: props.totalAnswers
    },
    series: [
      {
        name: 'Respuestas Actuales',
        type: 'bar',
        data: lastSevenDaysResponses.value,
        itemStyle: {
          color: '#4b1abd',
          borderRadius: [20, 20, 0, 0]
        },
        barWidth: '50%'
      },
      {
        name: 'Media Actual',
        type: 'line',
        data: [meanActual, meanActual, meanActual, meanActual, meanActual, meanActual, meanActual],
        lineStyle: {
          color: 'green',
          type: 'dashed'
        },
        symbol: 'none'
      },
      {
        name: 'Media Esperada',
        type: 'line',
        data: [
          meanExpected,
          meanExpected,
          meanExpected,
          meanExpected,
          meanExpected,
          meanExpected,
          meanExpected
        ],
        lineStyle: {
          color: 'purple',
          type: 'dashed'
        },
        symbol: 'none'
      }
    ]
  });
  }
};

const destroyChart = () => {
 if (chartInstance) {
   chartInstance.dispose(); 
   chartInstance = null; 
 }
};

onMounted(() => {
 createChart(); 

 const handleResize = () => {
   destroyChart();
   createChart(); 
 };

 window.addEventListener('resize', handleResize);

 onBeforeUnmount(() => {
   window.removeEventListener('resize', handleResize);
   destroyChart();
 });
});
</script>