<!-- eslint-disable vue/no-parsing-error -->
<template>
  <section id="inicio"
    class="relative flex 2xl:px-24 px-20 2xl:py-72 bg-background items-center mt-16 min-h-screen 2xl:max-h-[91vh] max-h-[89vh] overflow-hidden">
    <div class="absolute inset-0 p-2 box-border">
      <div class="w-full h-full 2xl:max-h-[91vh] max-h-[89vh] relative rounded-2xl overflow-hidden">
        <img src="@/assets/image/landing-bg.jpg" alt="Fondo"
          class="w-full h-full object-cover 2xl:object-[bottom] md:object-bottom rounded-2xl"
          style="object-position: center bottom;" />
        <div class="absolute inset-0 bg-gradient-to-l from-black/90 via-transparent to-transparent rounded-2xl"></div>
        <div class="absolute inset-0 bg-gradient-to-r from-black/100 via-transparent to-transparent rounded-2xl"></div>

        <div class="absolute bottom-8 left-0 right-0 flex justify-center w-full z-10">
          <p class="2xl:text-xl text-sm font-bold text-white animate-pulse">
            << Baja para profundizar en nuestra solución>>
          </p>
        </div>
      </div>
    </div>
    <div class="relative z-10 text-left text-white p-5 pt-20 2xl:pt-0">
      <div class="mx-auto max-w-7xl">
        <h2 class="2xl:text-8xl text-5xl font-extrabold font-montserrat mb-6">Prueba tu</h2>
        <h2 class="2xl:text-8xl text-5xl font-extrabold font-montserrat mb-6">Transparencia</h2>
        <p class="2xl:text-2xl body-1" style="text-align:justify;">
          Nos esforzamos por crear un entorno laboral más
        </p>
        <p class="2xl:text-2xl body-1 mb-10" style="text-align:justify;">
          transparente y comunicativo para tu organización.
        </p>
        <div class="flex justify-left space-x-4">
            <BotonComponent  @click="scrollToContacto"
              class="text-xl h-10 w-36 2xl:text-xl 2xl:h-15 2xl:w-48 border border-primary-400 rounded-full font-bold">
              Contáctanos
            </BotonComponent>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import BotonComponent from '../atoms/Button.vue';
const scrollToContacto = () => {
  const contactoSection = document.getElementById('contacto');
  if (contactoSection) {
    contactoSection.scrollIntoView({ behavior: 'smooth' });
  }
};
</script>
