<template>
  <section class="flex flex-col w-full mt-5 2xl:mt-8 gap-10 2xl:gap-12">
    <section class="flex flex-col lg:flex-row w-full 2xl:gap-12 gap-8">
      <!-- Columna <PERSON>a -->
      <section class="flex flex-col gap-10 w-2/6 2xl:1/5">
        <Card class="flex flex-col gap-8 h-full flex-1">
          <div class="flex flex-col w-full">
            <p class="text-lg font-semibold">Dimensiones objetadas</p>
            <p class="body-3 text-justify mt-6">
              En esta sección se presentan las dimensiones y evidencias objetadas, con el fin de
              actualizar la entrega corregida conforme a las observaciones del auditor.
            </p>
          </div>
          <hr class="border-t-1 border-gray-500 my-1" />
          <div>
            <ul class="space-y-4">
              <li v-for="(section, index) in allSections" :key="index" class="flex flex-col items-start gap-2">
                <div class="flex items-center gap-2 cursor-pointer" @click="selectSection(section)">
                  <i :class="section.icon" class="text-xl" :style="{ color: section.color }"></i>
                  <p class="text-md">{{ section.name }}</p>
                </div>
                <div class="w-full border-t-2 border-gray-300 mt-2"></div>
              </li>
            </ul>
          </div>
        </Card>
      </section>

      <!-- Columna Derecha -->
      <section class="flex flex-col flex-1 gap-10 w-4/6 2xl:4/5">
        <Card class="flex flex-col" v-if="selectedSection">
          <h3 class="text-lg font-semibold">Evidencias objetadas</h3>
          <hr class="border-t-1 border-gray-500 my-4" />
          <p class="body-4">Evidencias</p>
          <section class="flex flex-wrap gap-4 mt-8">
            <ObservationAppealCard v-for="(comment, index) in selectedSection.comments" :key="index"
              :evidenceName="comment.evidenceName" :evidenceLocation="comment.evidenceLocation"
              :comment="comment.comment" />
          </section>
          <div class="flex justify-end mt-6">
            <Button class="rounded-full 2xl:h-23 h-8 w-44 body-4">
              Terminar y enviar
            </Button>
          </div>
        </Card>
      </section>
    </section>
  </section>
</template>

<script setup lang="ts">
import Card from '@/components/atoms/Card.vue'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import ObservationAppealCard from './ObservationAppealCard.vue'
import Button from '../atoms/Button.vue'
const route = useRoute()
const allSections = route.query.allSections ? JSON.parse(route.query.allSections as string) : []

const selectedSection = ref<any | null>(null)
const showMore = ref(false)

const selectSection = (section: any) => {
  selectedSection.value = section
  showMore.value = false
}
</script>
