<script setup lang="ts">
import { useEmployees } from '@/hooks/useEmployees';
import Pagination from '../atoms/Pagination.vue';
import 'vue-toast-notification/dist/theme-sugar.css';
import { useToast } from 'vue-toast-notification';
import type { PropType } from 'vue';

const props = defineProps({
  employeeses: {
    type: Array as PropType<Array<{ name: string; email: string; rut: string }>>,
    required: true
  }
})

const $toast = useToast();
const { employees, handleEmployeesFileUpload, page, totalPages, handleGoToPage, handleNextPage, handlePreviousPage, totalEmployees } = useEmployees();

const handleEmployeesUpload = async (event: Event) => {
  try {
    await handleEmployeesFileUpload(event);
  } catch (error: any) {
    $toast.error('Error al importar funcionarios: ' + error.message);
  }
};
</script>
<template>
     <div v-if="props.employeeses.length > 0" class="flex space-x-2">
      </div>
    <div class="overflow-x-auto rounded-2xl flex flex-col gap-4">
      <table class="w-full text-sm text-left rtl:text-right text-gray-700">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left">ID</th>
            <th scope="col" class="px-6 py-3 text-left">Nombre</th>
            <th scope="col" class="px-6 py-3 text-left">Email</th>
            <th scope="col" class="px-6 py-3 text-left">Rut</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-if="employees.length === 0">
            <td class="px-6 py-4 whitespace-nowrap" colspan="4">
              <div class="flex justify-center items-center h-40">
                <label for="file-upload" class="bg-primary-900 text-white px-6 py-3 rounded-full cursor-pointer">
                  Importar funcionarios
                </label>
                <input id="file-upload" type="file" class="hidden" @change="handleEmployeesUpload">
              </div>
            </td>
          </tr>
          <tr v-else v-for="(item, index) in employees" :key="index">
            <td class="px-6 py-4 whitespace-nowrap">{{ index + 1 }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ item.name }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ item.email }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ item.rut }}</td>
          </tr>
        </tbody>
      </table>
      <div v-if="employees.length > 0" class="flex items-center">
        <p class="text-gray-500 text-sm ml-2 text-nowrap">Mostrando {{ (page + 1) * 12 }} de {{ totalEmployees }}
          funcionarios </p>
        <Pagination :itemCount="employees.length" :totalPages="totalPages" :page="page" @next="handleNextPage"
          @previous="handlePreviousPage" @go-to-page="handleGoToPage" />
      </div>
    </div>
</template>