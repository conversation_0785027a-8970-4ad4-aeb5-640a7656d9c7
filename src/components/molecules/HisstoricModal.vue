<template>
    <div class="mt-4 flex flex-wrap justify-start space-x-16">
      <div class="flex flex-col items-start space-y-1">
        <div class="flex items-center space-x-4">
          <div class="2xl:w-7 w-5 h-1 border-b-2 border-dashed border-green-600"></div>
          <p class="2xl:body-3 body-4 text-black">Media real de encuestas respondidas</p>
        </div>
        <p class="text-lg mx-12 text-black font-bold">{{ mean }}</p>
      </div>
  
      <div class="flex flex-col items-start space-y-1">
        <div class="flex items-center space-x-4">
          <div class="w-7 h-1 border-b-2 border-dashed border-purple-500"></div>
          <p class="2xl:body-3 body-4 text-black">Media diaria necesaria para cumplir el plazo</p>
        </div>
        <p class="text-lg text-black mx-12 font-bold">{{ idealAverage }}</p>
      </div>
    </div>
    <div ref="echartsContainer" class="echarts w-full h-50"></div>
    <div>
      <p class="2xl:body-3 body-4 text-gray-600">
        La <span class="text-purple-500 font-semibold">media diaria necesaria para cumplir el plazo</span> indica cuántas encuestas respondidas por día se necesitan para <span class="font-bold text-black">completar</span> las encuestas <span class="font-bold text-black">dentro del plazo establecido desde hoy en adelante.</span>
      </p>
    </div>
  </template>
  
  <script setup lang="ts">
import { onMounted, ref } from 'vue';
import * as echarts from 'echarts';

const echartsContainer = ref<HTMLDivElement | null>(null);
const props = defineProps<{
  totalAnswers: number;
  startDate: string;
  endDate: string;
  mean: number;
  idealAverage: number;
  lastTenDays: { day: number; month: string; responses: number; dayName: string }[]
}>();

onMounted(() => {
  const chart = echarts.init(echartsContainer.value!);
  const meanActual = props.mean;
  const meanExpected = props.idealAverage;

  const sortedDays = [...props.lastTenDays].sort((a, b) => {
    const monthMap: {[key: string]: number} = {
      'enero': 1, 'febrero': 2, 'marzo': 3, 'abril': 4, 
      'mayo': 5, 'junio': 6, 'julio': 7, 'agosto': 8, 
      'septiembre': 9, 'octubre': 10, 'noviembre': 11, 'diciembre': 12
    };
  
    const monthA = monthMap[a.month.toLowerCase()];
    const monthB = monthMap[b.month.toLowerCase()];
    
    if (Math.abs(monthA - monthB) > 6) {
      return monthB - monthA;
    }
    
    if (monthA !== monthB) {
      return monthA - monthB;
    }
    return a.day - b.day;
  });

  const xAxisData = sortedDays.map(day => `${day.day} ${day.month.substring(0, 3)}`); 
  const responseData = sortedDays.map(day => day.responses); 
  
  chart.setOption({
    tooltip: {},
    grid: {
      left: '5%',
      right: '0%',
      top: '10%',
      bottom: '10%'
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: props.totalAnswers
    },
    series: [
      {
        name: 'Respuestas Actuales',
        type: 'bar',
        data: responseData, 
        itemStyle: {
          color: '#4b1abd',
          borderRadius: [20, 20, 0, 0]
        },
        barWidth: '50%'
      },
      {
        name: 'Media Actual',
        type: 'line',
        data: Array(xAxisData.length).fill(meanActual),
        lineStyle: {
          color: 'green',
          type: 'dashed',
          width: 3
        },
        symbol: 'none'
      },
      {
        name: 'Media Esperada',
        type: 'line',
        data: Array(xAxisData.length).fill(meanExpected),
        lineStyle: {
          color: 'purple',
          type: 'dashed',
          width: 3
        },
        symbol: 'none'
      }
    ]
  });
});
</script>
  
  <style scoped>
  .echarts {
    width: 100%;
    height: 80%;
  }
  </style>
  