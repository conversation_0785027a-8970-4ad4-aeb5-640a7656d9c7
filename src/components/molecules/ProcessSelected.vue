<template>
  <card>
    <div class="p-4">
      <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold">Proceso de autodiagnóstico general</h1>
        <request @click="toggleState" class="bg-primary-900 text-white px-8 py-4 text-xl rounded">Solicitar</request>
      </div>
      <p class="mt-2 font-bold text-primary-600">e-transparencia</p>
      
      <h2 class="mt-4 text-xl font-bold">Descripción general</h2>
      <p class="mt-2">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum</p>
      
      <div class="mt-6 py-4 font-semibold">
        <div class="flex items-center">
          <i class="fas fa-check text-2xl text-green-500 mr-4"></i>
          <span>6 dimensiones</span> 
        </div>
        <hr class="my-2">
        <div class="flex items-center">
          <i class="fas fa-check text-2xl text-green-500 mr-4"></i>
          <span>54 preguntas</span>
        </div>
        <hr class="my-2">
        <div class="flex items-center">
          <i class="fas fa-check text-2xl text-green-500 mr-4"></i>
          <span>18 evidencias</span>
        </div>
        <hr class="my-2">
      </div>
    </div>
  </card>
</template>

<script setup lang="ts">
import { defineEmits } from 'vue';
import card from '@/components/atoms/Card.vue';
import request from '@/components/atoms/Button.vue';

const emit = defineEmits(['toggle-state']);

function toggleState() {
  emit('toggle-state');
}
</script>
