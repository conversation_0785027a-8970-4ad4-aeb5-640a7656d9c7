<script setup lang="ts">
import Card from '../atoms/Card.vue';
import { useAuditors } from '@/hooks/useAuditors';
import { useClickOutside } from '@/hooks/useClickOutside';
import type { Auditor } from '@/models/types';
import { onMounted, ref } from 'vue';
import TextSkeleton from '../templates/TextSkeleton.vue';
import { useSearch } from '@/hooks/useSearch';

const selectedAuditorId = defineModel<number>({
    required: true,
});

const emit = defineEmits(['update:modelValue']);

const auditor = ref<Auditor>();

const menuRef = ref<HTMLElement | null>(null);
const isMenuOpen = ref(false);

const { auditors, isLoading, handleSearchAuditors, handleGetAuditorByID } = useAuditors();

const { searchTerm } = useSearch(async (term: string) => {
    isMenuOpen.value = true;
    await handleSearchAuditors(term);
});


useClickOutside([menuRef], () => {
    isMenuOpen.value = false;
});

onMounted(async () => {
    if (selectedAuditorId.value !== -1) {
        auditor.value = await handleGetAuditorByID(selectedAuditorId.value);
    } else {
        // Precargar auditores para que estén disponibles cuando se haga focus
        await handleSearchAuditors('');
    }
});

const handleSelectAuditor = async (selectedAuditor: Auditor) => {
    selectedAuditorId.value = selectedAuditor.id;
    isMenuOpen.value = false;
    auditor.value = selectedAuditor;
};

const handleUnselectAuditor = () => {
    selectedAuditorId.value = -1;
    auditor.value = undefined;
};

const handleInputFocus = async () => {
    isMenuOpen.value = true;
    // Si no hay término de búsqueda, cargar todos los auditores
    if (!searchTerm.value.trim()) {
        await handleSearchAuditors('');
    }
};


</script>

<template>
    <div class="flex flex-col gap-2">
        <div v-if="selectedAuditorId !== -1 && isLoading">
            <TextSkeleton />
        </div>
        <label v-else ref="menuRef" class="font-semibold flex flex-col gap-3 relative">Auditor
            <span class="flex items-center gap-3 py-2 font-normal group transition-all duration-200
            hover:bg-primary-100 w-60 px-2 rounded-lg" v-if="auditor">
                <i @click="handleUnselectAuditor"
                    class="hidden group-hover:block fa-solid fa-trash text-red-500 cursor-pointer hover:text-red-700 transition-all duration-200 "></i>
                <span class="flex items-center justify-center w-7 h-7 rounded-full text-white text-sm "
                    :style="{ backgroundColor: auditor.color }">{{ auditor.acronym }}</span>
                {{ auditor?.username }}
            </span>
            <div v-else class="relative border border-primary-200 shadow-md shadow-primary-200 rounded-lg">
                <i class="fa-solid fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                <input type="text" v-model="searchTerm"
                    @focus="handleInputFocus"
                    class=" rounded-lg px-2 py-2.5 ml-10 text-black/60 outline-none font-medium"
                    placeholder="Buscar auditor..." />
            </div>
            <Card v-if="isMenuOpen"
                class="absolute top-[5.4rem] bg-white shadow-lg z-20 w-full px-2 py-2 font-normal border-primary-300">
                <div class="flex w-full justify-center p-3" v-if="isLoading" role="status">
                    <svg aria-hidden="true" class="w-8 h-8 text-gray-200 animate-spin fill-primary-600"
                        viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="currentColor" />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentFill" />
                    </svg>
                    <span class="sr-only">Loading...</span>
                </div>
                <div class="w-full text-sm" v-else>
                    <ul class="w-full" v-if="auditors.length > 0">
                        <li class="flex items-center gap-3 hover:bg-primary-100 cursor-pointer w-full p-3 rounded-lg transition-all duration-200"
                            @click="handleSelectAuditor(auditor)" v-for="auditor in auditors" :key="auditor.id">
                            <span class="flex items-center justify-center w-7 h-7 rounded-full text-white text-sm "
                                :style="{ backgroundColor: auditor.color }">{{ auditor.acronym }}</span>
                            {{ auditor.username }}
                        </li>
                    </ul>
                    <div class="p-3" v-else>No hay resultados</div>
                </div>
            </Card>
        </label>
    </div>

</template>