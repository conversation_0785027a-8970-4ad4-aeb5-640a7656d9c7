<script setup lang="ts">
import type { Institution } from '@/models/types';
import Card from '../atoms/Card.vue';
import { defineProps } from 'vue';
import { formatLabel } from '@/helpers/stringHelpers';

defineProps({
    institution: {
        type: Object as () => Institution,
        required: true,

    },
});

</script>

<template>

    <Card class="flex flex-col gap-12 max-h-min">
        <div class="flex flex-col gap-4">
            <div class="flex w-full justify-between">
                <span class="flex flex-col gap-1.5">
                    <p class="text-sm font-semibold text-black/50">
                        {{ formatLabel(institution.nemployees, "funcionario", "funcionarios") }}</p>

                    <h3 class="font-semibold">{{ institution.name }}</h3>
                </span>
                <span class="h-12 w-12 p-2 rounded-lg flex items-center justify-center font-semibold text-white"
                    :style="{ backgroundColor: institution.color }">
                    {{ institution.acronym }}
                </span>
            </div>

            <p class="text-sm font-semibold text-primary-700">
                {{ formatLabel(institution.nprocesses, "proceso realizado", "procesos realizados") }}
            </p>
        </div>

        <div class="flex w-full justify-between items-center">
            <slot name="options"></slot>
            <span v-if="institution.auditor" class="text-sm font-semibold text-black/60">
                {{ institution.auditor?.username }}
            </span>
            <slot v-else name="assign-auditor"></slot>
        </div>
    </Card>

</template>