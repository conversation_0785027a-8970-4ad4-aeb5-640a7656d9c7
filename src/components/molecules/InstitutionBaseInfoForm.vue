<script setup lang="ts">
import Input from '../atoms/Input.vue'
import Button from '../atoms/Button.vue'
import ColorPicker from './ColorPicker.vue'
import { ref, toRef, computed, watch } from 'vue'
import { extractCapitalizedInitials } from '@/helpers/stringHelpers'
import { createInstitutionBaseRules } from '@/models/validation/formRules'
import AcronymCircleAvatar from './AcronymCircleAvatar.vue'
import type { InstitutionBaseInfoSchema } from '@/models/validation/formSchemas'
import { useInstitutions } from '@/hooks/useInstitutions'
import type { Institution } from '@/models/types/institution'
import { useForm } from '@/hooks/useForm'
import regionsAndCities from '@/assets/JsonData/regionsAndCities.json'

/**
 * formulario que corresponde al primer paso del registro de una institución
 * (nombre, región, ciudad, dirección, acrónimo, email, contraseña, confirmar contraseña)
 *
 * @prop {InstitutionBaseInfoSchema} savedInstitutionData - datos de la institución guardados
 * @prop {Function} onNext - función que se ejecuta al dar click en el botón de siguiente como
 * callback
 **/

const props = defineProps({
  institution: {
    type: Object as () => Institution,
    required: false
  },
  update: {
    type: Boolean,
    default: false
  },
  savedInstitutionData: {
    type: Object as () => InstitutionBaseInfoSchema,
    required: true
  },
  onNext: {
    type: Function,
    required: true
  },
  enable: {
    type: Boolean,
    required: true
  }
})

const isColorPickerOpen = ref(false)
const institutionBaseRules = createInstitutionBaseRules(!props.update)
const { handleToggleInstitutionStatus } = useInstitutions()

interface RegionCity {
  region: string
  cities: string[]
}

const allRegions = regionsAndCities as RegionCity[]

const {
  formData: institutionData,
  $vForm: $vCreateInstitution,
  hasChanges,
  handleSubmit,
  isLoading
} = useForm({
  initialDataRef: toRef(props.savedInstitutionData),
  validationRules: institutionBaseRules,
  updateCondition: toRef(props.update),
  onSubmit: async (data) => {
    const newData = { ...data }

    if (!data.color) {
      newData.color = '#5C3D99'
    }

    await props.onNext(newData)
  }
})

const handleDisable = async () => {
  if (props.institution?.id) {
    await handleToggleInstitutionStatus(props.institution.id)
    window.location.reload()
  }
}

const onNameChange = (newName: string) => {
  institutionData.acronym = extractCapitalizedInitials(newName)
}

const onColorSelected = (color: string) => {
  institutionData.color = color
  isColorPickerOpen.value = false
}

const showPassword = ref(false)
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

const filteredCities = computed(() => {
  const selected = allRegions.find(r => r.region === institutionData.region)
  return selected ? selected.cities : []
})

watch(() => institutionData.region, () => {
  institutionData.city = ''
})
</script>


<template>
  <div class="flex h-full w-full">
    <AcronymCircleAvatar :color="institutionData.color" :acronym="institutionData.acronym"
      :onColorSelected="onColorSelected" />
    <form @submit.prevent="handleSubmit" class="flex flex-col h-full justify-between w-[70%]">
      <div class="flex flex-col justify-start gap-7">
        <div class="flex items-center justify-between gap-8 w-full">
          <span class="font-semibold text-black/50 w-[20%]">Nombre de institución</span>
          <Input :errorMessage="$vCreateInstitution.name.$errors[0]?.$message.toString()"
            @input="onNameChange($event.target.value)" v-model="institutionData.name" class="w-[75%]" />
        </div>
        <div class="flex items-center justify-between gap-8 w-full">
          <span class="font-semibold text-black/50 w-[20%]">Región</span>
          <select v-model="institutionData.region" class="w-[100%] border px-4 py-2 rounded bg-white"
            :class="{ 'border-red-500': $vCreateInstitution.region.$error }">
            <option disabled value="">Selecciona una región</option>
            <option v-for="region in allRegions" :key="region.region" :value="region.region">
              {{ region.region }}
            </option>
          </select>
        </div>
        <div class="flex items-center justify-between gap-8 w-full">
          <span class="font-semibold text-black/50 w-[20%]">Ciudad</span>
          <select v-model="institutionData.city" class="w-[100%] border px-4 py-2 rounded bg-white"
            :disabled="filteredCities.length === 0" :class="{ 'border-red-500': $vCreateInstitution.city.$error }">
            <option disabled value="">Selecciona una ciudad</option>
            <option v-for="city in filteredCities" :key="city" :value="city">
              {{ city }}
            </option>
          </select>
        </div>
        <div class="flex items-center justify-between gap-8 w-full">
          <span class="font-semibold text-black/50 w-[20%]">Dirección</span>
          <Input :errorMessage="$vCreateInstitution.address.$errors[0]?.$message.toString()"
            v-model="institutionData.address" class="w-[75%]" />
        </div>

        <div class="flex items-center justify-between gap-8 w-full">
          <span class="font-semibold text-black/50 w-[20%]">Acrónimo</span>
          <Input :errorMessage="$vCreateInstitution.acronym.$errors[0]?.$message.toString()"
            v-model="institutionData.acronym" class="w-[75%]" />
        </div>
        <div class="flex items-center justify-between gap-8 w-full">
          <span class="font-semibold text-black/50 w-[20%]">Email</span>
          <Input :errorMessage="$vCreateInstitution.email.$errors[0]?.$message.toString()"
            v-model="institutionData.email" class="w-[75%]" />
        </div>
        <div v-if="!update" class="flex items-center justify-end gap-8 w-full">
          <span class="font-semibold text-black/50 w-[20%]">Contraseña</span>
          <div class="relative w-[100%]">
            <Input v-bind="$attrs" :errorMessage="$vCreateInstitution.password.$errors[0]?.$message.toString()"
              v-model="institutionData.password!" :type="showPassword ? 'text' : 'password'" class="w-full" />
            <!-- Ícono para alternar visibilidad de la contraseña -->
            <button type="button" @click="togglePasswordVisibility"
              class="text-sm leading-5 text-gray-500 absolute right-6 top-1/2 transform -translate-y-1/2">
              <span v-if="showPassword">Ocultar</span>
              <span v-else>Mostrar</span>
            </button>
          </div>
        </div>

        <div v-if="!update" class="flex items-center justify-end gap-8 w-full">
          <span class="font-semibold text-black/50 w-[20%]">Confirmar contraseña</span>
          <div class="relative w-[100%]">
            <Input v-bind="$attrs" :errorMessage="$vCreateInstitution.confirmPassword.$errors[0]?.$message.toString()"
              v-model="institutionData.confirmPassword!" :type="showPassword ? 'text' : 'password'" class="w-full" />
            <button type="button" @click="togglePasswordVisibility"
              class="text-sm leading-5 text-gray-500 absolute right-6 top-1/2 transform -translate-y-1/2">
              <span v-if="showPassword">Ocultar</span>
              <span v-else>Mostrar</span>
            </button>
          </div>
        </div>
      </div>

      <div v-if="!update" class="flex w-full items-center justify-around py-4">
        <div></div>
        <Button :disabled="$vCreateInstitution.$errors.length > 0" type="submit" variant="primary"
          class="w-40">Siguiente</Button>
      </div>
      <div v-else class="flex w-full items-center justify-around">
        <Button v-if="enable" @click="handleDisable" variant="danger" type="button">
          Deshabilitar</Button>
        <Button :loading="isLoading" :disabled="$vCreateInstitution.$errors.length > 0 || !hasChanges" type="submit"
          variant="primary">Actualizar</Button>
      </div>
    </form>
  </div>
  <ColorPicker :onColorChange="onColorSelected" :selectedColor="institutionData.color" :isOpen="isColorPickerOpen"
    :onClose="() => {
        isColorPickerOpen = false
      }
      " />
</template>