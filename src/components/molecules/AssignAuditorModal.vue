<script setup lang="ts">
import Modal from '../atoms/Modal.vue';
import Button from '../atoms/Button.vue';
import type { Institution } from '@/models/types';
import { ref, watch } from 'vue';
import { useInstitutions } from '@/hooks/useInstitutions';
import AuditorPicker from './AuditorPicker.vue';
import { Logger } from '@/services/logger';
const props = defineProps({
    institution: {
        type: Object as () => Institution,
        required: false
    },
    isOpen: {
        type: Boolean,
        required: true
    },
    onClose: {
        type: Function,
        required: true
    }
})

const { handleAssignAuditorToInstitution } = useInstitutions();
const institutionRef = ref<Institution>();
const selectedAuditorID = ref<number>(-1);

watch(() => props.institution, (institution) => {
    if (institution) {
        institutionRef.value = institution;
        Logger.info("Institution selected for auditor assignment", institution);
    }
});

const assignAuditorToInstitutionHandler = async () => {
    try {
        if (institutionRef.value && selectedAuditorID.value !== -1) {
            await handleAssignAuditorToInstitution(institutionRef.value.id, selectedAuditorID.value);
            Logger.info("Auditor assigned successfully");
            props.onClose();
        } else {
            Logger.warn("Attempted assignment without an auditor selected");
        }
    } catch (error) {
        Logger.error("Error assigning auditor", error);
    }
}
</script>

<template>
    <Modal :isOpen="isOpen" :onClose="onClose">
        <div class="flex  w-[40vw] flex-col gap-8">
            <h3 class="flex flex-col gap-3 text-xl font-bold">Asignar Auditor
                <p class="text-sm font-medium">
                    <span class="font-semibold">{{ institutionRef?.name }} </span>
                    necesita un auditor.
                </p>
            </h3>
            <div class="border-t border-black/20"></div>
            <AuditorPicker v-model="selectedAuditorID" />
            <Button @click="assignAuditorToInstitutionHandler" :disabled="selectedAuditorID === -1"
                class="w-48 self-center">Asignar</Button>

        </div>
    </Modal>
</template>