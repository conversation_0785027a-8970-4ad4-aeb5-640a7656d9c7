<script setup lang="ts">
import Input from '../atoms/Input.vue';
import Button from '../atoms/Button.vue';
import { formatPhoneNumber } from '@/helpers/formatters';
import { formatRUT } from '@/helpers/formatters/rutFormatters';
import { createInstitutionLegalRules } from '@/models/validation/formRules';
import type { InstitutionLegalInfoFormSchema } from '@/models/validation/formSchemas';
import { useForm } from '@/hooks/useForm';
import { toRef } from 'vue';

// formulario que corresponde al segundo paso del registro de una institución
// (página web, teléfono, rut, razón social)

const props = defineProps({
    update: {
        type: Boolean,
        default: false
    },
    savedInstitutionData: {
        type: Object as () => InstitutionLegalInfoFormSchema,
        required: false
    },
    onNext: {
        type: Function,
        required: true
    }
});

const emit = defineEmits(['prev']);

const { formData: institutionData, $vForm: $vCreateInstitution, hasChanges, handleSubmit, isLoading } = useForm(
    {
        initialDataRef: toRef(
            { ...props.savedInstitutionData, companyRut: formatRUT(props.savedInstitutionData?.companyRut ?? "") }
        ),
        validationRules: createInstitutionLegalRules,
        updateCondition: toRef(props.update),
        onSubmit: async (data) => {
            const formData = {
                ...data,
                companyRut: data.companyRut.replace(/\./g, '').replace(/-/g, '').toLowerCase(), 
                whatsapp: data.phoneNumber
            }

            await props.onNext(formData);
        }
    }
)

const handleBack = () => {
    emit('prev');
}


</script>

<template>
    <form @submit.prevent="handleSubmit" class="flex flex-col gap-8 w-full justify-between">
        <div class="flex flex-col w-full gap-10 px-10">
            <div class="flex items-center gap-8 w-full">
                <span class="font-semibold text-black/50 w-48">Página web</span>
                <Input :errorMessage="$vCreateInstitution.website.$errors[0]?.$message.toString()"
                    v-model="institutionData.website" class="flex-1" />
            </div>
            <div class="flex items-center gap-8 w-full">
                <span class="font-semibold text-black/50 w-48">Teléfono de contacto</span>
                <Input :formatter="formatPhoneNumber"
                    :errorMessage="$vCreateInstitution.phoneNumber.$errors[0]?.$message.toString()"
                    v-model="institutionData.phoneNumber" class="flex-1" />
            </div>
            <div class="flex items-center gap-8 w-full">
                <span class="font-semibold text-black/50 w-48">RUT de institución</span>
                <Input :disabled="update" :formatter="formatRUT"
                    :errorMessage="$vCreateInstitution.companyRut.$errors[0]?.$message.toString()"
                    v-model="institutionData.companyRut" class="flex-1" />
            </div>
            <div class="flex items-center gap-8 w-full">
                <span class="font-semibold text-black/50 w-48">Razón social</span>
                <Input :disabled="update"
                    :errorMessage="$vCreateInstitution.companyName.$errors[0]?.$message.toString()"
                    v-model="institutionData.companyName" class="flex-1" />
            </div>
        </div>
        <div v-if="!update" class="flex w-full justify-around items-center">
            <Button @click="handleBack" variant="invert" class="w-40">Volver</Button>
            <Button :disabled="$vCreateInstitution.$errors.length > 0" type="submit" variant="primary"
                class="w-40">Siguiente</Button>
        </div>
        <div v-else class="flex w-full justify-around items-center">
            <div class="w-[12.2rem]"></div>
            <Button :loading="isLoading" :disabled="$vCreateInstitution.$errors.length > 0 || !hasChanges" type="submit"
                variant="primary">
                Actualizar</Button>
        </div>
    </form>
</template>