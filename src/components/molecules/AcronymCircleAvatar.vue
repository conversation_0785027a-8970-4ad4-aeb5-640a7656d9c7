<script setup lang="ts">
import { ref } from 'vue';
import Button from '../atoms/Button.vue';
import ColorPicker from './ColorPicker.vue';

const props = defineProps({
    acronym: String,
    color: String,
    onColorSelected: {
        type: Function,
        required: true
    }
})

const isColorPickerOpen = ref<boolean>(false);
const selectedColor = ref<string>(props.color || "#5C3D99");

const onColorSelected = (color: string) => {
    selectedColor.value = color;
    props.onColorSelected(color);
    isColorPickerOpen.value = false;
}
</script>

<template>
    <div class="flex flex-col gap-6 px-10">

        <picture class="flex text-white font-bold text-4xl items-center justify-center h-44 w-44 rounded-full"
            :style="{ backgroundColor: selectedColor }">
            {{ acronym }}
        </picture>
        <Button @click="isColorPickerOpen = true" variant="invert" class="uppercase text-sm px-2 py-1">
            Cambiar color
        </Button>
    </div>

    <ColorPicker class="z-50" :isOpen="isColorPickerOpen" :onClose="() => { isColorPickerOpen = false }"
        :onColorChange="onColorSelected" />
</template>