<script setup lang="ts">
import Button from '../atoms/Button.vue';
import { ref } from 'vue';

const props = defineProps({
  onSearch: {
    type: Function,
    required: true
  },
  placeholder: {
    type: String,
    default: 'Buscar...'
  },

});

const searchTerm = ref<string>('');
const submitSearch = () => {
  props.onSearch(searchTerm.value);
};

</script>

<template>

  <div class="bg-white py-5 px-8 flex items-center justify-between rounded-3xl shadow-lg shadow-black/5">
    <div class="flex items-center h-full">
      <span class="text-gray-700 font-bold px-6">Búsqueda rápida</span>
      <div class="h-7 border border-gray-300 ml-4"></div>
    </div>
    <form @submit.prevent="submitSearch" class="flex flex-1 pl-10">

      <input type="search" id="default-search" v-model="searchTerm" :placeholder="placeholder"
        class="block w-full p-4 pr-12 text-sm text-gray-900 rounded-lg bg-transparent   focus:outline-none" />
      <Button type="submit">
        <span class="flex items-center gap-3">
          <i class="fa-solid fa-magnifying-glass"></i>
          Buscar
        </span>
      </Button>

    </form>
  </div>

</template>