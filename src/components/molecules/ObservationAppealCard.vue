<template>
  <div class="border-2 border-primary-400 2xl:ml-6 ml-1 rounded-lg p-4 2xl:w-64 2xl:h-72 w-40 h-52 flex flex-col">
    <div class="flex items-center mb-2">
      <i class="fa-solid fa-file-alt text-xl text-purple-500"></i>
      <p class="ml-3 2xl:body-2 body-5 font-semibold">{{ props.evidenceName }}.pdf</p>
    </div>
    <div class="mt-2">
      <p class="2xl:body-3 body-5 text-gray-600">{{ props.comment }}</p>
    </div>
    <div class="flex justify-end mt-auto">
      <button @click="openUploadModal"
        class="bg-primary-400 2xl:body-4 body-5 text-white py-2 px-4 rounded-full hover:bg-purple-600 transition">
        Actualizar
      </button>
    </div>

    <!-- Modal -->
    <Modal :isOpen="isModalOpen" :onClose="closeUploadModal">
      <div class="flex flex-col items-center p-6">
        <h2 class="text-lg font-semibold mb-4">Subir nueva evidencia</h2>
        
        <div v-if="previewSrc" class="w-full flex flex-col items-center">
          <embed class="w-full h-64 rounded-md border" :src="previewSrc" />
          <button @click="removeFile" class="mt-2 text-red-500">Eliminar archivo</button>
        </div>

        <input v-if="!previewSrc" type="file" accept=".pdf" @change="onFileChange" class="mt-4">

        <div class="flex justify-end mt-4">
          <button @click="uploadFile" :disabled="!selectedFile"
            class="bg-primary-400 text-white py-2 px-4 rounded-full hover:bg-purple-600 transition">
            Guardar
          </button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import Modal from '../atoms/Modal.vue';
import { useEvidences } from '@/hooks/useEvidences';
import { useProcesses } from '@/hooks/process/useProcesses';
import { useAuth } from '@/hooks/useAuth';
import { EvidenceStatus, type Dimension, type Evidence, type Process } from '@/models/types';
import { useDimensions } from '@/hooks/useDimensions';
import { useAsyncFetch } from '@/hooks/useAsyncFetch';

const { handleUploadEvidence } = useEvidences();
const { handleGetDimensionsByProcessID } = useDimensions();
const { handleGetActualProcessByInstitutionID } = useProcesses();
const { authData } = useAuth();

const props = defineProps<{ evidenceName: string; evidenceLocation: string; comment: string }>();

const actualProcess = ref<Process | null>(null);
const dimensions = ref<Dimension[]>([]);
const actualDimension = ref<Dimension | undefined>(undefined);
const isModalOpen = ref(false);
const selectedFile = ref<File | null>(null);
const previewSrc = ref<string | null>(null);

const handleFetchDimensions = async () => {
  actualProcess.value = await handleGetActualProcessByInstitutionID(authData.value!.simpleInstitutionDTO!.id)?? null;
  dimensions.value = await handleGetDimensionsByProcessID(actualProcess.value!.id);
  actualDimension.value = dimensions.value.length > 0 ? dimensions.value[0] : undefined;
};

onMounted(async () => {
  await handleFetch();
});
const { fetchData: handleFetch } = useAsyncFetch(handleFetchDimensions);

const openUploadModal = () => {
  selectedFile.value = null;
  previewSrc.value = null;
  isModalOpen.value = true;
};

const closeUploadModal = () => {
  isModalOpen.value = false;
};

const onFileChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  const file = target.files?.[0];

  if (file) {
    if (file.type !== "application/pdf") {
      alert("Solo se permiten archivos PDF.");
      return;
    }
    const MAX_FILE_SIZE = 50 * 1024 * 1024;

if (file.size > MAX_FILE_SIZE) {
    alert("El archivo es demasiado grande. Límite: 50MB.");
    return;
}
    selectedFile.value = file;
    previewSrc.value = URL.createObjectURL(file);
  }
};

const removeFile = () => {
  selectedFile.value = null;
  previewSrc.value = null;
};

const uploadFile = async () => {
  if (!selectedFile.value || !actualProcess.value || !actualDimension.value) {
    console.warn("No hay archivo seleccionado o faltan datos del proceso/dimensión.");
    return;
  }

  const evidence: Evidence = {
    id: Date.now(),
    title: selectedFile.value.name,
    approvalRequirements: "Pendiente",
    type: "PDF",
    file: selectedFile.value,
    uploaded: false, 
    evidenceStatus: EvidenceStatus.APPEAL, 
    comment: "Archivo subido para revisión",
  };

  try {
    await handleUploadEvidence(actualProcess.value.id, actualDimension.value, evidence, dimensions);
    closeUploadModal();
    alert("Evidencia subida correctamente.");
  } catch (error) {
    console.error("Error al subir la evidencia:", error);
    alert("Hubo un problema al subir el archivo.");
  }
};
</script>
