<script setup lang="ts">
import { useInstitutions } from '@/hooks/useInstitutions';
import type { Institution } from '@/models/types';
import { ref, watch } from 'vue';

const props = defineProps({
    level: {
        type: Number,
        required: true
    }
})

const institutions = ref<Institution[]>([]);
const { handleGetInstitutionsByLevel } = useInstitutions();

const fetchInstitutionsByLevel = async (level: number) => {
    institutions.value = await handleGetInstitutionsByLevel(level);
}

watch(() => props.level, async (value) => {
    await fetchInstitutionsByLevel(value);
});

await fetchInstitutionsByLevel(props.level);
</script>

<template>
    <div class="flex flex-col gap-2 max-h-80 overflow-y-auto">
        <div v-if="institutions.length > 0" class="flex flex-col gap-2" :key="level">
            <RouterLink :to="{ path: `/admin/institutions/${institution.id}` }" v-for="institution in institutions"
                :key="institution.id" class="institution-item">
                <div class="flex w-full px-4 py-2 items-center gap-4 hover:bg-purple-800 rounded-md
                            transition-all duration-300 hover:text-white text-black/80 cursor-pointer">
                    <div class="h-8 w-8 rounded-full text-xs flex justify-center items-center text-white"
                        :style="{ backgroundColor: institution.color }">
                        {{ institution.acronym }}
                    </div>
                    <p class="font-semibold"> {{ institution.name }}</p>
                </div>
            </RouterLink>
        </div>
        <p v-else class="text-sm font-medium text-black/60">
            No hay instituciones en este nivel.
        </p>
    </div>
</template>