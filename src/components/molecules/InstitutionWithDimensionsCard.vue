<script setup lang="ts">
import Card from '@/components/atoms/Card.vue'
import LevelProgressBar from '@/components/atoms/LevelProgressBar.vue'
import type { Institution } from '@/models/types';

defineProps({
    institution: {
        type: Object as () => Institution,
        required: true
    }
})
</script>

<template>
    <Card class="flex-1 flex flex-col items-center gap-6">
        <div class="flex w-full justify-center">
            <span class="flex items-center justify-center h-24 w-24 relative rounded-full"
                :style="{ backgroundColor: institution.color }">
                <h4 class="font-semibold text-white"> {{ institution.acronym }}</h4>
                <span class="absolute flex items-center justify-center w-16 h-6 rounded-full bg-orange-400 bottom-0">
                    <p class="text-center text-white text-xs font-semibold">{{ institution.level }} </p>
                </span>
            </span>
        </div>

        <h3 class="font-semibold text-black/80 2xl:text-nowrap xl:text-center"> {{ institution.name }}</h3>
        <div class="flex gap-3 items-center text-black/60 text-sm">
            <i class="fas fa-location-dot"></i>
            <p class=""> {{ institution.address }}</p>
        </div>

        <div class="flex flex-col gap-6 w-full">
            <LevelProgressBar v-for="dimension in institution.dimensions" :key="dimension.name" :label="dimension.name"
                :level="dimension.level ?? 0" />

        </div>

    </Card>
</template>