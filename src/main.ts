import { createApp } from 'vue'
import App from './App.vue'
import router from './router/routes'
import '@fortawesome/fontawesome-free/css/all.css'
import ToastPlugin from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-default.css';
import './style.css'
import { createPinia } from 'pinia'
import '@/assets/tailwind.css'
import '@fontsource/poppins';

const pinia = createPinia()

createApp(App).use(router).use(pinia).use(ToastPlugin, {
    position: 'top-right',
}).mount('#app')
