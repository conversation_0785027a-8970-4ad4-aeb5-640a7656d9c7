import { fetchAuditor<PERSON>y<PERSON> } from "@/services/api/auditorService";
import { fetchInstitutionByID } from "@/services/api/institutionService";
import { ref, watch } from "vue";
import { useRoute } from "vue-router";
import { useAuth } from "./useAuth";
import { fetchManagerByID } from "@/services/api/managerService";
import ManagerSubtitleOptions from "@/components/molecules/ManagerSubtitleOptions.vue";
import { fetchProcessByID } from "@/services/api/processService";
import { useAsyncFetch } from "./useAsyncFetch";


export const useHeader = () => {
    const route = useRoute();
    const headerTitle = ref<string>('');
    const headerSubtitle = ref<string>('');
    const subtitleComponent = ref<null | { component: any, props: any }>(null);
    const isSubtitleClickable = ref<boolean>(false);
    const { authData } = useAuth();

    const handleSetTitle = async () => {
        if (route.params.auditorID) {
            const auditorName = await fetchAuditorByID(parseInt(route.params.auditorID as string)).then(auditor => auditor.username);
            return "Auditores / " + auditorName;

        } else if (route.params.institutionID) {
            const institution = await fetchInstitutionByID(parseInt(route.params.institutionID as string));
            subtitleComponent.value = {
                component: ManagerSubtitleOptions,
                props: {
                    managerId: institution.manager?.id
                }
            };

            return "Instituciones / " + institution.name;
        } else if (authData.value?.role === 'MANAGER') {
            const institutionName = authData.value.simpleInstitutionDTO?.name;

            return institutionName + "  /  " + route.meta.name as string;
        } else if (route.params.processID) {
            const process = await fetchProcessByID(parseInt(route.params.processID as string));

            return "Procesos / " + process.simpleInstitutionDTO.name;
        }

        return route.meta.name as string;
    }

    const { fetchData, isLoading, data: title } = useAsyncFetch(handleSetTitle);

    watch(() => route.path, async (_newPath, _) => {
        subtitleComponent.value = null;
        await fetchData();
        headerTitle.value = title.value!;
    });

    return {
        headerTitle,
        headerSubtitle,
        isSubtitleClickable,
        subtitleComponent,
        isLoading
    }
}