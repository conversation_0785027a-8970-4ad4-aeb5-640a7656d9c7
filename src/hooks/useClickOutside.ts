import { onMounted, onUnmounted, type Ref } from "vue";

/**
 * hook para manejar el click fuera de un elemento HTML
 * 
 * @param refs referencias de los elementos HTML a observar 
 * 
 * @param callback función a ejecutar cuando se hace click fuera de los elementos
 */

export const useClickOutside = (refs: Ref<HTMLElement | null>[], callback: () => void) => {
    const handleClickOutside = (event: any) => {
        const isOutside = refs
            .filter((ref) => ref.value !== null)
            .every((ref) => ref.value && !ref.value.contains(event.target));
        if (isOutside) {
            callback();
        }
    };

    onMounted(() => {
        document.addEventListener('mousedown', handleClickOutside);
    });

    onUnmounted(() => {
        document.removeEventListener('mousedown', handleClickOutside);
    });
}