import { fetchPDFRecommendationsByProcessId, fetchRecommendationsByProcessId } from "@/services/api/recommendationsService";
import jsPDF from "jspdf";
import { Logger } from "@/services/logger";

export const useRecommendations = () => {

    const handleGetRecommendationsByProcessID = async (processID: number) => {
        try {
            const recommendations = await fetchRecommendationsByProcessId(processID);
            return recommendations;
        } catch (error) {
            Logger.error('Error getting recommendations by process ID:', error);
            throw error;
        }
    }

    const handleGeneratePDFFile = async (processID: number, institutionName?: string) => {
        try {
            const blob = await fetchPDFRecommendationsByProcessId(processID, institutionName);

            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `Recomendaciones_${institutionName ?? 'institucion'}.pdf`;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url); // Limpieza
        } catch (error) {
            Logger.error('Error generating PDF file for recommendations:', error);
            throw error;
        }
    };

    const handleGenerateRecommendationsPDF = async (processID: number, institutionName: string) => {
        try {
            const recommendations = await fetchRecommendationsByProcessId(processID);
            const doc = new jsPDF();

            doc.text(institutionName, 14, 15);
            doc.setFontSize(11);

            let y = 30;

            recommendations.forEach((rec, index) => {
                doc.setFontSize(12);
                doc.text(`Recomendación ${index + 1}`, 10, y);
                y += 6;
                doc.setFontSize(10);
                doc.text(`Dimensión: ${rec.dimension}`, 10, y);
                y += 6;
                doc.text(`Nivel Actual: ${rec.currentLevel}`, 10, y);
                y += 6;
                doc.text(`Nivel Objetivo: ${rec.targetLevel}`, 10, y);
                y += 6;

                const recommendationText = doc.splitTextToSize(rec.description, 180);
                doc.text(recommendationText, 10, y);
                y += 6 * recommendationText.length;

                if (y > 270) {
                    doc.addPage();
                    y = 10;
                }
            });

            doc.save(`Recomendaciones_${institutionName}.pdf`);
            return recommendations;
        } catch (error) {
            Logger.error('Error generating PDF for recommendations:', error);
            throw error;
        }
    }


    return {
        handleGetRecommendationsByProcessID,
        handleGenerateRecommendationsPDF,
        handleGeneratePDFFile
    }


}