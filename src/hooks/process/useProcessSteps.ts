import { ref, type Ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ProcessStatus, type Process, type Recommendation, type Step } from '@/models/types'
import { fetchRecommendationsByProcessId } from '@/services/api/recommendationsService'

interface ProcessStatusLabel {
  title: string
  description: string
  icon: string
  color: string
}

const processSteps: Step[] = [
  {
    number: 1,
    status: ProcessStatus.IN_PROGRESS,
    icon: 'fa-solid fa-rotate',
    title: 'Enviar las encuestas a sus funcionarios',
    expandedTitle: 'Enviar las encuestas a sus funcionarios',
    description:
      'Actualmente, tiene un proceso en curso. Los resultados que se muestran a continuación correponden al último proceso finalizado. Una vez que el proceso actual se complete, sus resultados se actualizarán en esta vista automáticamnte. Puede monitorear el avance del proceso de evaluación con el botón monitorear',
    buttonText: 'Enviar Encuestas',
    buttonEnabled: false,
    color: 'text-neutral-800'
  },
  {
    number: 2,
    status: ProcessStatus.IN_PROGRESS,
    icon: 'fa-solid fa-upload',
    title: 'Subir evidencias para cada dimensión',
    expandedTitle: 'Subir evidencias para cada dimensión',
    description:
      'Suba todas las evidencias requeridas para continuar el proceso. Este paso se habilita cuando faltan evidencias.',
    buttonText: 'Subir evidencias',
    buttonEnabled: true,
    color: 'text-customRed-200'
  },
  {
    number: 3,
    status: ProcessStatus.SURVEY_FINISHED,
    icon: 'fa-solid fa-clock',
    title: 'En revisión por el auditor',
    expandedTitle: 'El proceso está en revisión',
    description:
      'El proceso está siendo revisado por un auditor. Durante esta etapa, el auditor evaluará las evidencias y encuestas enviadas. Este proceso puede tomar un tiempo considerable, dependiendo de la cantidad de evidencias y encuestas.',
    buttonText: 'En revisión',
    buttonEnabled: false,
    color: 'text-customYellow-100'
  },
  {
    number: 4,
    status: ProcessStatus.APPEALABLE,
    icon: 'fa-solid fa-triangle-exclamation',
    title: 'Ver resultados',
    expandedTitle: 'Revisión de objeciones',
    description:
      'Algunas preguntas de su proceso han sido objetadas por el auditor a cargo debido a la falta de evidencia suficiente o la ausencia de la misma. Estas objeciones impactan directamente en los niveles que su institución puede alcanzar. Para evitar limitaciones en sus resultados, le recomendamos apelar las objeciones presentando evidencias concluyentes.',
    buttonText: 'Subir evidencias',
    buttonEnabled: true,
    color: 'text-customRed-200'
  },
  {
    number: 5,
    status: ProcessStatus.FINISHED,
    icon: 'fa-solid fa-check',
    title: 'Ver resultados',
    expandedTitle: 'Este proceso ya terminó y los resultados están disponibles',
    description: 'Actualmente ve los datos obtenidos y evaluados por el auditor asignado.',
    buttonText: 'Ver resultados',
    buttonEnabled: false,
    color: 'text-green-600'
  }
]

export const useProcessSteps = (process: Ref<Process | undefined>, mergeFinalStates = false) => {
  const router = useRouter()
  const currentStep = ref(1)
  const buttonEnabled = ref(true)
  const buttonLabel = ref('')
  const processStatus = ref<ProcessStatusLabel>({
    title: '',
    description: '',
    icon: '',
    color: ''
  })

  const recommendations = ref<Recommendation[]>([])
  const fetchRecommendations = async () => {
    if (process.value) {
      recommendations.value = await fetchRecommendationsByProcessId(process.value.id)
    }
  }
  const filteredSteps = computed(() => {
    if (!mergeFinalStates) {
      return processSteps.map((step) => {
        if (
          (step.status === ProcessStatus.APPEALABLE || step.status === ProcessStatus.FINISHED) &&
          recommendations.value.length === 0
        ) {
          return {
            ...step,
            title: 'esperando resultados...'
          }
        }
        return step
      })
    }

    const steps: Step[] = []

    for (const step of processSteps) {
      if (step.status === ProcessStatus.APPEALABLE) continue

      if (step.status === ProcessStatus.FINISHED) {
        steps.push({
          ...step,
          status: ProcessStatus.FINISHED
        })
        continue
      }

      steps.push(step)
    }

    return steps.map((step, index) => {
      let modifiedStep = step
      if (
        (step.status === ProcessStatus.APPEALABLE || step.status === ProcessStatus.FINISHED) &&
        recommendations.value.length === 0
      ) {
        modifiedStep = {
          ...step,
          title: 'Esperando resultados...'
        }
      }
      return {
        ...modifiedStep,
        number: index + 1
      }
    })
  })

  const mapStatus = (status: ProcessStatus): ProcessStatus => {
    if (mergeFinalStates && status === ProcessStatus.APPEALABLE) {
      return ProcessStatus.FINISHED
    }
    return status
  }

  const updateCurrentStep = () => {
    const status = process.value?.status
    if (!status) return

    const mappedStatus = mapStatus(status as ProcessStatus)
    const step = filteredSteps.value.find((s) => s.status === mappedStatus)
    currentStep.value = step?.number ?? 1
  }

  const updateButtonLabel = () => {
    const step = filteredSteps.value.find((s) => s.number === currentStep.value)
    buttonLabel.value = step?.buttonText ?? ''
    buttonEnabled.value = !!step?.buttonText
  }

  const updateButtonEnabled = () => {
    const step = filteredSteps.value.find((s) => s.number === currentStep.value)
    buttonEnabled.value = step?.buttonEnabled ?? true
  }
  const updateProcessStatus = () => {
    const step = filteredSteps.value.find((s) => s.number === currentStep.value)
    if (step) {
      processStatus.value = {
        title: step.expandedTitle ?? step.title,
        description: step.description ?? '',
        icon: step.icon,
        color: step.color
      }
    }
  }

  watch(
    process,
    () => {
      updateCurrentStep()
      updateButtonLabel()
      updateProcessStatus()
      updateButtonEnabled()
    },
    { immediate: true }
  )

  return {
    currentStep,
    processStatus,
    buttonLabel,
    buttonEnabled,
    fetchRecommendations,
    handleButtonAction: () => {
      if (process.value) {
        router.push(`/gestor/process/${process.value.id}`)
      }
    },
    processSteps: filteredSteps
  }
}
