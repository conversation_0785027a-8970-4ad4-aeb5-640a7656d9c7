import { ref, watch } from 'vue'

export function useRegions() {
  const regions = ref<{ codigo: string; nombre: string }[]>([])
  const cities = ref<{ codigo: string; nombre: string }[]>([])
  const selectedRegionCode = ref<string>('')

  const fetchRegions = async () => {
    try {
      const response = await fetch('https://apis.digital.gob.cl/dpa/regiones')
      const data = await response.json()
      regions.value = data
    } catch (error) {
      console.error('Error al obtener regiones:', error)
    }
  }

  const fetchCitiesByRegion = async (regionCode: string) => {
    try {
      const response = await fetch(
        `https://apis.digital.gob.cl/dpa/regiones/${regionCode}/comunas`
      )
      const data = await response.json()
      cities.value = data
    } catch (error) {
      console.error('Error al obtener comunas:', error)
    }
  }

  watch(selectedRegionCode, (newCode) => {
    if (newCode) {
      fetchCitiesByRegion(newCode)
    } else {
      cities.value = []
    }
  })

  fetchRegions()

  return {
    regions,
    cities,
    selectedRegionCode
  }
}
