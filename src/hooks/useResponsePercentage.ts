import type { Ref } from 'vue';
import type { DashboardInstitution } from '@/models/types';

interface DayInfo {
  day: number;
  month: string;
  responses: number;
  dayName: string;
}

export function useResponsePercentage(
  dashboardInstitutionData: Ref<DashboardInstitution | null>,
  originalData: Ref<number[]>,
  projectionData: Ref<number[]>,
  lastTenDays: DayInfo[]
) {
const responsePorcentage = () => {
  if (dashboardInstitutionData.value && dashboardInstitutionData.value.responseAverages) {
    const sortedAverages = [...dashboardInstitutionData.value.responseAverages].sort(
      (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    originalData.value = sortedAverages.map(average => average.completionPercentage);

    const today = new Date();
    const dailyResponseMap = new Map();

    sortedAverages.forEach(average => {
      const responseDate = new Date(average.date);
      const responseDay = responseDate.toISOString().split('T')[0];

      const differenceInDays = (today.getTime() - responseDate.getTime()) / (1000 * 60 * 60 * 24);

      if (differenceInDays <= 10) {
        if (dailyResponseMap.has(responseDay)) {
          dailyResponseMap.set(responseDay, dailyResponseMap.get(responseDay) + average.peopleAnswered);
        } else {
          dailyResponseMap.set(responseDay, average.peopleAnswered);
        }
      }
    });

    lastTenDays.forEach(dayInfo => {
      const responseCount = dailyResponseMap.get(new Date(today.getFullYear(), today.getMonth(), dayInfo.day).toISOString().split('T')[0]) || 0;
      dayInfo.responses = responseCount;
    });

    const last7DaysEntries = Array.from(dailyResponseMap.entries()).slice(-7);
    projectionData.value = last7DaysEntries.map(([_, totalResponses]) => totalResponses);

    const estimationPoints = dashboardInstitutionData.value.estimationPoints;
    const clonedOriginalData = [...originalData.value];

    Object.keys(estimationPoints).forEach(percentage => {
      const integerPercentage = Math.round(parseFloat(percentage));
      clonedOriginalData.push(integerPercentage);
    });

    projectionData.value = clonedOriginalData;
  }
};

  return { responsePorcentage };
}
