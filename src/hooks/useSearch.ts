import { ref, watch } from 'vue';

export function useSearch(searchFunction: (searchTerm: string) => void, delay = 300) {
    const searchTerm = ref('');
    const timeout = ref<number | null>(null);

    const triggerSearch = () => {
        if (timeout.value) {
            clearTimeout(timeout.value);
        }
        timeout.value = setTimeout(() => {
            searchFunction(searchTerm.value);
        }, delay);
    };

    watch(searchTerm, triggerSearch);

    return {
        searchTerm
    };
}