import type { Process } from "@/models/types";
import type { InstitutionRequest } from "@/models/types/institution";
import { fetchAllInstitutionsRegisterRequests, markInstitutionRequestAsRead } from "@/services/api/institutionService";
import { fetchAllProcessesRequests } from "@/services/api/processService";
import { ref } from "vue"
import { Logger } from "@/services/logger";

/**
 * hook para manejar las solicitudes de instituciones y procesos
 * 
 * @returns un objeto con funciones y datos para manejar las solicitudes
 * 
 * - institutionsRequests: solicitudes de registro de instituciones
 * - processesRequests: solicitudes de procesos (desde el gestor)
 * - handleFetchInstitutionRequests: fetcher para obtener las solicitudes de registro de instituciones
 * - handleFetchProcessesRequests: fetcher para obtener las solicitudes de procesos
 */

export const useRequests = () => {
    const institutionsRequests = ref<InstitutionRequest[]>([]);
    const processesRequests = ref<Process[]>([]);
    const isLoading = ref<boolean>(false);

    const handleFetchInstitutionRequests = async () => {
        try {
            isLoading.value = true;
            const response = await fetchAllInstitutionsRegisterRequests();
            institutionsRequests.value = response.filter(request => request.requestStatus !== 'READ');
        } catch (error) {
            Logger.error('Error getting requests from institution:', error);
        } finally {
            isLoading.value = false
        }
    }

    const handleFetchProcessesRequests = async () => {
        try {
            isLoading.value = true;
            const response = await fetchAllProcessesRequests();
            processesRequests.value = response.filter((process) => process.requestStatus === 'UNREAD')
        } catch (error) {
            Logger.error('Error getting process requests:', error);
        } finally {
            isLoading.value = false
        }
    }

    const handleMarkInstitutionRequestAsRead = async (requestId: number) => {
        try {
            await markInstitutionRequestAsRead(requestId);
        } catch (error) {
            Logger.error('Error marking institution request as read:', error);
            throw error;
        }
    }

    return {
        institutionsRequests,
        processesRequests,
        isLoading,
        handleFetchInstitutionRequests,
        handleFetchProcessesRequests,
        markInstitutionRequestAsRead: handleMarkInstitutionRequestAsRead
    }
}