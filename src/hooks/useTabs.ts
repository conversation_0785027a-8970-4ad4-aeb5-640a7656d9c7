import { computed, ref, type Ref } from "vue";

/**
 * hook para manejar tabs
 * 
 * @param tabsRef referencia a los tabs del tipo Tab[]
 * 
 * @returns objeto con funciones y datos para manejar los tabs
 * 
 * - activeTab: tab activo
 * - activeTabComponent: componente del tab activo
 * - setActiveTab: función para establecer el tab activo
 * - nextTab: función para avanzar al siguiente tab
 * - prevTab: función para retroceder al tab anterior
 */

export interface Tab {
    name: string;
    title?: string;
    description?: string;
    component: Object;
}

export const useTabs = (tabsRef: Ref<Tab[]>) => {
    const activeTab = ref(tabsRef.value[0]);

    const nextTab = () => {
        const index = tabsRef.value.findIndex(tab => tab.name === activeTab.value.name);
        if (index < tabsRef.value.length - 1) {
            activeTab.value = tabsRef.value[index + 1];
        }
    }

    const prevTab = () => {
        const index = tabsRef.value.findIndex(tab => tab.name === activeTab.value.name);
        if (index > 0) {
            activeTab.value = tabsRef.value[index - 1];
        }
    }

    const setActiveTab = (name: string) => {
        activeTab.value = tabsRef.value.find(tab => tab.name === name) ?? tabsRef.value[0];
    };

    const activeTabComponent = computed(() => activeTab.value.component);

    return {
        activeTab,
        activeTabComponent,
        setActiveTab,
        nextTab,
        prevTab
    };
};