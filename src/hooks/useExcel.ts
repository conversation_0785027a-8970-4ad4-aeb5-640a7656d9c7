import { ref } from 'vue';
import * as XLSX from 'xlsx';

/**
 * hook para procesar archivos excel y csv
 * @returns un objeto con funciones y datos para procesar archivos excel y csv
 * 
 * - parseExcelFile: función para parsear un archivo excel o csv
 * - error: error al procesar el archivo excel o csv
 */

export const useExcel = () => {
    const error = ref<string | null>(null);

    const parseExcelFile = async (file: File, expectedColumns?: string[]): Promise<any[]> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target?.result as ArrayBuffer);
                    let workbook;
                    
                    if (file.name.endsWith('.csv')) {
                        const csvData = e.target?.result as string;
                        workbook = XLSX.read(csvData, { type: 'string', codepage: 65001 });
                    } else {
                        workbook = XLSX.read(data, { type: 'array' });
                    }

                    const sheet = workbook.Sheets[workbook.SheetNames[0]];
                    const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 }) as any[][];

                    const actualColumns = jsonData[0] || [];
                    const missingColumns = expectedColumns?.filter(col => !actualColumns.includes(col));

                    if (missingColumns?.length && missingColumns.length > 0) {
                        error.value = `Falta(n) la(s) columna(s) esperada(s): ${missingColumns.join(', ')}`;
                        reject(new Error(error.value));
                    } else {
                        const formattedData = XLSX.utils.sheet_to_json(sheet);
                        resolve(formattedData);
                    }
                } catch (err: any) {
                    error.value = 'Error al procesar el archivo: ' + 'Archivo no válido';
                    reject(new Error(error.value));
                }
            };

            reader.onerror = (error: any) => {
                error.value = 'Error al leer el archivo: ' + error.message;
                reject(new Error(error.value));
            };

            reader.readAsArrayBuffer(file);
        });
    };

    return {
        parseExcelFile,
        error
    };
}
