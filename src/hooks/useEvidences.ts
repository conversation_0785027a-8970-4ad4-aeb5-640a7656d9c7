import { EvidenceStatus, type Dimension, type Evidence } from "@/models/types";
import { appealEvidence, approveEvidence, deleteEvidence, downloadEvidence, fetchEvidencesWithDimensionsByProcessID, rejectEvidence, uploadEvidence } from "@/services/api/evidenceService";
import { ref, type Ref } from "vue";
import { Logger } from "@/services/logger";

export const useEvidences = () => {
    const evidences = ref<Evidence[]>([]);
    const isLoading = ref(false);

    const handleGetEvidenceByProcessID = async (processId: number): Promise<Evidence | undefined> => {
        try {
            const evidence = await fetchEvidencesWithDimensionsByProcessID(processId);
            Logger.debug("Evidences fetched successfully", evidence);
            return evidence;
        } catch (error: any) {
            if (error?.message?.includes('No active process found')) {
                return undefined;
            }else {
            Logger.error("Error fetching evidences by process ID", { processId, error: error.message });
            throw new Error("Error fetching evidences by process ID: " + error.message);
        }
            
        }
    };

    const handleAppealEvidence = async (evidence: Evidence, dimensionsRef: Ref<Dimension[]>) => {
        try {
            if (!evidence.file || !evidence.title) {
                Logger.warn("Invalid evidence data", evidence);
                throw new Error("Invalid evidence data");
            }
    
            const renamedEvidence = new File([evidence.file], evidence.title, { type: evidence.file.type });
            const formData = new FormData();
            formData.append('file', renamedEvidence);
    
            await appealEvidence(evidence.id!, formData);
    
            dimensionsRef.value.forEach(dimension => {
                const evidences = Array.isArray(dimension.evidences) ? dimension.evidences : [];
    
                evidences.forEach(e => {
                    if (e.id === evidence.id) {
                        e.evidenceStatus = EvidenceStatus.APPEALED;
                    }
                });
            });
    
            Logger.debug("Evidence appealed successfully", { evidenceId: evidence.id });
        } catch (error) {
            Logger.error("Error appealing evidence", { evidenceId: evidence.id, error });
            throw error;
        }
    };

    const handleUploadEvidence = async (processId: number, dimension: Dimension, evidence: Evidence, dimensionsRef: Ref<Dimension[]>) => {
        try {
            if (!evidence.file || !evidence.title) {
                Logger.warn("Invalid evidence data", evidence);
                throw new Error("Invalid evidence data");
            }
            const renamedEvidence = new File([evidence.file], evidence.title, { type: evidence.file.type });

            const formData = new FormData();
            formData.append('files', renamedEvidence);

            await uploadEvidence(processId, dimension.id!, formData);

            const targetDimension = dimensionsRef.value.find(d => d.name === dimension.name);
            if (targetDimension) {
                targetDimension.evidences?.forEach(e => {
                    if (e.title === evidence.title) {
                        e.uploaded = true;
                    }
                });
            }

            Logger.debug("Evidence uploaded successfully", { evidenceTitle: evidence.title });
        } catch (error) {
            Logger.error("Error uploading evidence", { processId, evidenceTitle: evidence.title, error });
            throw error;
        }
    };

    const handleDeleteEvidence = async (evidence: Evidence, dimension: Dimension, dimensionsRef: Ref<Dimension[]>) => {
        try {
            Logger.info("Deleting evidence", { evidenceId: evidence.id, dimensionId: dimension.id });
            await deleteEvidence(evidence.id!);

            const targetDimension = dimensionsRef.value.find(d => d.name === dimension.name);
            if (targetDimension) {
                targetDimension.evidences?.forEach(e => {
                    if (e.title === evidence.title) {
                        e.uploaded = false;
                        e.file = undefined;
                    }
                });
            }
            Logger.debug("Evidence deleted successfully", { evidenceId: evidence.id });
        } catch (error) {
            Logger.error("Error deleting evidence", { evidenceId: evidence.id, error });
            throw error;
        }
    };

    const handlePreviewEvidence = async (evidenceId: number) => {
        try {
            isLoading.value = true;
            const response = await downloadEvidence(evidenceId);
            const url = window.URL.createObjectURL(response);
            Logger.debug("Evidence downloaded successfully", { evidenceId });
            return url;
        } catch (error) {
            Logger.error("Error downloading evidence", { evidenceId, error });
            throw error;
        } finally {
            isLoading.value = false;
        }
    };

    const handleApproveOrRejectEvidence = async (evidenceId: number, status: EvidenceStatus, comment?: string) => {
        try {
            if (status === EvidenceStatus.ACCEPTED || status === EvidenceStatus.ACCEPTED_WITH_COMMENT) {
                await approveEvidence(evidenceId, comment ? comment : '');
                Logger.debug("Evidence approved successfully", { evidenceId });
            } else if (status === EvidenceStatus.REJECTED) {
                await rejectEvidence(evidenceId, comment);
                Logger.debug("Evidence rejected successfully", { evidenceId });
            }
        } catch (error) {
            Logger.error("Error approving/rejecting evidence", { evidenceId, status, error });
            throw error;
        }
    };

    return {
        evidences,
        isLoading,
        handleUploadEvidence,
        handleDeleteEvidence,
        handlePreviewEvidence,
        handleApproveOrRejectEvidence,
        handleAppealEvidence,
        handleGetEvidenceByProcessID
    };
};