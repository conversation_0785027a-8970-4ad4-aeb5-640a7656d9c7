import type { MaturityModel } from "@/models/types/maturityModel";
import type { MaturityModelFormSchema } from "@/models/validation/formSchemas";
import { createMaturityModel, fetchAllMaturityModels } from "@/services/api/maturityModelService";
import { usePagination } from "./usePagination";
import { Logger } from "@/services/logger";
/**
 * hook para manejar los modelos de madurez
 * 
 * @returns un objeto con funciones y datos para manejar los modelos de madurez
 * 
 * - `maturityModels`: modelos de madurez
 * - `handleFetchMaturityModels`: fetcher para obtener los modelos de madurez
 * - `handleCreateMaturityModel`: función para crear un modelo de madurez, junto a sus dimensiones
 */

export const useMaturityModels = () => {
    const fetchMaturityModels = (page: number) => fetchAllMaturityModels(page);
    const { data: maturityModels, page, totalPages, fetchPageData, handleNextPage, handlePreviousPage, isLoading } = usePagination<MaturityModel>(fetchMaturityModels);

    const handleCreateMaturityModel = async (maturityModel: MaturityModelFormSchema, file: File): Promise<MaturityModel> => {
        try {
            const createdMaturityModel = await createMaturityModel(maturityModel, file);
            return createdMaturityModel;
        } catch (error) {
            Logger.error('Error creating maturity model:', error);
            throw error;
        }
    }

    return {
        maturityModels,
        page,
        isLoading,
        totalPages,
        fetchPageData,
        handleNextPage,
        handlePreviousPage,
        handleCreateMaturityModel
    }
}