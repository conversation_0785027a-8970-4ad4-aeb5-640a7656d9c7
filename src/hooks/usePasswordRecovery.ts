// hooks/usePasswordRecovery.ts
import { ref } from 'vue';
import { recoveryService } from "@/services/api/recoveryService";

export function usePasswordRecovery() {
  const loading = ref(false);
  const error = ref<string | null>(null);

  const sendCode = async (email: string) => {
    try {
      loading.value = true;
      error.value = null;
      return await recoveryService.sendRecoveryCode(email);
    } catch (err: any) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const validateCode = async (email: string, recoveryCode: string) => {
    try {
      loading.value = true;
      error.value = null;
      return await recoveryService.validateRecoveryCode(email, recoveryCode);
    } catch (err: any) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const changePassword = async (email: string, password: string, validationPassword: string) => {
    try {
      loading.value = true;
      error.value = null;
      return await recoveryService.updatePassword(email, password, validationPassword);
    } catch (err: any) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    sendCode,
    validateCode,
    changePassword,
    loading,
    error,
  };
}