import type { GestorFormSchema } from "@/models/validation/formSchemas"
import { assignManagerToInstitution } from "@/services/api/institutionService";
import { createManager, toggleManagerStatus, updateManager } from "@/services/api/managerService";
import { formatRutToInteger } from "@/helpers/formatters/rutFormatters";
import { Logger } from "@/services/logger";
export const useManagers = () => {

    const handleUpdateManager = async (managerId: number, managerFormData: GestorFormSchema) => {
        try {
            const updatedManager = await updateManager(managerId, managerFormData);
            return updatedManager;
        } catch (error) {
            Logger.error('Error updating manager:', error)
            throw error;
        }
    }
    const handleDisableManager = async (institutionId: number, managerId: number, newManagerFormData: GestorFormSchema) => {
        try {
            const managerData = {
                ...newManagerFormData,
                rut: formatRutToInteger(newManagerFormData.rut).toString()
            }
            const newManager = await createManager(managerData);
            await assignManagerToInstitution(institutionId, newManager.id);

            // deshabilitar gestor actual
            await toggleManagerStatus(managerId);

        } catch (error) {
            Logger.error('Error disabling manager:', error)
            throw error;
        }
    }

    return {
        handleUpdateManager,
        handleDisableManager
    }
}