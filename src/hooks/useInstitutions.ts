import { assignAuditorToInstitution, assignManagerToInstitution, createInstitution, toggleInstitutionStatus, fetchAllInstitutions, updateInstitution, fetchInstitutionByManagerID, fetchInstitutionLevelDistribution, fetchInstitutionsByLevel, fetchTop3Institutions, fetchDashboard, fetchTrending } from "@/services/api/institutionService";
import { usePagination } from "./usePagination";
import { createManager } from "@/services/api/managerService";
import type { GestorFormSchema, InstitutionFormSchema } from "@/models/validation/formSchemas";
import { mapInstitutionFormSchemaToInstitution } from "@/mappers";
import type { Institution, LevelPercentage } from "@/models/types/index";
import { Logger } from "@/services/logger"; 

/**
 * hook para manejar la lógica relacionada a las instituciones
 * 
 * @returns un objeto con funciones y datos para manejar las instituciones
 * 
 * - institutions: instituciones
 * - page: página actual
 * - totalPages: total de páginas
 * - fetchPageData: función para obtener los datos de la página actual
 * - handleSearchInstitutions: función para buscar instituciones
 * - handleNextPage: función para avanzar a la siguiente página
 * - handlePreviousPage: función para retroceder a la página anterior
 * - handleCreateInstitution: función para crear una institución
 * - handleUpdateInstitution: función para actualizar una institución
 * - handleAssignAuditorToInstitution: función para asignar un auditor a una institución
 * - handleToggleInstitutionStatus: función para deshabilitar una institución
 * - handleGetInstitutionByManagerID: función para obtener una institución por el id de su gestor
 */

export const useInstitutions = () => {
    const fetchInstitutions = (page: number) => fetchAllInstitutions(page);

    const { data: institutions, page, fetchPageData, totalPages, handleNextPage, handlePreviousPage, isLoading, handleGoToPage } = usePagination(fetchInstitutions);

    const handleSearchInstitutions = async (search: string) => {
        try {
            isLoading.value = true;
            institutions.value = (await fetchAllInstitutions(page.value, search)).data;
        } catch (error: any) {
            Logger.error("Error searching institutions: " + error.message, { search });
        } finally {
            isLoading.value = false;
        }
    }

    const handleCreateInstitution = async (institutionData: InstitutionFormSchema) => {
        try {
            const institutionResponse = await createInstitution(institutionData);

            const gestorData = {
                ...institutionData.gestorInfo,
                password: institutionData.baseInfo?.password,
            } as GestorFormSchema;

            const gestorResponse = await createManager(gestorData);
            await assignManagerToInstitution(institutionResponse.id, gestorResponse.id);
            return institutionResponse;
        } catch (error: any) {
            Logger.error("Error creating institution: " + error.message, { institutionData });
            throw new Error("Error creating institution: " + error.message);
        }
    }

    const handleUpdateInstitution = async (institutionId: number, managerId: number, institutionData: InstitutionFormSchema) => {
        try {
            const institutionToUpdate = mapInstitutionFormSchemaToInstitution(institutionId, managerId, institutionData);
            const updatedInstitution = await updateInstitution(institutionId, institutionToUpdate);
            
            Logger.info(`Institution with ID: ${institutionId} updated successfully.`);
            return updatedInstitution;
        } catch (error: any) {
            Logger.error("Error updating institution: " + error.message, { institutionId, institutionData });
            throw new Error("Error updating institution: " + error.message);
        }
    }

    const handleGetInstitutionByManagerID = async (managerId: number): Promise<Institution | undefined> => {
        try {
            return await fetchInstitutionByManagerID(managerId);
        } catch (error: any) {
            Logger.error("Error fetching institution by manager ID: " + error.message, { managerId });
            throw new Error("Error fetching institution by manager ID: " + error.message);
        }
    }

    const handleAssignAuditorToInstitution = async (institutionId: number, auditorId: number) => {
        try {
            await assignAuditorToInstitution(institutionId, auditorId);
            
            window.location.reload();
        } catch (error: any) {
            Logger.error("Error assigning auditor to institution: " + error.message, { institutionId, auditorId });
        }
    }

    const handleToggleInstitutionStatus = async (institutionId: number) => {
        try {
            await toggleInstitutionStatus(institutionId);
        } catch (error: any) {
            Logger.error("Error changing institution status: " + error.message, { institutionId });
        }
    }

    const handleGetLevelDistribution = async () => {
        try {
            const levelDistribution = await fetchInstitutionLevelDistribution();

            if (!Array.isArray(levelDistribution)) {
                throw new Error("Invalid level distribution data.");
            }    

            const completeLevels: LevelPercentage[] = Array.from({ length: 5 }, (_, i) => ({
                level: i + 1,
                percentage: 0,
                ninstitutions: 0,
            }));

            levelDistribution.forEach((level: any) => {
                const index = completeLevels.findIndex(l => l.level === level.level);
                if (index !== -1) {
                    completeLevels[index] = level;
                }
            });

            Logger.info("Level distribution fetched successfully.");
            return completeLevels;
        } catch (error: any) {
            Logger.error("Error fetching level distribution: " + error.message);
            return Array.from({ length: 5 }, (_, i) => ({
                level: i + 1,
                percentage: 0,
                ninstitutions: 0,
            }));
        }
    }

    const handleGetInstitutionsByLevel = async (level: number) => {
        try {
            return await fetchInstitutionsByLevel(level);
        } catch (error: any) {
            Logger.error("Error fetching institutions by level: " + error.message, { level });
            return [];
        }
    }

    const handleGetTop3Institutions = async () => {
        try {
            return await fetchTop3Institutions();
        } catch (error: any) {
            Logger.error("Error fetching top 3 institutions: " + error.message);
        }
    }

    const dashboardGetInstitution = async (institutionId: number) => {
        try {
            return await fetchDashboard(institutionId);
        } catch (error: any) {
            Logger.error("Error fetching institution dashboard: " + error.message, { institutionId });
        }
    }

    const trendingGetChart = async (institutionId: number) => {
        try {
            return await fetchTrending(institutionId);
        } catch (error: any) {
            Logger.error("Error fetching trending chart: " + error.message, { institutionId });
        }
    }

    return {
        institutions,
        page,
        totalPages,
        isLoading,
        fetchPageData,
        handleSearchInstitutions,
        handleNextPage,
        handlePreviousPage,
        handleGoToPage,
        handleCreateInstitution,
        handleUpdateInstitution,
        handleAssignAuditorToInstitution,
        handleToggleInstitutionStatus,
        handleGetInstitutionByManagerID,
        handleGetLevelDistribution,
        handleGetInstitutionsByLevel,
        handleGetTop3Institutions,
        dashboardGetInstitution,
        trendingGetChart
    };
}
