import { ref } from 'vue';
import { useEvidences } from '@/hooks/useEvidences';
import { Logger } from '@/services/logger';

export function useEvidencesData() {
  const { handleGetEvidenceByProcessID } = useEvidences();
  const sectionsWithComments = ref<any[]>([]);
  const totalComments = ref(0);
  const expandedSection = ref<string | null>(null);

  const processEvidenceData = (apiData: any) => {
    Logger.debug('Processing evidence data', apiData);

    const sectionMap: Record<string, {
      color: string;
      name: string;
      icon: string;
      commentsCount: number;
      comments: { evidenceName: string; comment: string }[];
    }> = {};

    apiData.forEach((section: any) => {
      const commentsInSection = section.evidences
        .filter((evidence: any) => evidence.comment && evidence.comment.trim().length > 0)
        .map((evidence: any) => ({
          evidenceName: evidence.title,
          comment: evidence.comment,
        }));

      if (commentsInSection.length > 0) {
        sectionMap[section.name] = {
          color: section.color,
          name: section.name,
          icon: section.icon,
          commentsCount: commentsInSection.length,
          comments: commentsInSection,
        };
      }
    });

    sectionsWithComments.value = Object.values(sectionMap);
    totalComments.value = sectionsWithComments.value.reduce(
      (acc, section) => acc + section.commentsCount,
      0
    );
  };

  const fetchEvidenceData = async (institutionId: number) => {
    try {
      const apiData = await handleGetEvidenceByProcessID(institutionId);
      processEvidenceData(apiData);
    } catch (error) {
      Logger.error('Error getting evidence data', error);
    }
  };

  const toggleSection = (sectionName: string) => {
    Logger.debug(`Changing section status: ${sectionName}`, { expandedSection: expandedSection.value });
    expandedSection.value = expandedSection.value === sectionName ? null : sectionName;
  };

  return {
    sectionsWithComments,
    totalComments,
    expandedSection,
    fetchEvidenceData,
    toggleSection
  };
}
