import type { PaginationResponse } from "@/models/types/index";
import { ref, watch, type Ref, type UnwrapRef } from "vue";
import { Logger } from "@/services/logger";

/**
 * hook para manejar la paginación de los datos
 * 
 * @param fetchFunction función fetcher que recibe el número de página y retorna una promesa con los datos
 * 
 * @returns un objeto con funciones y datos para manejar la paginación
 * 
 * - data: datos de la página actual
 * - page: página actual
 * - totalPages: total de páginas
 * - fetchPageData: función para obtener los datos de la página actual
 * - handleNextPage: función para avanzar a la siguiente página
 * - handlePreviousPage: función para retroceder a la página anterior
 */

export const usePagination = <T>(fetchFunction: (page: number) => Promise<PaginationResponse<T>>) => {
    // UnwrapRef<T[]> se usa para evitar un error de TypeScript al desenvolver el valor de la interfaz genérica T
    const data: Ref<UnwrapRef<T[]>> = ref([]) as Ref<UnwrapRef<T[]>>;

    const page = ref<number>(0);
    const totalPages = ref<number>(0);
    const isLoading = ref<boolean>(false);

    watch(page, () => {
        fetchPageData(page.value);
    });

    const fetchPageData = async (page: number) => {
        try {
            isLoading.value = true;
            const result = await fetchFunction(page);
            data.value = result.data as UnwrapRef<T[]>;
            totalPages.value = result.totalPages;
        } catch (error: any) {
             Logger.error('Error getting data:', error)
        } finally {
            isLoading.value = false;
        }
    };

    const handleNextPage = () => {
        page.value++;
        window.scrollTo(0, 0);
    };

    const handlePreviousPage = () => {
        if (page.value > 0) {
            page.value--;
            window.scrollTo(0, 0);
        }
    };

    const handleGoToPage = (newPage: number) => {
        if (newPage >= 0 && newPage < totalPages.value) {
            page.value = newPage;
        }
    }

    return {
        data,
        page,
        isLoading,
        totalPages,
        fetchPageData,
        handleNextPage,
        handlePreviousPage,
        handleGoToPage
    };
};