import { shallowRef } from 'vue';

export function useAsyncFetch<T, P = void>(asyncFunction: (params: P) => Promise<T>) {
    const data = shallowRef<T | null>(null);
    const isLoading = shallowRef(false);
    const error = shallowRef<Error | null>(null);

    const fetchData = async (params: P) => {
        isLoading.value = true;
        error.value = null;
        try {
            data.value = await asyncFunction(params);
        } catch (err) {
            error.value = err as Error;
        } finally {
            isLoading.value = false;
        }
    };

    return {
        data,
        isLoading,
        error,
        fetchData,
    };
}
