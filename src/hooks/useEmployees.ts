import { ref, computed } from "vue";
import { useExcel } from "./useExcel";
import { formatRUT, formatRutToFlat } from "@/helpers/formatters";
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { usePagination } from "./usePagination";
import { fetchActualProcessByInstitutionID } from "@/services/api/processService";

interface Employee {
    name: string;
    email: string;
    rut: string;
    selected: boolean;
}

export interface ParsedEmployees {
    employeesNames: string;
    employeesEmails: string;
    employeesRut: string;
}

const PAGE_SIZE = 12;

export const useEmployees = () => {
    const { parseExcelFile } = useExcel();
    const employees = ref<Employee[]>([]);
    const totalEmployees = ref<number>(0);
    const excelRawFile = ref<File | null>(null);

    const fetchEmployees = async (page: number) => {
        const start = page * PAGE_SIZE;
        const end = start + PAGE_SIZE;
        return {
            data: employees.value.slice(start, end),
            totalPages: Math.ceil(employees.value.length / PAGE_SIZE)
        };
    };

    const {
        data: paginatedEmployees,
        page,
        totalPages,
        fetchPageData,
        handleNextPage,
        handlePreviousPage,
        handleGoToPage,
        isLoading
    } = usePagination(fetchEmployees);

    const handleEmployeesFileUpload = async (event: Event) => {
      const file = (event.target as HTMLInputElement).files?.[0]
      if (file) {
        try {
          const EMPLOYEE_EXCEL_COLUMNS = 'Nombre,Correo,Rut'
          const columns = EMPLOYEE_EXCEL_COLUMNS.split(',')
          const jsonData = await parseExcelFile(file, columns)

          const newEmployees: Employee[] = jsonData.map((row: any) => ({
            name: row[columns[0]]?.trim() || '',
            email: row[columns[1]]?.trim().toLowerCase() || '',
            rut: row[columns[2]]?.trim().toLowerCase() || '',
            selected: false
          }))

          // Evitar duplicados por RUT
          const existingRuts = new Set(employees.value.map((emp) => formatRutToFlat(emp.rut)))
          const filteredNewEmployees = newEmployees.filter(
            (emp) => !existingRuts.has(formatRutToFlat(emp.rut))
          )

          employees.value.push(...filteredNewEmployees)

          totalEmployees.value = employees.value.length
          await fetchPageData(page.value)
          excelRawFile.value = file
        } catch (err: any) {
          console.error('Error al cargar archivo de empleados:', err.message)
          throw new Error(
            'No se pudo procesar el archivo. Verifique que tenga las columnas requeridas.'
          )
        }
      }
    }

    const addEmployee = (employeeFormData: any) => {
        employees.value.push({
            ...employeeFormData,
            selected: false
        });
    
        totalEmployees.value = employees.value.length; 
        fetchPageData(page.value);
    };
    

    const removeEmployee = (index: number) => {
        employees.value.splice(index, 1);

        totalEmployees.value = employees.value.length;

        if (employees.value.length === 0 && page.value > 0) {
            handlePreviousPage();
        }

        fetchPageData(page.value);
    };

    const handleGeneratePDFFile = async (institutionId: number) => {
        try {
            const actualProcess = await fetchActualProcessByInstitutionID(institutionId);

            if (!actualProcess || !actualProcess.employees) {
                throw new Error("No se encontraron empleados o proceso activo para esta institución.");
            }

            const doc = new jsPDF();
            const processEmployees = actualProcess.employees;

            doc.text(actualProcess.simpleInstitutionDTO.name, 14, 15);
            doc.setFontSize(11);
            doc.text("Listado de empleados", 14, 23);

            autoTable(doc, {
                startY: 31,
                head: [['ID', 'Nombre', 'Email', 'RUT']],
                body: processEmployees.map((employee, index) => [
                    index + 1,
                    employee.name,
                    employee.email,
                    formatRUT(employee.rut)
                ])
            });

            doc.save('empleados.pdf');
        } catch (error: any) {
            console.error("Error generando PDF:", error.message);
            throw new Error("No fue posible generar el archivo PDF.");
        }
    };

    const parseEmployeesToConcatenatedStrings = (): ParsedEmployees => {
        const employeesNames = employees.value.map(item => item.name).join(', ');
        const employeesEmails = employees.value.map(item => item.email).join(', ');
        const employeesRut = employees.value.map(item => formatRutToFlat(item.rut)).join(', ');

        return { employeesNames, employeesEmails, employeesRut };
    };

    const totalPagesCalculated = computed(() =>
        Math.ceil(employees.value.length / PAGE_SIZE)
    );

    const rawEmployees = employees;

    return {
        employees: paginatedEmployees,
        rawEmployees,
        page,
        totalPages,
        totalPagesCalculated,
        totalEmployees,
        excelRawFile,
        handleEmployeesFileUpload,
        addEmployee,
        removeEmployee, 
        handleNextPage,
        handlePreviousPage,
        handleGoToPage,
        parseEmployeesToConcatenatedStrings,
        handleGeneratePDFFile,
        fetchPageData,
        isLoading
    };
};
