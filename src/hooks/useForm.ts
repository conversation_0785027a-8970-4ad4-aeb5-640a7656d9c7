import { computed, reactive, ref, watch, type Ref } from 'vue';
import { useVuelidate } from '@vuelidate/core';
import { Logger } from '@/services/logger';

/**
 * Custom hook para manejar la lógica de formularios
 * @param initialData - Datos iniciales para el formulario
 * @param validationRules - Reglas de validación para el formulario
 * @param updateCondition - Condición para determinar si se está en modo actualización
 * @param onSubmit - Función callback para manejar el envío del formulario
 * @returns - Objetos y funciones relacionadas con la lógica del formulario
 */

interface UseFormOptions<T> {
    initialDataRef: Ref<T>;
    validationRules: any;
    updateCondition: Ref<boolean>;
    onSubmit: (data: T) => Promise<void>;
    updateOnlyModified?: boolean;
}

export function useForm<T extends Record<string, any>>(
    {
        initialDataRef,
        validationRules,
        updateCondition,
        onSubmit,
        updateOnlyModified = false
    }: UseFormOptions<T>
) {
    const formData = reactive<T>(initialDataRef ? { ...initialDataRef.value } : {} as T);
    const $vForm = useVuelidate(validationRules, formData, { $scope: false });
    const isLoading = ref<boolean>(false);

    watch(initialDataRef, (newData) => {
        JSON.stringify(newData) !== '{}' ? Object.assign(formData, newData)
            : Object.keys(formData).forEach(key => {
                delete formData[key];
            });
        $vForm.value.$reset();
    }, { deep: true });

    const hasChanges = computed(() => {
        return (Object.keys(formData) as Array<keyof T> as any).some((key: any) => {
            const currentValue = formData[key];
            const initialValue = initialDataRef.value[key];
            return currentValue !== initialValue;
        });
    });

    const handleSubmit = async () => {
        try {
            Logger.info('Submitting form', formData);
            isLoading.value = true;
            const result = await $vForm.value.$validate();
            if (result) {
                if (updateCondition.value && !hasChanges.value) {
                    Logger.warn('No changes detected, skipping submission');
                    return;
                }
                
                const submissionData = updateCondition.value && updateOnlyModified 
                    ? getUpdatedFields(initialDataRef.value, formData as any) as any 
                    : formData as any;
                
                await onSubmit(submissionData);
                Logger.info('Form submitted successfully', submissionData);
            }
        } catch (error) {
            Logger.error('Error submitting form', error);
        } finally {
            isLoading.value = false;
        }
    };

    const getUpdatedFields = (oldData: T, newData: T): Partial<T> => {
        const updatedFields: Partial<T> = {};
        Object.keys(newData).forEach(key => {
            if (newData[key as keyof T] !== oldData[key as keyof T]) {
                updatedFields[key as keyof T] = newData[key as keyof T];
            }
        });
        return updatedFields;
    }

    return {
        formData,
        $vForm,
        hasChanges,
        handleSubmit,
        isLoading
    };
}