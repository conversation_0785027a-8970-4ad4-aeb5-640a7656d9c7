import type { Dimension, Evidence } from "@/models/types";
import { useExcel } from "./useExcel";
import { ref } from "vue";
import dimensionsJson from "@/assets/JsonData/dimensions.json";
import { createDimension } from "@/services/api/dimensionsService";
import { mapDimensionToDimensionFormSchema } from "@/mappers";
import { fetchAppealedEvidencesByProcessID, fetchEvidencesWithDimensionsByProcessID, fetchRejectedEvidencesByProcessID } from "@/services/api/evidenceService";
import { Logger } from "@/services/logger";

/**
 * Hook para manejar las dimensiones
 * 
 * @returns Un objeto con funciones y datos para manejar las dimensiones.
 * 
 * - `actualDimension`: Dimensión actual
 * - `dimensions`: Dimensiones
 * - `handleDimensionsFileUpload`: Función para subir un archivo de dimensiones
 * - `handleCreateDimension`: Función para crear una dimensión
 */
export const useDimensions = () => {
    const { parseExcelFile } = useExcel();
    const dimensions = ref<Dimension[]>([]);
    const actualDimension = ref<Dimension>();

    const handleDimensionsFileUpload = async (event: Event) => {
        const file = (event.target as HTMLInputElement).files?.[0];
        if (file) {
            try {
                const MODEL_EXCEL_COLUMNS = "Dimensión,Descripción,Nombre evidencia,Condiciones de aprobación,Nivel,Pregunta";
                const columns = MODEL_EXCEL_COLUMNS?.split(',') || [];
                const jsonData = await parseExcelFile(file, columns);
                Logger.debug("File parsed successfully", jsonData);

                const groupedDimensions: Record<string, Dimension> = {};

                jsonData.forEach((row: any) => {
                    const dimensionName = row[columns[0]] || '';
                    if (!groupedDimensions[dimensionName]) {
                        groupedDimensions[dimensionName] = {
                            name: dimensionName,
                            description: '',
                            evidences: [],
                            questions: []
                        };
                    }

                    if (row[columns[2]]) {
                        const evidence: Evidence = {
                            title: row[columns[2]] || '',
                            type: 'PDF',
                            approvalRequirements: row[columns[3]]
                        };
                        groupedDimensions[dimensionName].evidences?.push(evidence);
                    }

                    groupedDimensions[dimensionName].description = row[columns[1]];
                    groupedDimensions[dimensionName].questions!.push({
                        text: row[columns[5]] || ''
                    });
                });

                dimensions.value = Object.values(groupedDimensions);
                dimensions.value.forEach(dimension => {
                    const { icon, color } = getDimensionStyle(dimension.name);
                    if (icon && color) {
                        dimension.icon = icon;
                        dimension.color = color;
                    }
                });

                actualDimension.value = dimensions.value[0];

            } catch (err: any) {
                Logger.error("Error uploading dimensions file", err);
                throw new Error(err.message);
            }
        }
    };

    const getDimensionStyle = (dimensionName: string) => {
        const icon = dimensionsJson.find(d => d.name.toLowerCase() === dimensionName.toLowerCase())?.icon;
        const color = dimensionsJson.find(d => d.name.toLowerCase() === dimensionName.toLowerCase())?.color;
        return { icon, color };
    };

    const handleCreateDimension = async (dimension: Dimension) => {
        try {
            const mappedDimension = mapDimensionToDimensionFormSchema(dimension);
            const response = await createDimension(mappedDimension);
            Logger.info("Dimension created successfully", response);
            return response;
        } catch (error) {
            Logger.error("Error creating dimension", error);
            throw error;
        }
    };

    const handleGetDimensionsByProcessID = async (processID: number) => {
        try {
            const dimensions = await fetchEvidencesWithDimensionsByProcessID(processID);
            const filteredDimensions = dimensions.filter((dimension: any) => dimension.evidences.length > 0);
            Logger.info(`Dimensions obtained: ${filteredDimensions.length}`);
            return filteredDimensions;
        } catch (error) {
            Logger.error(`Error getting dimensions for processID: ${processID}`, error);
            throw error;
        }
    };

    const handleGetAppealedDimensionsWithEvidences = async (processID: number): Promise<Dimension[]> => {
        try {
            const dimensions = await fetchAppealedEvidencesByProcessID(processID);

    
            const filteredDimensions = dimensions.filter((dimension: Dimension) => {
                return Array.isArray(dimension.evidences) && dimension.evidences.length > 0;
            });
    
            Logger.info(`Appealed dimensions obtained: ${filteredDimensions.length}`);
    
            return filteredDimensions;
        } catch (error) {
            Logger.error(`Error getting appealed dimensions for processID: ${processID}`, error);
            throw error;
        }
    };

    const handleGetRejectedDimensionsByProcessID = async (processID: number) => {
        try {
            const dimensions = await fetchRejectedEvidencesByProcessID(processID);
            const filteredDimensions = (Array.isArray(dimensions) ? dimensions : Object.values(dimensions))
                .map((category: any) => {
                    const rejectedEvidences = (Array.isArray(category.evidences) ? category.evidences : [])
                        .filter((evidence: any) => evidence.evidenceStatus === 'REJECTED');
                    rejectedEvidences.forEach((evidence: any) => {
                        const correspondingRequirements = category.evidenceRequirements.map((requirement: any) => ({
                            title: requirement.title,
                            requirement: requirement.requirement
                        }));
                        evidence.approvalRequirements = correspondingRequirements;
                    });
                    return {
                        dimension: category,
                        rejectedEvidences
                    };
                });
    
            Logger.info(`Rejected dimensions obtained: ${filteredDimensions.length}`, filteredDimensions);
            return filteredDimensions;
        } catch (error) {
            Logger.error(`Error fetching rejected dimensions for processID: ${processID}`, error);
            throw error;
        }
    };
    
    
    
    

    return {
        actualDimension,
        dimensions,
        handleDimensionsFileUpload,
        handleCreateDimension,
        handleGetDimensionsByProcessID,
        handleGetAppealedDimensionsWithEvidences,
        handleGetRejectedDimensionsByProcessID
    };
};