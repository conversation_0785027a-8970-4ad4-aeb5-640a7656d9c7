import { ref, computed, watch, type Ref } from 'vue';
import { useInstitutions } from '@/hooks/useInstitutions';
import { useTabs, type Tab } from '@/hooks/useTabs';
import type { InstitutionBaseInfoSchema, InstitutionFormSchema, InstitutionLegalInfoFormSchema, GestorFormSchema } from '@/models/validation/formSchemas';
import type { Institution } from '@/models/types/index';
import { mapInstitutionToInstitutionFormSchema } from '@/mappers';
import InstitutionBaseInfoForm from '@/components/molecules/InstitutionBaseInfoForm.vue';
import InstitutionLegalInfoForm from '@/components/molecules/InstitutionLegalInfoForm.vue';
import GestorForm from '@/components/molecules/GestorForm.vue';
import AssignAuditorForm from '@/components/molecules/AssignAuditorForm.vue';
import { useManagers } from './useManagers';
import { Logger } from '@/services/logger';
/**
 * hook para manejar los pasos del formulario de instituciones
 * 
 * @param institutionDataRef referencia a los datos de la institución, si es una actualización
 * 
 * @returns un objeto con funciones y datos para manejar los pasos del formulario
 * 
 * - isUpdate: indica si el formulario es para actualizar una institución
 * - activeTabComponent: componente activo en el paso actual
 * - activeTab: paso actual
 * - nextTab: función para avanzar al siguiente paso
 * - prevTab: función para retroceder al paso anterior
 * - setActiveTab: función para establecer el paso activo manualmente (actualizar)
 * - institutionFormData: datos del formulario de la institución
 * - nextStep: función para avanzar al siguiente paso y guardar los datos del paso actual
 * - prevStep: función para retroceder al paso anterior
 * - getCurrentStepInfo: función para obtener los datos del paso actual
 * - handleSubmit: función para enviar los datos del formulario
 * - handleUpdateSubmit: función para enviar los datos del formulario en una actualización
 * - tabs: pasos del formulario
 */

const TAB_MAPPING = {
    'Institución': 'baseInfo',
    'Contacto': 'legalInfo',
    'Gestor': 'gestorInfo',
    'Auditor': 'auditorInfo'
} as Record<string, string>;

const baseTabs: Tab[] = [{
    name: 'Institución',
    title: 'Información Básica',
    description: 'Ingrese los datos básicos de la institución.',
    component: InstitutionBaseInfoForm
}, {
    name: 'Contacto',
    title: 'Información de Contacto',
    description: 'Ingrese los datos de contacto específicos de la institución.',
    component: InstitutionLegalInfoForm
}, {
    name: 'Gestor',
    title: 'Informacion del Gestor',
    description: 'Ingrese los datos del gestor de la institución.',
    component: GestorForm
}];

const auditorTab: Tab = {
    name: 'Auditor',
    title: 'Actualizar Institución - Asignar Auditor',
    description: 'Asigne un auditor a la institución.',
    component: AssignAuditorForm
};

export const useInstitutionsFormSteps = (institutionDataRef: Ref<Institution | undefined>) => {
    const isUpdate = ref<boolean>(false);

    const tabs = computed(() => {
        if (isUpdate.value) {
            return [...baseTabs, auditorTab];
        }
        return baseTabs;
    });

    const { handleCreateInstitution, handleUpdateInstitution } = useInstitutions();
    const { handleUpdateManager } = useManagers();
    const { activeTabComponent, activeTab, nextTab, prevTab, setActiveTab } = useTabs(tabs);
    const institutionFormData = ref<InstitutionFormSchema | null>(null);

    watch(institutionDataRef, (newInstitution) => {
        isUpdate.value = newInstitution ? !!newInstitution.id : false;
        if (newInstitution) {
            institutionFormData.value = mapInstitutionToInstitutionFormSchema(newInstitution);
        } else {
            institutionFormData.value = null;
        }
    }, { immediate: true });

    const nextStep = async (stepData: any) => {
        const currentTab = activeTab.value.name;
        const formSection = TAB_MAPPING[currentTab];

        institutionFormData.value = {
            ...institutionFormData.value,
            [formSection]: stepData,
        };

        if (!isUpdate.value) {
            if (currentTab === 'Gestor') {
                await handleSubmit(institutionFormData.value);
            }
            nextTab();
        } else {
            await handleUpdateSubmit(stepData, currentTab);
        }
    };

    const prevStep = () => {
        prevTab();
    };

    const getCurrentStepInfo = () => {
        const currentTab = activeTab.value.name;
        const formSection = TAB_MAPPING[currentTab];

        if (institutionFormData.value) {
            return institutionFormData.value[formSection as keyof InstitutionFormSchema];
        }
    };

    const handleSubmit = async (institutionData: InstitutionFormSchema) => {
        try {
            if (isUpdate.value && institutionDataRef.value) {
                await handleUpdateInstitution(institutionDataRef.value.id, institutionDataRef.value.manager!.id, institutionData);
            } else {
                await handleCreateInstitution(institutionData);
            }
            window.location.reload();
        } catch (error: any) {
            Logger.error('Error submitting the form', error);
        }
    };

    const handleUpdateSubmit = async (updatedData: InstitutionBaseInfoSchema | InstitutionLegalInfoFormSchema | GestorFormSchema, currentTab: string) => {
        try {
            const formSection = TAB_MAPPING[currentTab] as keyof InstitutionFormSchema;
            const updatedInstitutionData = {
                ...institutionFormData.value,
                [formSection]: updatedData
            };

            if (currentTab === 'Gestor') {
                await handleUpdateManager(institutionDataRef.value!.manager!.id, updatedData as GestorFormSchema);
                Logger.info('Manager updated');
            }

            await handleUpdateInstitution(institutionDataRef.value!.id, institutionDataRef.value!.manager!.id, updatedInstitutionData);
            Logger.info('Institution data updated successfully');
            window.location.reload();
        } catch (error: any) {
            Logger.error('Error updating data', error);
        }
    };

    return {
        isUpdate,
        activeTabComponent,
        activeTab,
        nextTab,
        prevTab,
        setActiveTab,
        institutionFormData,
        nextStep,
        prevStep,
        getCurrentStepInfo,
        handleSubmit,
        handleUpdateSubmit,
        tabs
    };
};