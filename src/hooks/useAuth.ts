import { ref, watchEffect } from 'vue';
import { jwtDecode } from "jwt-decode";
import { login } from '@/services/api/authService';
import type { AuthData, SimpleInstitutionDTO } from '@/models/types';
import { fetchManagerByID } from '@/services/api/managerService';
import { Logger } from '@/services/logger';

export const useAuth = () => {
  const authData = ref<AuthData>();

  const getToken = (): string | undefined => {
    const data = extractAuthData();
    return data ? data.token : undefined;
  }

  const logout = (): void => {
    localStorage.removeItem('authData');
    authData.value = undefined;
    window.location.reload();
  }

  const handleLogin = async (params: { email: string, password: string }): Promise<void> => {
    try {

      const token = await login(params.email, params.password);
      const decodedToken = jwtDecode(token) as any;

      const auth: AuthData = {
        token,
        id: decodedToken.id,
        username: decodedToken.sub,
        role: decodedToken.role,
        exp: decodedToken.exp
      };

      if (auth.role === 'MANAGER') {
        auth.simpleInstitutionDTO = await handleManagerLogin(auth.id, auth.token);
      }

      saveAuthData(auth);

      window.location.reload();
    } catch (error: any) {
      Logger.error("Error logging in:", { message: error.message });
      throw Error("Credenciales no válidas");
      
    }
  }

  const saveAuthData = (data: AuthData): void => {
    localStorage.setItem('authData', JSON.stringify(data));
    authData.value = data;
  }

  const extractAuthData = (): AuthData | null => {
    const authDataString = localStorage.getItem('authData');
    if (authDataString) {
      return JSON.parse(authDataString) as AuthData;
    }
    return null;
  }

  const handleManagerLogin = async (managerId: number, token: string): Promise<SimpleInstitutionDTO> => {
    try {
      const institution = (await fetchManagerByID(managerId, token)).institution!;
      return {
        id: institution.id,
        name: institution.name,
        city: institution.city,
        acronym: institution.acronym,
        color: institution.color
      }
    } catch (error: any) {
      Logger.error("Error getting data from manager", { message: error.message });
      throw error;
    }
  }

  watchEffect(() => {
    authData.value = extractAuthData()!;
  });

  return {
    authData,
    handleLogin,
    logout,
    getToken
  };
};
