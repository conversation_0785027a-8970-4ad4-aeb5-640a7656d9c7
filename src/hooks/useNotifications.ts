import { ref } from 'vue';
import type { NotificationResponse } from '@/models/types/notificationResponse'; 
import { fetchNotification } from '@/services/api/notificationService';
import { Logger } from '@/services/logger';

export const useNotifications = (userId:number, role:string) => {
    const notificationsRef = ref<string[]>([]);

    const fetchData = async () => {
        try {
            const data: NotificationResponse =  await fetchNotification(userId, role); ; 
            notificationsRef.value = data.notifications;
        } catch (error) {
             Logger.error('Error getting data:', error)
        }
    };
    return {
        fetchData,
        notifications: notificationsRef,
    };
};
