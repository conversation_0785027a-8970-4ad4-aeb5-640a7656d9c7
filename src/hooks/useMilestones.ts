import { ref } from 'vue';
import type { Milestones } from '@/models/types';
import { Logger } from '@/services/logger';

export const useMilestones = () => {
  const milestones = ref<Milestones[]>([]);

  const fetchData = async () => {
    try {
      const response = await fetch('/hitos.json');
      const data = await response.json();
      milestones.value = data;
    } catch (error) {
      Logger.error('Error getting data: ', error)
    }
  };

  return {
    fetchData,
    milestones
  };
}
