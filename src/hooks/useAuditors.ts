import { createAuditor, fetchAllAuditors, fetchAuditorByID, toggleAuditorStatus, updateAuditor } from "@/services/api/auditorService";
import { usePagination } from "./usePagination";
import type { AuditorFormSchema } from "@/models/validation/formSchemas";
import type { Auditor } from "@/models/types";
import { Logger } from "@/services/logger";

/**
 * hook para manejar la lógica relacionada con los auditores
 * 
 * @returns un objeto con funciones y datos para manejar los auditores
 * 
 * - auditors: auditores
 * - page: página actual
 * - totalPages: total de páginas
 * - fetchPageData: función para obtener los datos de la página actual
 * - handleSearchAuditors: función para buscar auditores
 * - handleCreateAuditor: función para crear un auditor
 * - handleUpdateAuditor: función para actualizar un auditor
 * - handleToggleAuditorStatus: función para deshabilitar un auditor
 * - handleNextPage: función para avanzar a la siguiente página
 * - handlePreviousPage: función para retroceder a la página anterior
 */

export const useAuditors = () => {
    const fetchAuditors = (page: number) => fetchAllAuditors(page);
    const { data: auditors, page, totalPages, fetchPageData, handleNextPage, handlePreviousPage, isLoading, handleGoToPage } = usePagination<Auditor>(fetchAuditors);

    const handleSearchAuditors = async (search: string) => {
        try {
            isLoading.value = true;
            auditors.value = (await fetchAllAuditors(page.value, search)).data;
        } catch (error: any) {
            Logger.error("Error searching for auditors", { message: error.message });
        } finally {
            isLoading.value = false;
        }
    };

    const handleCreateAuditor = async (auditor: AuditorFormSchema) => {
        try {
            const newAuditor = await createAuditor(auditor);
            auditors.value = [newAuditor, ...auditors.value];
            window.location.reload();
        } catch (error: any) {
            Logger.error("Error creating auditor", { message: error.message });
        }
    };

    const handleUpdateAuditor = async (auditorId: number, auditor: AuditorFormSchema) => {
        try {
            const updatedAuditor: Auditor = await updateAuditor(auditorId, auditor);
            auditors.value = auditors.value.map(aud =>
                aud.id === updatedAuditor.id ? updatedAuditor : aud
            );
            window.location.reload();
        } catch (error: any) {
            Logger.error("Error updating auditor", { message: error.message });
        }
    };

    const handleToggleAuditorStatus = async (auditorId: number) => {
        try {
            await toggleAuditorStatus(auditorId);
        } catch (error: any) {
            Logger.error("Error changing auditor status", { message: error.message });
        }
    };

    const handleGetAuditorByID = async (auditorId: number) => {
        try {
            isLoading.value = true;
            const auditor = await fetchAuditorByID(auditorId);
            return auditor;
        } catch (error: any) {
            Logger.error("Error getting auditor", { message: error.message });
        } finally {
            isLoading.value = false;
        }
    };

    return {
        auditors,
        page,
        isLoading,
        totalPages,
        handleGoToPage,
        fetchPageData,
        handleSearchAuditors,
        handleGetAuditorByID,
        handleCreateAuditor,
        handleUpdateAuditor,
        handleToggleAuditorStatus,
        handleNextPage,
        handlePreviousPage,
    };
};
