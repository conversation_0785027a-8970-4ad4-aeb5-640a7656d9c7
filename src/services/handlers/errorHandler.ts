
/**
 * handler para manejar errores en las peticiones fetch
 * @param response respuesta de la petición
 * @param errorMessage mensaje de error personalizado
 * @throws un error con el mensaje de error de la respuesta
 */

export interface ErrorResponse {
    status: number;
    errorCode: string;
    message: string;
    timestamp: string;
}

export const handleFetchError = async (response: Response, errorMessage?: string): Promise<never> => {
    const responseData: ErrorResponse = await response.json();
    const serverError = responseData.errorCode ? `${responseData.errorCode}: ${responseData.message}` : 'Unknown error';
    const fullErrorMessage = errorMessage ? `${errorMessage}: ${serverError}` : serverError;

    throw new Error(fullErrorMessage);
};