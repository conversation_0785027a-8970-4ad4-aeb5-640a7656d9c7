import { handleFetchError } from './errorHandler';
import { Logger } from '../logger';

/**
 * handler para manejar errores en las peticiones fetch
 * @param url a la que se hace la petición
 * @param options opciones de la petición como FetchOptions (extiende a RequestInit)
 * @returns promesa con la respuesta de la petición
 */

interface FetchOptions extends RequestInit {
    errorMessage?: string;
    token?: string;
    responseType?: 'json' | 'blob';
}

export const fetchWithHandling = async (url: string, options?: FetchOptions): Promise<any> => {
    const headers = new Headers(options?.headers);
    const controller = new AbortController();
    const signal = controller.signal;


    if (options?.token) {
        headers.append('Authorization', `Bearer ${options.token}`);
    }

    try {
        const response = await fetch(url, {
            ...options,
            headers,
            signal
        });

        if (!response.ok) {
            await handleFetchError(response, options?.errorMessage);
        }

        const responseHandlers: Record<'json' | 'blob', () => Promise<any>> = {
            json: () => response.json(),
            blob: () => response.blob()
        };
        return responseHandlers[options?.responseType || 'json']();
        
    } catch (error: any) {
        if (error.name === 'AbortError') {
            Logger.error(`[fetchWithHandling] Timeout reached at: ${url}`);
            Logger.debug(`Error details: ${error.message}`);
            throw new Error('Timeout de la solicitud alcanzado');
        }

        Logger.error(`[fetchWithHandling] Error in request to: ${url}`, error);
        throw error;
    }
};
