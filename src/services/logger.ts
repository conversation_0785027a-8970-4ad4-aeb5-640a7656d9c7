export enum LogLevel {
    DEBUG = "DEBUG",
    INFO = "INFO",
    WARN = "WARN",
    ERROR = "ERROR",
  }
  
  export class Logger {
    private static formatMessage(level: LogLevel, message: string, data?: unknown) {
      return {
        timestamp: new Date().toISOString(),
        level,
        message,
        data: data || null,
      };
    }
  
    private static logToConsole(logObject: ReturnType<typeof Logger.formatMessage>) {
      const { level, timestamp, message, data } = logObject;
  
      switch (level) {
        case LogLevel.DEBUG:
          console.debug(`[${timestamp}] [DEBUG]: ${message}`, data);
          break;
        case LogLevel.INFO:
          console.info(`[${timestamp}] [INFO]: ${message}`, data);
          break;
        case LogLevel.WARN:
          console.warn(`[${timestamp}] [WARN]: ${message}`, data);
          break;
        case LogLevel.ERROR:
          console.error(`[${timestamp}] [ERROR]: ${message}`, data);
          break;
      }
    }
  
    static debug(message: string, data?: unknown) {
      const log = this.formatMessage(LogLevel.DEBUG, message, data);
      this.logToConsole(log);
    }
  
    static info(message: string, data?: unknown) {
      const log = this.formatMessage(LogLevel.INFO, message, data);
      this.logToConsole(log);
    }
  
    static warn(message: string, data?: unknown) {
      const log = this.formatMessage(LogLevel.WARN, message, data);
      this.logToConsole(log);
    }
  
    static error(message: string, data?: unknown) {
      const log = this.formatMessage(LogLevel.ERROR, message, data);
      this.logToConsole(log);
    }
  }
  