import type { Auditor, PaginationResponse } from "@/models/types";
import type { AuditorFormSchema } from "@/models/validation/formSchemas";
import { fetchWithHandling } from "../handlers/fetchHandler";
import { useAuth } from "@/hooks/useAuth";
import { buildQueryParams } from "./helpers/apiHelpers";

/**
 * Servicio para manejar las mutaciones de los auditores
 */

const API_URL = `${import.meta.env.VITE_USER_API_URL}/auditors`

/**
 * método para obtener todos los auditores
 * @param page página a obtener (paginación)
 * @returns un PaginationResponse, con los auditores y el total de páginas
 */
export const fetchAllAuditors = async (page: number = 0, name: string = ""): Promise<PaginationResponse<Auditor>> => {
    const { getToken } = useAuth();
    const queryParams = buildQueryParams({ page, name });

    const responseData = await fetchWithHandling(`${API_URL}?${queryParams}`, {
        errorMessage: "Error fetching auditors",
        token: getToken(),
    });

    return {
        data: responseData.data.auditors as Auditor[],
        totalPages: responseData.data.totalPages,
    }
}

/**
 * Método para obtener un auditor por ID
 * @param auditorID ID del auditor
 * @returns el auditor encontrado
 */
export const fetchAuditorByID = async (auditorID: number): Promise<Auditor> => {
    const { getToken } = useAuth();
    return (await fetchWithHandling(`${API_URL}/${auditorID}`, {
        token: getToken(),
        errorMessage: "Error fetching auditor with ID: " + auditorID
    })).data
}

/**
 * método para crear un auditor
 * @param auditor datos del auditor a crear como AuditorFormSchema
 * @returns el auditor creado
 */
export const createAuditor = async (auditor: AuditorFormSchema): Promise<Auditor> => {
    const { getToken } = useAuth();
    return (await fetchWithHandling(API_URL, {
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify(auditor),
        errorMessage: "Error creating auditor",
        token: getToken()
    })).data;
}

/**
 * método para actualizar un auditor
 * @param auditorId ID del auditor a actualizar
 * @param auditor datos del auditor a actualizar como AuditorFormSchema
 * @returns el auditor actualizado
 */
export const updateAuditor = async (auditorId: number, auditor: AuditorFormSchema): Promise<Auditor> => {
    const { getToken } = useAuth();
    return (await fetchWithHandling(`${API_URL}/${auditorId}`, {
        method: "PATCH",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify(auditor),
        errorMessage: `Error updating auditor with ID: ${auditorId}`,
        token: getToken()
    })).data;
}

/**
 * método para alternar el estado de un auditor (habilitado/deshabilitado)
 * @param auditorId ID del auditor a actualizar
 * @returns void
 */
export const toggleAuditorStatus = async (auditorId: number): Promise<void> => {
    const { getToken } = useAuth();
    await fetchWithHandling(`${API_URL}/${auditorId}/status`, {
        method: "PATCH",
        errorMessage: `Error toggling auditor status with ID: ${auditorId}`,
        token: getToken()
    });
}