import { fetchWithHandling } from "../handlers/fetchHandler";

/**
 * Servicio de autenticación
 * 
 */

const API_URL = `${import.meta.env.VITE_USER_API_URL}/auth`;

export const login = async (email: string, password: string): Promise<string> => {
  const response = await fetchWithHandling(`${API_URL}/login`, {
    method: "POST",
    body: JSON.stringify({ username: email, password }),
  })

  return response.token;
};
