
/**
 * método para construir los parámetros de una query dinámicamente
 * @param params los parámetros a agregar a la query
 * @returns una cadena de texto con los parámetros de la query
 */
export const buildQueryParams = (params: Record<string, any>): string => {
    const queryParams = new URLSearchParams();
    Object.keys(params).forEach(key => {
        const value = params[key];
        if (value !== undefined && value !== null && value !== "") {
            queryParams.append(key, value.toString());
        }
    });
    return queryParams.toString();
}