import type { DashboardKPIStats, KPICard } from "@/models/types/index";
import { fetchWithHandling } from "../handlers/fetchHandler";
import { useAuth } from "@/hooks/useAuth";
import { Logger } from "../logger";

const API_URL = `${import.meta.env.VITE_USER_API_URL}/admins`;

/**
 * Servicio de administradores
 */

export const fetchDashboardKPIStats = async (): Promise<KPICard[]> => {
    try {
        const stats = await getDashboardStats();
        return mapToKPICards(stats);
    } catch (error: any) {
        handleError("fetchDashboardKPIStats", error);
        throw new Error("No se pudieron obtener los KPI del dashboard.");
    }
};

export const fetchDashboardStats = async (): Promise<DashboardKPIStats> => {
    try {
        return await getDashboardStats();
    } catch (error: any) {
        handleError("fetchDashboardStats", error);
        throw new Error("No se pudieron obtener las estadísticas del dashboard.");
    }
};

const getDashboardStats = async (): Promise<DashboardKPIStats> => {
    const { getToken } = useAuth();

    const response = await fetchWithHandling(`${API_URL}/dashboard-stats`, {
        token: getToken(),
        errorMessage: "Error al obtener estadísticas del dashboard",
    });

    if (!response || !response.data) {
        throw new Error("Respuesta vacía o inválida desde el servidor.");
    }

    return response.data as DashboardKPIStats;
};

const mapToKPICards = (stats: DashboardKPIStats): KPICard[] => {
    return [
        {
            title: "Instituciones",
            value: stats.numberOfInstitutions?.toString() ?? '0',
            icon: "fas fa-building",
            color: "#4E36E2"
        },
        {
            title: "Auditores",
            value: stats.numberOfAuditors?.toString() ?? '0',
            icon: "fas fa-user-tie",
            color: "#48A9F8"
        },
        {
            title: "Auditorias Realizadas",
            value: stats.totalCompletedAudits?.toString() ?? '0',
            icon: 'fas fa-clipboard-check',
            color: '#2AA88A',
        },
        {
            title: 'Procesos en curso',
            value: stats.processesInProgress?.toString() ?? '0',
            icon: 'fas fa-spinner',
            color: '#8BC740',
        },
        {
            title: 'Personas Encuestadas',
            value: stats.peopleSurveyed?.toString() ?? '0',
            icon: 'fas fa-people-group',
            color: '#3666E2',
        },
    ];
};

const handleError = (context: string, error: any) => {
    const isDev = import.meta.env.MODE === 'development';

    Logger.error(`[${context}]`, error);

    if (isDev) {
        console.error(`[${context}]`, error);
    }
};
