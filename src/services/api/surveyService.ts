import type { Survey } from "@/models/types";
import { fetchWithHandling } from "../handlers/fetchHandler";
import { Logger } from "../logger";
import { useAuth } from "@/hooks/useAuth";

const API_URL = `${import.meta.env.VITE_SURVEY_API_URL}/surveys`;

/**
 * método para crear una encuesta
 * @param processId id del proceso
 * @param processName nombre del proceso
 * @returns url de la encuesta creada
 */

export const createSurvey = async (processId: number, processName: string): Promise<string> => {
    try {
        const { getToken } = useAuth();
        const responseData = await fetchWithHandling(`${API_URL}/process/${processId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ title: processName }),
            errorMessage: `Error al crear una encuesta para el proceso con ID: ${processId}`,
            token: getToken(),
        });
        return responseData.surveyUrl;
    } catch (error: any) {
        Logger.error("Error creating survey:", error);
        throw new Error('Error al crear una encuesta: ' + error.message);
    }
};

/**
 * método para obtener el enlace de la encuesta por ID de proceso
 * @param processID id del proceso
 * @returns url de la encuesta
 */
export const fetchSurveyLinkByProcessID = async (processID: number): Promise<string> => {
    try {
        const { getToken } = useAuth();
        const responseData = await fetchWithHandling(`${API_URL}/process/${processID}/url`, {
            errorMessage: `Error fetching survey link by process ID: ${processID}`,
            token: getToken(),
        });
        return responseData.data.surveyUrl;
    } catch (error: any) {
        Logger.error("Error getting survey link by process ID:", error);
        throw new Error('Error al obtener el enlace de la encuesta por ID de proceso: ' + error.message);
    }
}

export const fetchCountConsent = async (processId: number, consent: boolean): Promise<string> => {
    try {
        const { getToken } = useAuth();
        const responseData = await fetchWithHandling(`${API_URL}/consent/${processId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ consentGiven: consent }),
            errorMessage: `Error al aceptar concentimeinto para la encuensta de ID: ${processId}`,
            token: getToken(),
        });
        return responseData.surveyUrl;
    } catch (error: any) {
        Logger.error("Error creating survey:", error);
        throw new Error('Error al crear una encuesta: ' + error.message);
    }
}

/**
 * metodo para obtener una encuesta por ID
 * @param surveyID id de la encuesta
 * @returns la encuesta
 */
export const fetchSurveyByID = async (surveyID: number): Promise<Survey> => {
    try {
        const response = await fetch(`${API_URL}/${surveyID}`);
        if (!response.ok) {
            Logger.warn(`Invalid response when getting survey with ID: ${surveyID}`);
            throw new Error('Error al obtener la encuesta por ID');
        }
        const data = await response.json();
        
        Logger.info(`Survey successfully completed: ${JSON.stringify(data)}`);
        return data;
    } catch (error: any) {
        Logger.error("Error getting survey by ID:", error);
        throw new Error('Error al obtener una encuesta por ID: ' + error.message);
    }
};
