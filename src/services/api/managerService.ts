import type { Gestor } from "@/models/types";
import { fetchWithHandling } from "../handlers/fetchHandler";
import type { GestorFormSchema } from "@/models/validation/formSchemas";
import { useAuth } from "@/hooks/useAuth";

/**
 * Servicio para manejar las mutaciones de los gestores
 */

const API_URL = `${import.meta.env.VITE_USER_API_URL}/managers`

/**
 * método para crear un gestor
 * @param gestorData datos del gestor a crear como GestorFormSchema
 * @returns el gestor creado
 */
export const createManager = async (gestorData: GestorFormSchema): Promise<Gestor> => {
    const { getToken } = useAuth();
    return (await fetchWithHandling(`${API_URL}`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify(gestorData),
        errorMessage: "Error creating gestor",
        token: getToken()
    })).data
}


/**
 * método para actualizar un gestor
 * @param gestorID id del gestor a actualizar
 * @param gestorData datos del gestor a actualizar como GestorFormSchema
 * @returns el gestor actualizado
 */
export const updateManager = async (gestorID: number, gestorData: GestorFormSchema): Promise<Gestor> => {
    const { getToken } = useAuth();
    return (await fetchWithHandling(`${API_URL}/${gestorID}`, {
        method: "PATCH",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify(gestorData),
        errorMessage: "Error updating gestor with ID " + gestorID,
        token: getToken()
    })).data
}

/**
 * método para cambiar el estado de un gestor (activo/inactivo)
 * @param gestorID id del gestor a eliminar
 * @returns
 */
export const toggleManagerStatus = async (gestorID: number): Promise<void> => {
    const { getToken } = useAuth();
    return (await fetchWithHandling(`${API_URL}/${gestorID}/status`, {
        method: "PATCH",
        token: getToken(),
        errorMessage: "Error toggling gestor status with ID " + gestorID
    })).data
}


/**
 * método para obteenr un gestor por su id
 * @param gestorID id del gestor a obtener
 * @param token token de autenticación opcional
 * @returns el gestor con el id especificado
 */
export const fetchManagerByID = async (gestorID: number, token?: string): Promise<Gestor> => {
    const { getToken } = useAuth();
    return (await fetchWithHandling(`${API_URL}/${gestorID}`, {
        token: token || getToken(),
        errorMessage: "Error fetching gestor with ID " + gestorID
    })).data
}