import { useAuth } from "@/hooks/useAuth";
import { fetchWithHandling } from "../handlers/fetchHandler";
import { mapToDimensionWithEvidences } from "@/mappers";

/**
 * Servicio para manejar las evidencias
 */

const API_URL = `${import.meta.env.VITE_API_URL}/evidences`;

/**
 * método para subir evidencias
 * @param processId id del proceso
 * @param dimensionId id de la dimensión
 * @param evidenceFormData formulario con la evidencia como archivo
 * @returns 
 */
export const uploadEvidence = async (processId: number, dimensionId: number, evidenceFormData: FormData) => {
    const { getToken } = useAuth();
    return await fetchWithHandling(`${API_URL}/${processId}/${dimensionId}/upload`, {
        method: 'POST',
        errorMessage: `Error uploading evidence for process: ${processId} and dimension: ${dimensionId}`,
        token: getToken(),
        body: evidenceFormData,
    });
}

export const appealEvidence = async (rejectedEvidenceId: number, evidenceFormData: FormData) => {
    const { getToken } = useAuth();
    return await fetchWithHandling(`${API_URL}/appeal/${rejectedEvidenceId}/upload`, {
        method: 'POST',
        errorMessage: `Error uploading appealed evidence with id: ${rejectedEvidenceId}`,
        token: getToken(),
        body: evidenceFormData,
    });
}

/**
 * método para eliminar evidencias
 * @param evidenceId id de la evidencia a eliminar
 * @returns 
 */
export const deleteEvidence = async (evidenceId: number) => {
    const { getToken } = useAuth();
    return await fetchWithHandling(`${API_URL}/${evidenceId}`, {
        method: 'DELETE',
        errorMessage: `Error deleting evidence: ${evidenceId}`,
        token: getToken(),
    });
}

/**
 * método para obtener evidencias con dimensiones por id de proceso
 * @param processId id del proceso
 * @returns 
 */
export const fetchEvidencesWithDimensionsByProcessID = async (processId: number) => {
    const { getToken } = useAuth();
    const response = (await fetchWithHandling(`${API_URL}/by-process/${processId}`, {
        errorMessage: `Error fetching evidences for process: ${processId}`,
        token: getToken(),
    })).data;

    return response.map((item: any) => mapToDimensionWithEvidences(item.dimension, item.evidence));
}

export const fetchRejectedEvidencesByProcessID = async (processId: number) => {
    const { getToken } = useAuth();
    
    const apiResponse = await fetchWithHandling(`${API_URL}/by-process/${processId}/rejected`, {
        errorMessage: `Error fetching rejected evidences for process: ${processId}`,
        token: getToken(),
    });
    const response = apiResponse.data;
    
    if (!response || typeof response !== 'object') {
        return [];
    }
    
    const transformedData = Object.entries(response).map(([dimensionName, data]: [string, any]) => {
        const { dimension, evidence } = data;
        const evidenceRequirements = Array.isArray(dimension.evidenceRequirements) ? dimension.evidenceRequirements : [];
    
        return {
            dimension: {
                id: dimension.id,
                name: dimension.name,
                description: dimension.description,
                evidenceRequirements,
                levels: null
            },
            evidence: Array.isArray(evidence) 
                ? evidence
                    .filter((evidenceItem: any) => {
                        if (!evidenceItem || !evidenceItem.evidenceStatus) {
                            return false;
                        }
                        return evidenceItem.evidenceStatus === "REJECTED";
                    })
                    .map((evidenceItem: any) => {
                        
                        const fileName = evidenceItem.fileName?.trim().toLowerCase(); 
                        if (!fileName) {
                            console.warn("[WARNING]: Evidence item sin título", evidenceItem);
                            return {
                                id: evidenceItem.id || "Desconocido",
                                title: "Título no disponible",
                                type: evidenceItem.fileType?.includes("pdf") ? "PDF" : evidenceItem.fileType?.toUpperCase() || "UNKNOWN",
                                uploaded: true,
                                evidenceStatus: evidenceItem.evidenceStatus,
                                comment: evidenceItem.comment ?? "Sin comentarios",
                                evidenceRequirements: "Título no disponible - no se pudo asociar requerimiento."
                            };
                        }
                        
                        const matchingRequirement = evidenceRequirements.find((req: any) => 
                            req.title?.trim().toLowerCase() === fileName
                        );
                        return {
                            id: evidenceItem.id,
                            title: matchingRequirement?.title ?? fileName, 
                            type: evidenceItem.fileType?.includes("pdf") ? "PDF" : evidenceItem.fileType?.toUpperCase() || "UNKNOWN",
                            uploaded: true,
                            evidenceStatus: evidenceItem.evidenceStatus,
                            comment: evidenceItem.comment ?? "Sin comentarios",
                            evidenceRequirements: matchingRequirement 
                                ? matchingRequirement.requirement 
                                : "Requerimiento no especificado."
                        };
                    })
                : []
        };
    });
    const rejectedDimensions = transformedData
        .filter(dimension => dimension.evidence.length > 0) 
        .map((item: any) => {
            return mapToDimensionWithEvidences(item.dimension, item.evidence);
        });
    return rejectedDimensions;
};




export const fetchAppealedEvidencesByProcessID = async (processId: number) => {
    const { getToken } = useAuth();
    try {
        const response = (await fetchWithHandling(`${API_URL}/by-process/${processId}/appealed`, {
            errorMessage: `Error fetching appealed evidences for process: ${processId}`,
            token: getToken(),
        })).data;
        if (!response || typeof response !== 'object') {
            console.warn("La respuesta no es válida:", response);
            return [];
        }

        const transformedData = Object.entries(response as Record<string, { evidence: any[] }>).map(([dimensionName, evidencesData], index) => {
            const evidencesArray = Array.isArray(evidencesData.evidence) ? evidencesData.evidence : [];
            return {
                dimension: {
                    id: index + 1,
                    name: dimensionName,
                    description: `Proceso de ${dimensionName}`,
                    evidenceRequirements: [],
                    levels: null
                },
                evidence: evidencesArray.map((evidence: any) => ({
                    id: evidence?.id || null,
                    fileName: evidence?.fileName || 'Nombre no disponible',
                    fileType: evidence?.fileType || 'Tipo no disponible',
                    conditions: evidence?.conditions || 'Sin condiciones',
                    comment: evidence?.comment || 'Sin comentarios',
                    evidenceStatus: evidence?.evidenceStatus || 'Estado no disponible',
                }))
            };
        });
        const filteredData = transformedData.filter(dimension => dimension.evidence.length > 0);

        return filteredData.map((item: any) => 
            mapToDimensionWithEvidences(item.dimension, item.evidence)
        );
    } catch (error) {
        console.error("Error en fetchAppealedEvidencesByProcessID:", error);
        return [];
    }
}


/**
 * método para descargar evidencias
 * @param evidenceId id de la evidencia a descargar
 * @returns la evidencia descargada como blob
 */
export const downloadEvidence = async (evidenceId: number) => {
    const { getToken } = useAuth();
    return await fetchWithHandling(`${API_URL}/${evidenceId}`, {
        errorMessage: `Error fetching preview for evidence: ${evidenceId}`,
        token: getToken(),
        responseType: 'blob',
    });
}

/**
 * método para aprobar una evidencia
 * @param evidenceId id de la evidencia a aprobar
 * @param comment comentario para cuando la aprobación lo requiera
 * @returns 
 */
export const approveEvidence = async (evidenceId: number, comment?: string) => {
    const { getToken } = useAuth();
    return await fetchWithHandling(`${API_URL}/${evidenceId}/accept`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        errorMessage: `Error approving evidence: ${evidenceId}`,
        token: getToken(),
        body: comment ? JSON.stringify({ comment }) : JSON.stringify({}),
    });
}

/**
 * método para rechazar una evidencia
 * @param evidenceId id de la evidencia a rechazar
 * @param comment comentario para cuando el rechazo lo requiera
 * @returns 
 */
export const rejectEvidence = async (evidenceId: number, comment?: string) => {
    const { getToken } = useAuth();
    return await fetchWithHandling(`${API_URL}/${evidenceId}/reject`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        errorMessage: `Error rejecting evidence: ${evidenceId}`,
        token: getToken(),
        body: comment ? JSON.stringify({ comment }) : JSON.stringify({}),
    });
}