import { useAuth } from "@/hooks/useAuth";
import { fetchWithHandling } from "../handlers/fetchHandler";
import type { Recommendation } from "@/models/types";
import { mapToColor } from "@/mappers";

const API_URL = `${import.meta.env.VITE_RESULT_API_URL}`;

export const fetchRecommendationsByProcessId = async (processId: number): Promise<Recommendation[]> => {
    const { getToken } = useAuth();
    const result = (await fetchWithHandling(`${API_URL}/recommendations/by-process/${processId}`, {
        errorMessage: `Error fetching recommendations for process: ${processId}`,
        token: getToken()
    })).data as Recommendation[];

    return result.map((r) => ({ ...r, color: mapToColor(r.dimension) }));

}

export const fetchPDFRecommendationsByProcessId = async (processId: number, institutionName?: string): Promise<Blob> => {
    const { getToken } = useAuth();
    const url = `${API_URL}/reports/${processId}/render`;

    const response = await fetch(url, {
        headers: {
            Authorization: `Bearer ${getToken()}`,
            'Content-Type': 'application/pdf', // aunque opcional, ayuda a ser explícitos
        }
    });

    if (!response.ok) {
        throw new Error(`Error fetching PDF report for process: ${processId}`);
    }

    return await response.blob(); // Devuelve el Blob con el contenido PDF
};