import { useAuth } from "@/hooks/useAuth";
import { fetchWithHandling } from "../handlers/fetchHandler";

const API_URL = `${import.meta.env.VITE_USER_API_URL}/notifications`;

interface NotificationResponse {
  notifications: string[];
}

export const fetchNotification = async (userId: number, role: string): Promise<NotificationResponse> => {
  const { getToken } = useAuth();
  const url = `${API_URL}?user=${userId}&role=${role}`;
 
  const responseData = await fetchWithHandling(url, {
    token: getToken(),
    errorMessage: "Error fetching notifications",
  });
  const notifications = responseData.data as NotificationResponse;

  return notifications;
};
