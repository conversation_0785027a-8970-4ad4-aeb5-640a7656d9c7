import dimensions from "@/assets/JsonData/dimensions.json";
import recommendations from '@/assets/JsonData/recommendations.json';
import type { Dimension, ResponsesByDimension } from "@/models/types";
import { fetchWithHandling } from "../handlers/fetchHandler";
import type { DimensionFormSchema } from "@/models/validation/formSchemas";
import { useAuth } from "@/hooks/useAuth";

/**
 * Servicio para manejar las dimensiones
 */


const API_URL = `${import.meta.env.VITE_API_URL}/dimensions`

/**
 * método para crear una dimensión
 * @param dimensionData datos de la dimensión a crear, como `DimensionFormSchema`
 * @returns la dimensión creada
 */
export const createDimension = async (dimensionData: DimensionFormSchema): Promise<Dimension> => {
    const { getToken } = useAuth();
    return (await fetchWithHandling(API_URL, {
        method: 'POST',
        body: JSON.stringify(dimensionData),
        errorMessage: 'Error creating dimension',
        headers: {
            "Content-Type": "application/json",
        },
        token: getToken()
    })).data
}

export const getDimensions = (): Dimension[] => {
    return dimensions as Dimension[];
};

export const getResponsesByDimension = (dimensionID: number, emulate: boolean): ResponsesByDimension[] => {
    return dimensions.map((dimension) => {
        let responsePercentage = 0;
        if (emulate) {
            const array = new Uint8Array(1);
            crypto.getRandomValues(array);
            responsePercentage = Math.floor((array[0] / 256) * 100);
        }

        return {
            dimension,
            responsePercentage
        } as ResponsesByDimension;
    });
}

export const getDimensionsWithRecommendations = () => {
    return recommendations.map(recommendation => {
        const dimension = dimensions.find(dimension => dimension.id === recommendation.dimensionID);
        return { ...recommendation, dimension };
    });
}

export const getDimensionByName = (name: string) => {
    return dimensions.find((dimension) => dimension.name === name);
}