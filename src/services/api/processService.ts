import { ProcessStatus, type Dimension, type PaginationResponse, type Process } from '@/models/types/index';
import { fetchWithHandling } from '../handlers/fetchHandler';
import { useAuth } from '@/hooks/useAuth';
import { buildQueryParams } from './helpers/apiHelpers';
import { mapToDimension } from '@/mappers';

/**
 * Servicio para manejar las mutaciones de los procesos
 */

const API_URL = `${import.meta.env.VITE_API_URL}/processes`;
const API_MANUALS = `${import.meta.env.VITE_API_URL}/manuals`;
/**
 * método para obtener todos los procesos por estado
 * @param status estado a filtrar
 * @returns los procesos con el estado especificado
 */
export const fetchProcessesByStatus = async (status: ProcessStatus, page: number = 0, name: string = ""): Promise<PaginationResponse<Process>> => {
    const { getToken } = useAuth();
    const queryParams = buildQueryParams({ page, name });
    const responseData = await fetchWithHandling(`${API_URL}/by-status/${status}?${queryParams}`, {
        errorMessage: `Error fetching processes by status: ${status}`,
        token: getToken()
    });



    const processes = responseData.data as Process[];

    processes.forEach(process => {
        process.employees = convertToEmployeeList(process.employeesNames, process.employeesEmails, process.employeesRut);
    });

    return {
        data: processes,
        totalPages: responseData.totalPages
    };
}

/**
 * método para obtener todos los procesos por id de auditor
 * @param auditorId id del auditor
 * @param page página a obtener
 * @returns los procesos con el id de auditor especificado
 */
export const fetchProcessesByAuditorId = async (auditorId: number, page: number = 0, name: string = ""): Promise<PaginationResponse<Process>> => {
    const { getToken } = useAuth();
    const queryParams = buildQueryParams({ page, name });
    const responseData = await fetchWithHandling(`${API_URL}/by-auditor/${auditorId}?${queryParams}`, {
        errorMessage: `Error fetching processes by auditor ID: ${auditorId}`,
        token: getToken()
    });

    const processes = responseData.data as Process[];

    processes.forEach(process => {
        process.employees = convertToEmployeeList(process.employeesNames, process.employeesEmails, process.employeesRut);
    });

    return {
        data: responseData.data as Process[],
        totalPages: responseData.totalPages
    };
}


export const fetchAllProcessesByInstitutionID = async (institutionID: number, page: number = 0): Promise<PaginationResponse<Process>> => {
    const { getToken } = useAuth();
    const responseData = await fetchWithHandling(`${API_URL}/by-institution/${institutionID}?page=${page}`, {
        errorMessage: `Error fetching processes by institution with ID ${institutionID}`,
        token: getToken()
    });

    const processes = responseData.data.processes as Process[];

    return {
        data: processes,
        totalPages: responseData.totalPages
    };
}



/**
 * método para obtener el proceso actual de una institución
 * @param institutionID id de la institución
 * @returns el primer proceso de la lista, considerando que es el actual
 */

/*export const fetchActualProcessByInstitutionID = async (institutionID: number): Promise<Process> => {
    const { getToken } = useAuth();

    // TODO: Cambiar a que retorne el proceso actual, cuando la API se actualice
    // actualmente la API no devuelve el proceso actual cuando está en estado APPEALABLE
    // cuando sea así, cambiar a la URL comentada

    // URL: `${API_URL}/by-institution/${institutionID}?current=true`
    const responseData = await fetchWithHandling(`${API_URL}/by-institution/${institutionID}?current=true`, {
        errorMessage: `Error fetching actual process by institution with ID ${institutionID}`,
        token: getToken()
    });

    const actualProcess = responseData.data.currentProcess;

    if (actualProcess) {
        actualProcess.employees = convertToEmployeeList(actualProcess.employeesNames, actualProcess.employeesEmails, actualProcess.employeesRut);
    }

    return actualProcess;
}*/

export const fetchActualProcessByInstitutionID = async (institutionID: number): Promise<Process> => {
    const { getToken } = useAuth();
    const responseData = await fetchWithHandling(`${API_URL}/by-institution/${institutionID}`, {
        errorMessage: `Error fetching processes by institution with ID ${institutionID}`,
        token: getToken()
    });

    const allProcesses = responseData.data?.processes ?? [];

    if (allProcesses.length === 0) {
        throw new Error(`No processes found for institution with ID ${institutionID}`);
    }

    allProcesses.sort((a: any, b: any) => {
        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
    });

    const actualProcess = allProcesses[0];
    if (actualProcess) {
        actualProcess.employees = convertToEmployeeList(
            actualProcess.employeesNames,
            actualProcess.employeesEmails,
            actualProcess.employeesRut
        );
    }

    return actualProcess;
}

/**
 * método para finalizar un proceso con estado AUDIT o APPEAL
 * @param processID id del proceso
 * @param auditorID id del auditor
 * @returns 
 */
export const finishProcess = async (processID: number, auditorID: number): Promise<void> => {
    const { getToken } = useAuth();
    return (await fetchWithHandling(`${API_URL}/${processID}/finish/${auditorID}`, {
        method: 'PATCH',
        errorMessage: `Error finishing process with ID: ${processID}`,
        token: getToken()
    })).data;
}

export const auditProcess = async (processID: number): Promise<void> => {
    const { getToken } = useAuth();
    return (await fetchWithHandling(`${API_URL}/${processID}/audit`, {
        method: 'PATCH',
        errorMessage: `Error auditing process with ID: ${processID}`,
        token: getToken()
    })).data;
}

/**
 * método para obtener todas las solicitudes de procesos
 * @returns todas las solicitudes de procesos (admin)
 */
export const fetchAllProcessesRequests = async (): Promise<Process[]> => {
    const { getToken } = useAuth();
    const responseData = await fetchWithHandling(`${API_URL}/requests`, {
        errorMessage: "Error fetching processes requests",
        token: getToken()
    })
    return responseData.data as Process[];
}

/**
 * método para obtener un proceso por id
 * @param id id del proceso
 * @returns el proceso con el id especificado
 */
export const fetchProcessByID = async (id: number): Promise<Process> => {
    const { getToken } = useAuth();
    return (await fetchWithHandling(`${API_URL}/${id}`, {
        errorMessage: `Error fetching process by ID: ${id}`,
        token: getToken()
    })).data as Process;
}


/**
 * método para modificar el estado de un proceso
 * @param id id del proceso
 * @param status nuevo estado del proceso
 * @returns void
 */
export const modifyProcessStatus = async (id: number, status: ProcessStatus): Promise<void> => {
    const { getToken } = useAuth();
    return (await fetchWithHandling(`${API_URL}/${id}`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status }),
        errorMessage: `Error modifying process status with ID: ${id}`,
        token: getToken()
    })).data;
}

/**
 * método para solicitar un proceso
 * @param institutionId id de la institución
 * @param payload payload con los datos del proceso
 * @returns el proceso solicitado
 */
export const requestProcess = async (
  institutionId: number,
  payload: any
): Promise<Process> => {
  const { getToken } = useAuth()

  return (
    await fetchWithHandling(`${API_URL}/${institutionId}/request-process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${getToken()}`
      },
      body: JSON.stringify(payload),
      errorMessage: 'Error requesting process'
    })
  ).data.process
}



/**
 * método para aceptar un proceso (pasa del estado UNINITIATED a IN_PROGRESS)
 * @param processId id del proceso
 * @returns 
 */
export const acceptProcess = async (processId: number): Promise<Process> => {
    const { getToken } = useAuth();
    const responseData = await fetchWithHandling(`${API_URL}/requests/${processId}/accept`, {
        method: 'POST',
        errorMessage: "Error accepting process with ID: " + processId,
        token: getToken()
    });
    return responseData.data;

};

/**
 * método para asignar un modelo de madurez a un proceso
 * @param processId id del proceso
 * @param maturityModelId id del modelo de madurez a asignar
 * @returns 
 */
export const assignMaturityModelToProcess = async (processId: number, maturityModelId: number): Promise<void> => {
    const { getToken } = useAuth();
    const responseData = await fetchWithHandling(`${API_URL}/${processId}/assign-model/${maturityModelId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        errorMessage: "Error assigning maturity model to process with ID: " + processId,
        token: getToken()
    });
    return responseData.data;
}

/**
 * método para rechazar un proceso
 * @param processId id del proceso
 * @returns 
 */
export const rejectProcess = async (processId: number): Promise<void> => {
    const { getToken } = useAuth();
    const response = await fetchWithHandling(`${API_URL}/requests/${processId}/reject`, {
        method: 'POST',
        errorMessage: "Error rejecting process with ID: " + processId,
        token: getToken()
    });
    return response.data;

};

/**
 * Método para descargar el formato de plantilla de empleados
 * @returns Blob con el archivo descargado
 */
export const downloadFormatEmployee = async () => {
    const { getToken } = useAuth();
    return await fetchWithHandling(`${API_MANUALS}/employee-template`, {
        errorMessage: `Error fetching format employee`,
        token: getToken(),
        responseType: 'blob',
    });
}

export const downloadManual = async (url: string) => {
    return await fetchWithHandling(`${API_MANUALS}/${url}`, {
        errorMessage: `Error fetching manual ${url}`,
        responseType: 'blob',
    });
}

/**
 * Método para obtener las dimensiones asociadas a un proceso
 * @param processID ID del proceso
 * @returns Lista de dimensiones asociadas al proceso
 */
export const fetchDimensionsByProcessID = async (processID: number): Promise<Dimension[]> => {
    const { getToken } = useAuth();
    const responseData = await fetchWithHandling(`${API_URL}/${processID}/dimensions`, {
        errorMessage: `Error fetching dimensions by process ID: ${processID}`,
        token: getToken()
    });

    const dimensions = responseData.data;

    return (dimensions as Dimension[]).map((dimension: any) => mapToDimension(dimension));
}
/**
 * Método para convertir strings de nombres, correos y RUTs en una lista de empleados
 * @param namesString String con nombres separados por coma
 * @param emailsString String con correos separados por coma
 * @param rutsString String con RUTs separados por coma
 * @returns Arreglo de objetos con nombre, correo y RUT de cada empleado
 */

export const convertToEmployeeList = (
    namesString: string,
    emailsString: string,
    rutsString: string
): { name: string, email: string, rut: string }[] => {
    const namesArray = namesString ? namesString.split(',').map(name => name.trim()) : [];
    const emailsArray = emailsString ? emailsString.split(',').map(email => email.trim()) : [];
    const rutsArray = rutsString ? rutsString.split(',').map(rut => rut.trim()) : [];

    const employeeList = namesArray.map((name, index) => ({
        name,
        email: emailsArray[index] || '',
        rut: rutsArray[index] || ''
    }));

    return employeeList;
}
