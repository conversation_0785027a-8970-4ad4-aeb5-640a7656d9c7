import { useAuth } from "@/hooks/useAuth";
import { fetchWithHandling } from "../handlers/fetchHandler";
import type { User } from "@/models/types";

const API_URL = `${import.meta.env.VITE_USER_API_URL}`;

export const fetchProfileUser = async (userId: number, role: string): Promise<User> => {
  const { getToken } = useAuth();
  const url = `${API_URL}/${role}s/${userId}`;
 
  const responseData = await fetchWithHandling(url, {
    token: getToken(),
    errorMessage: "Error fetching users",
  });
  const userProfile = responseData.data as User;

  return userProfile;
};
