import type { MaturityModel } from "@/models/types/maturityModel";
import { fetchWithHandling } from "../handlers/fetchHandler";
import { useAuth } from "@/hooks/useAuth";
import type { MaturityModelFormSchema } from "@/models/validation/formSchemas";
import type { PaginationResponse } from "@/models/types";

/**
 * Servicio para manejar las mutaciones de los modelos de madurez
 */

const API_URL = `${import.meta.env.VITE_API_URL}/maturity-models`

/**
 * método para obtener todos los modelos de madurez
 * @returns todos los modelos de madurez
 */
export const fetchAllMaturityModels = async (page: number = 0): Promise<PaginationResponse<MaturityModel>> => {
    const { getToken } = useAuth();
    const responseData = await fetchWithHandling(`${API_URL}?page=${page}`, {
        errorMessage: "Error fetching maturity models",
        token: getToken(),
    })

    return {
        data: responseData.data.maturityModels as MaturityModel[],
        totalPages: responseData.data.totalPages as number
    };
}

/**
 * método para crear un modelo de madurez
 * @param maturityModel datos del modelo a crear, como `MaturityModelFormSchema`
 * @returns el modelo creado
 */
export const createMaturityModel = async (maturityModel: MaturityModelFormSchema, modelFile: File): Promise<MaturityModel> => {
    const { getToken } = useAuth();
    const formData = new FormData();

    formData.append('name', maturityModel.name);
    formData.append('description', maturityModel.description);
    formData.append('timeLimit', maturityModel.timeLimit.toString());
    formData.append('file', modelFile);

    return (await fetchWithHandling(API_URL, {
        method: "POST",
        body: formData,
        errorMessage: "Error creating maturity model",
        token: getToken(),
    })).data as MaturityModel;
}

/**
 * método para asignar una dimensión a un modelo de madurez
 * @param maturityModelId id del modelo de madurez
 * @param dimensionId id de la dimensión
 * @returns 
 */
export const assignDimensionToMaturityModel = async (maturityModelId: number, dimensionId: number) => {
    const { getToken } = useAuth();
    return await fetchWithHandling(`${API_URL}/${maturityModelId}/assign/${dimensionId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        errorMessage: `Error assigning dimension with ID ${dimensionId} to maturity model with ID : ${maturityModelId}`,
        token: getToken()
    });
}
