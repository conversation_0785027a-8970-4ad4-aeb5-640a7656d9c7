import type { Institution, PaginationResponse, InstitutionRequest, LevelPercentage, DashboardInstitution, ResponseTrending } from "@/models/types/index";
import type { InstitutionFormSchema, InstitutionRequestFormSchema } from "@/models/validation/formSchemas";
import { fetchWithHandling } from "../handlers/fetchHandler";
import { useAuth } from "@/hooks/useAuth";
import { buildQueryParams } from "./helpers/apiHelpers";

/**
 * Servicio para manejar las mutaciones de las instituciones
 */

const API_URL = `${import.meta.env.VITE_API_URL}/institutions`
const API_USER = `${import.meta.env.VITE_USER_API_URL}/admins/dashboard-stats`
const API_DASHBOARD = `${import.meta.env.VITE_RESULT_API_URL}/results/institution`
/**
 * método para obtener todas las instituciones
 * @param page página a obtener (paginación)
 * @returns un PaginationResponse, con las instituciones y el total de páginas
 */
export const fetchAllInstitutions = async (page: number = 0, name: string = ""): Promise<PaginationResponse<Institution>> => {
    const { getToken } = useAuth();
    const queryParams = buildQueryParams({ page, name });

    const responseData = await fetchWithHandling(`${API_URL}?${queryParams}`, {
        token: getToken(),
        errorMessage: "Error fetching institutions",
    });
    const institutions = responseData.data.institutions as Institution[];

    institutions.forEach(institution => {
        institution.processes = [];
    });

    return {
        data: institutions,
        totalPages: responseData.data.totalPages,
    };
};

/**
 * método para obtener las 3 instituciones con mejor nivel, junto a sus dimensiones
 * @returns las 3 instituciones con mejor nivel
 */
export const fetchTop3Institutions = async (): Promise<Institution[]> => {
    const { getToken } = useAuth();
    const responseData = await fetchWithHandling(`${API_USER}`, {
        token: getToken(),
        errorMessage: "Error fetching top 3 institutions",
    });

    const institutions = responseData.data.institutions.institutions as Institution[];
    const topInstitutions = institutions.slice(0, 3);

    const dimensionsNames = [
        "Institucionalización", "Adquisición de bienes", "Adquisición de servicios", "Contratación de personal", "Rendición de cuentas", "Comunicación"];

    topInstitutions.forEach((institution) => {
        institution.dimensions = [];
        for (let i = 0; i < dimensionsNames.length; i++) {
            const dimension = {
                name: dimensionsNames[i],
                level: parseFloat((Math.random() * 4 + 1).toFixed(1)),
                description: "",
                evidences: []
            };
            institution.dimensions.push(dimension);
        }
    });

    return topInstitutions;
}

/**
 * método para obtener las distribuciones de nivel de las instituciones
 * @returns la distribución de nivel de las instituciones
 */
export const fetchInstitutionsByLevel = async (level: number): Promise<Institution[]> => {
    const { getToken } = useAuth();
    const responseData = await fetchWithHandling(`${API_URL}?level=${level}`, {
        token: getToken(),
        errorMessage: "Error fetching institutions by level",
    });

    return responseData.data.institutions as Institution[];
};

/**
 * método para obtener las distribuciones de nivel de las instituciones
 * @returns la distribución de nivel de las instituciones
 */
export const fetchInstitutionLevelDistribution = async (): Promise<LevelPercentage[]> => {
    const { getToken } = useAuth();
    const responseData = await fetchWithHandling(`${API_URL}?totalPercentage=true`, {
        token: getToken(),
        errorMessage: "Error fetching institutions level distribution",
    });
    const levelPercentages = responseData.data.institutions as LevelPercentage[];
    levelPercentages.forEach(level => {
        level.percentage = parseInt(level.percentage.toString());
    });
    return levelPercentages;
};


/**
 * método para obtener una institución por su id
 * @param id id de la institución
 * @returns la institución con el id especificado
 */
export const fetchInstitutionByID = async (id: number): Promise<Institution> => {
    const { getToken } = useAuth();
    return (await fetchWithHandling(`${API_URL}/${id}`, {
        token: getToken(),
        errorMessage: `Error fetching institution with ID ${id}`,
    })).data;
   
}

/**
 * método para obtener una institución por su gestor (id del gestor)
 * @param managerId id del gestor 
 * @returns la institución del gestor, o undefined si no tiene
 */
export const fetchInstitutionByManagerID = async (managerId: number): Promise<Institution | undefined> => {
    const institutions = await fetchAllInstitutions();
    return institutions.data.find(institution => institution.manager?.id === managerId);
}

/**
 * método para crear una institución
 * @param institutionData datos de la institución a crear, como un objeto InstitutionFormSchema
 * @returns la institución creada
 */
export const createInstitution = async (institutionData: InstitutionFormSchema): Promise<Institution> => {
    const { getToken } = useAuth();
    const body = JSON.stringify({ ...institutionData.baseInfo, ...institutionData.legalInfo, nemployees: 0 });
    return (await fetchWithHandling(`${API_URL}`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: body,
        errorMessage: "Error creating institution",
        token: getToken(),
    })).data;
};

/**
 * método para actualizar una institución
 * @param institutionId id de la institución a actualizar
 * @param institutionData datos de la institución a actualizar, como un objeto Institution
 * @returns la institución actualizada
 */
export const updateInstitution = async (institutionId: number, institutionData: Institution): Promise<Institution> => {
    const { getToken } = useAuth();
    return await fetchWithHandling(`${API_URL}/${institutionId}`, {
        method: "PATCH",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(institutionData),
        errorMessage: `Error updating institution with ID ${institutionId}`,
        token: getToken(),
    });
};

/**
 * método para habilitar/deshabilitar una institución (cambiar el estado `isActive`)
 * @param institutionId id de la institución a habilitar/deshabilitar
 */
export const toggleInstitutionStatus = async (institutionId: number): Promise<any> => {
    const { getToken } = useAuth();
    return await fetchWithHandling(`${API_URL}/${institutionId}/status`, {
        method: "PATCH",
        headers: {
            "Content-Type": "application/json",
        },
        errorMessage: `Error toggling institution status with ID ${institutionId}`,
        token: getToken(),
    });
};

/**
 * método para asignar un gestor a una institución
 * @param institutionId id de la institución a asignar el gestor
 * @param managerId id del gestor a asignar
 */
export const assignManagerToInstitution = async (institutionId: number, managerId: number) => {
    const { getToken } = useAuth();
    return await fetchWithHandling(`${API_URL}/${institutionId}/assign-manager/${managerId}`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        errorMessage: `Error assigning manager with ID ${managerId} to institution with ID ${institutionId}`,
        token: getToken(),
    });

};

/**
 * método para asignar un auditor a una institución
 * @param institutionId id de la institución a asignar el auditor
 * @param auditorId id del auditor a asignar
 */
export const assignAuditorToInstitution = async (institutionId: number, auditorId: number) => {
    const { getToken } = useAuth();
    return await fetchWithHandling(`${API_URL}/${institutionId}/assign-auditor/${auditorId}`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        errorMessage: `Error assigning auditor with ID ${auditorId} to institution with ID ${institutionId}`,
        token: getToken(),
    });
};


/**
 * método para obtener los registros de ingreso de instituciones al sistema
 * @param page página a obtener (paginación)
 * @returns lista de InstitutionRequest
 */
export const fetchAllInstitutionsRegisterRequests = async (page: number = 0): Promise<InstitutionRequest[]> => {
    const { getToken } = useAuth();
    return (await fetchWithHandling(`${API_URL}/requests?page=${page}`, {
        method: "GET",
        errorMessage: "Error fetching institutions register requests",
        token: getToken(),
    })).data;
};

/**
 * método para solicitar el registro de una institución
 * @param institutionData datos de la institución, como `InstitutionRequestFormSchema`
 * @returns 
 */
export const requestInstitutionRegister = async (institutionData: InstitutionRequestFormSchema): Promise<InstitutionRequest> => {
    const body = JSON.stringify(institutionData);
    return (await fetchWithHandling(`${API_URL}/requests`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: body,
        errorMessage: "Error requesting institution register",
    })).data;
}

/**
 * método para marcar una solicitud de registro de institución como leída
 * @param requestId id de la solicitud
 * @returns 
 */
export const markInstitutionRequestAsRead = async (requestId: number) => {
    const { getToken } = useAuth();
    return await fetchWithHandling(`${API_URL}/requests/${requestId}`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
        },
        errorMessage: `Error marking request with ID ${requestId} as read`,
        token: getToken(),
    });
}


export const fetchDashboard = async (requestId: number): Promise<DashboardInstitution> => {
    const { getToken } = useAuth();
    const responseData = await fetchWithHandling(`${API_DASHBOARD}/${requestId}/dashboard`, {
        token: getToken(),
        errorMessage: "Error fetching institutions level distribution",
    });
    const dashboardInstitution = responseData.data as DashboardInstitution;

    return dashboardInstitution;
}

export const fetchTrending = async (requestId: number): Promise<ResponseTrending> => {
    const { getToken } = useAuth();
    const responseData = await fetchWithHandling(`${API_DASHBOARD}/${requestId}/trending`, {
        token: getToken(),
        errorMessage: "Error fetching institutions level distribution",
    });
    const trendingData = responseData.data as ResponseTrending;

    return trendingData;
}
