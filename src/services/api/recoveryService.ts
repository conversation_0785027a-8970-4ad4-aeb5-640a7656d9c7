// services/recoveryService.ts

const API_BASE = `${import.meta.env.VITE_USER_API_URL}/recovery`;

export const recoveryService = {
  sendRecoveryCode: async (email: string) => {
    const response = await fetch(`${API_BASE}/code`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email }),
    });

    if (!response.ok) throw new Error('Error enviando código de recuperación');
    return response.json();
  },

  validateRecoveryCode: async (email: string, recoveryCode: string) => {
    const response = await fetch(`${API_BASE}/validate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, recoveryCode }),
    });

    if (!response.ok) throw new Error('Código de recuperación inválido');
    return response.json();
  },

  updatePassword: async (email: string, password: string, validationPassword: string) => {
    const response = await fetch(`${API_BASE}/password`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password, validationPassword }),
    });

    if (!response.ok) throw new Error('Error actualizando contraseña');
    return response.json();
  },
};
