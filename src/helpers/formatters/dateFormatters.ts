/**
 * método para formatear una fecha formato chileno
 * @param dateString string con la fecha a formatear
 * @param separator string con el separador a utilizar
 * @returns la fecha formateada en formato `DD-MM-YYYY` (separator = '-')
 */
export const formatDateForChile = (dateString: string | null, separator: string = '-'): string => {
    const date = dateString ? new Date(dateString) : new Date();

    const year = date.getUTCFullYear();
    const month = (`0${date.getUTCMonth() + 1}`).slice(-2);
    const day = (`0${date.getUTCDate()}`).slice(-2);

    return `${day}${separator}${month}${separator}${year}`;
}

/**
 * método para formatear una fecha a string
 * @param date como `Date`
 * @returns la fecha formateada en formato `YYYY-MM-DD`
 */
export const formatDateToString = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
/**
 * método para formatear una fecha a ISO
 * @param dateString string con la fecha a formatear
 * @returns la fecha formateada en formato ISO
 */
export const formatDateToISO = (dateString: string): string => {
    const date = new Date(`${dateString}T00:00:00Z`);
    return date.toISOString();
};

export const formatDateToDDMMYYYY = (dateInput: string | Date): string => {
    const date = typeof dateInput === 'string' ? convertUTCToLocalNaiveDate(dateInput): dateInput;
  
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
  
    return `${day}-${month}-${year}`;
  };

export const formatDateToYYYYMMDD = (dateInput: string | Date): string => {
  const date = typeof dateInput === 'string' ? convertUTCToLocalNaiveDate(dateInput) : dateInput

  const day = String(date.getDate()).padStart(2, '0')
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const year = date.getFullYear()

  return `${year}-${month}-${day}`
};

  /**
   * Converts a UTC date string to a local naive `Date` object.
   * 
   * This function takes a UTC date string and creates a new `Date` object
   * using the UTC components (year, month, day, hours, minutes, seconds, milliseconds).
   * The resulting `Date` object will not account for the local timezone offset,
   * effectively treating the UTC components as if they were local time.
   * 
   * @param utcDateString - The UTC date string to be converted. If `null` or `undefined`,
   *                        the function returns the current date and time.
   * @returns A `Date` object representing the naive local date and time.
   */
  export const convertUTCToLocalNaiveDate = (utcDateString: string | null | undefined) => {
    if (!utcDateString) return new Date();
    
    const tempDate = new Date(utcDateString); 

    const year = tempDate.getUTCFullYear();
    const month = tempDate.getUTCMonth();
    const day = tempDate.getUTCDate();
    const hours = tempDate.getUTCHours();
    const minutes = tempDate.getUTCMinutes();
    const seconds = tempDate.getUTCSeconds();
    const milliseconds = tempDate.getUTCMilliseconds();

    return new Date(year, month, day, hours, minutes, seconds, milliseconds);
  };
