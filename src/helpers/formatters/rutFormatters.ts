
/**
 * método para formatear un RUT a formato estándar (XX.XXX.XXX-X)
 * @param rut RUT a formatear
 * @returns el RUT formateado
 */
export const formatRUT = (rut: string): string => {
    rut = rut.replace(/[^0-9Kk]/g, "").toUpperCase();
    rut = rut.slice(0, 9);

    if (rut.length < 2) {
        return rut;
    }

    const rutBody = rut.slice(0, -1);
    const dv = rut.slice(-1);

    let formattedRutBody = '';
    let counter = 0;

    for (let i = rutBody.length - 1; i >= 0; i--) {
        formattedRutBody = rutBody.charAt(i) + formattedRutBody;
        counter++;
        if (counter === 3 && i !== 0) {
            formattedRutBody = '.' + formattedRutBody;
            counter = 0;
        }
    }

    return `${formattedRutBody}-${dv}`;
};


/**
 * método para formatear un RUT a formato plano (XXXXXXXX-X)
 * @param rut RUT a formatear
 * @returns el RUT formateado
 */
export const formatRutToFlat = (rut: string) => {
    const cleanedRut = rut.replace(/[^0-9K]/g, '');
    const rutLength = cleanedRut.length;
    if (rutLength <= 1) return cleanedRut;

    const rutWithoutLastDigit = cleanedRut.slice(0, -1);
    const lastDigit = cleanedRut.slice(-1);

    return `${rutWithoutLastDigit}-${lastDigit}`;

}

/**
 * método para formatear un rut al formato entero (sin puntos ni guión)
 * @param rut el rut a formatear
 * @returns el rut formateado
 */
export const formatRutToInteger = (rut: string): number => {
    return parseInt(rut.replace(/[^\d]/g, ''), 10);
};
