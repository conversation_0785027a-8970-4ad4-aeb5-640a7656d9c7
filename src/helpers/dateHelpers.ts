/**
 * método para parsear un string a fecha
 * @param dateStr string con la fecha a parsear
 * @returns la fecha parseada
 */
export const parseStringToDate = (dateStr: string): Date => {
    const [year, month, day] = dateStr.split('-').map(Number);
    const date = new Date();
    date.setFullYear(year);
    date.setMonth(month - 1);
    date.setDate(day);
    date.setHours(0, 0, 0, 0); // fuerza medianoche local
    return date;
  };

/**
 * método para sumar meses a una fecha
 * @param date fecha a la que se le sumarán los meses
 * @param months meses a sumar
 * @returns la fecha con los meses sumados
 */
export const addMonthsToDate = (date: Date, months: number): Date => {
    const newDate = new Date(date);
    newDate.setMonth(date.getMonth() + months);
    return newDate;
}

/**
 * método para sumar días a una fecha
 * @param date fecha a la que se le sumarán los días
 * @param days días a sumar
 * @returns la fecha con los días sumados
 */
export const addDaysToDate = (date: Date, days: number): Date => {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    result.setHours(0, 0, 0, 0);
    return result;
};
