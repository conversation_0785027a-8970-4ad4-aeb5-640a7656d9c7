
/**
 * método para formatear un label que depende de la cantidad (singular o plural)
 * @param count cantidad de elementos
 * @param singular cómo se llama el elemento en singular
 * @param plural cómo se llama el elemento en plural
 * @returns el label formateado
 */
export const formatLabel = (count: number, singular: string, plural: string): string => {
    if (count === null || count === 0) {
        return `Sin ${plural}`;
    }
    return count === 1 ? `${count} ${singular}` : `${count} ${plural}`;
};

/**
 * método para extraer las iniciales de un nombre
 * @param name nombre a extraer las iniciales
 * @returns las iniciales del nombre
 */
export const extractInitials = (name: string): string => {
    return name
        .split(" ")
        .map((word) => word[0].toUpperCase())
        .join("");
}

/**
 * método para extraer las iniciales capitalizadas de un nombre
 * @param name nombre a extraer las iniciales
 * @returns las iniciales capitalizadas del nombre
 */
export const extractCapitalizedInitials = (name: string): string => {
    const words = name.split(" ");
    let initials = "";

    for (const word of words) {
        const firstUpperCaseLetter = word.split('').find(char => char === char.toUpperCase());

        if (firstUpperCaseLetter) {
            initials += firstUpperCaseLetter;
        }
    }

    return initials;
}