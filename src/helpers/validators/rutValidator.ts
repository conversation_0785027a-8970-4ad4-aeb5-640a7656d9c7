export const formatRUT = (rut: string): string => {
  rut = rut.replace(/[^0-9Kk]/g, "").toUpperCase();

  if (rut.length < 2) return rut;

  const rutBody = rut.slice(0, -1);
  const dv = rut.slice(-1);

  let formattedRutBody = '';
  let counter = 0;

  for (let i = rutBody.length - 1; i >= 0; i--) {
    formattedRutBody = rutBody.charAt(i) + formattedRutBody;
    counter++;
    if (counter === 3 && i !== 0) {
      formattedRutBody = '.' + formattedRutBody;
      counter = 0;
    }
  }

  return `${formattedRutBody}-${dv}`;
};

export const validateRutFormat = (rut: string): boolean => {
  const rutRegex = /^\d{1,3}(\.\d{3})*-[0-9K]$/;
  return rutRegex.test(rut);
};

export const calculateCheckDigit = (body: string): string => {
  const number = body.replace(/\./g, "");

  let sum = 0;
  let multiplicador = 2;

  for (let i = number.length - 1; i >= 0; i--) {
    sum += parseInt(number.charAt(i)) * multiplicador;
    multiplicador = multiplicador < 7 ? multiplicador + 1 : 2;
  }

  const modulo11 = 11 - (sum % 11);

  return modulo11 === 11 ? "0" : modulo11 === 10 ? "K" : modulo11.toString();
};

export const validateRUT = (rut: string): boolean => {
  rut = formatRUT(rut);

  if (!validateRutFormat(rut)) {
    return false;
  }

  const [body, dv] = rut.split("-");
  const calculatedDigit = calculateCheckDigit(body);

  return calculatedDigit.toUpperCase() === dv.toUpperCase();
};

export const getRUTValidationDetails = (rut: string): { valid: boolean; message?: string } => {
  if (!rut || rut.trim() === "") {
    return { valid: false, message: "El RUT está vacío." };
  }

  const cleanRut = rut.replace(/[^0-9Kk]/g, "").toUpperCase();
  if (cleanRut.length < 2) {
    return { valid: false, message: "El RUT debe tener al menos un número y un dígito verificador." };
  }

  const formattedRut = formatRUT(cleanRut);

  if (!validateRutFormat(formattedRut)) {
    return { valid: false, message: "El formato del RUT es incorrecto. Debe ser como 12.345.678-9." };
  }

  const [body, dv] = formattedRut.split("-");
  const expectedDV = calculateCheckDigit(body);

  if (expectedDV.toUpperCase() !== dv.toUpperCase()) {
    return {
      valid: false,
      message: `Dígito verificador incorrecto. Se esperaba "${expectedDV}", pero se recibió "${dv}".`
    };
  }

  return { valid: true };
};
