<script setup lang="ts">
import Layout from './components/layout/LayoutComponent.vue';
import { useAuth } from '@/hooks/useAuth';

const { getToken } = useAuth();
const isLoggedIn = getToken() !== undefined;

</script>

<!-- si el usuario está autenticado, se muestra el componente Layout, de lo contrario, se renderiza la ruta sola.  -->
<template>
  <Layout v-if="isLoggedIn">
    <router-view v-slot="{ Component, route }">
      <keep-alive>
        <component :is="Component" :key="route.fullPath" />
      </keep-alive>
    </router-view>
  </Layout>
  <router-view v-else />
</template>
