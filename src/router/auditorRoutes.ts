/**
 * rutas relacionadas con el rol de auditor
 */

export const auditorRoutes = [
        {

        path: '/auditor/SURVEY_FINISHED',
        meta: {
            name: 'Auditar',
            icon: 'fas fa-home',
            requiresAuth: true,
            requiredRole: 'AUDITOR'
        },
        children: [
            {
                path: '',
                component: () => import('@/views/auditor/AuditorProcessesPage.vue'),
            },
            {
                path: ':processID',
                component: () => import('@/views/auditor/AuditorAuditPage.vue'),
            }
        ]
    },
    {
        path: '/auditor/IN_PROGRESS',
        meta: {
            name: 'En proceso',
            icon: 'fas fa-building',
            requiresAuth: true,
            requiredRole: 'AUDITOR'
        },
        children: [
            {
                path: '',
                component: () => import('@/views/auditor/AuditorProcessesPage.vue'),
            },
            {
                path: ':processID',
                component: () => import('@/views/auditor/AuditorInProgressPage.vue')
            }
        ]
    },
    {
        path: '/auditor/UNINITIATED',
        component: () => import('@/views/auditor/AuditorProcessesPage.vue'),
        meta: {
            name: 'Sin iniciar',
            icon: 'fas fa-puzzle-piece',
            requiresAuth: true,
            requiredRole: 'AUDITOR'
        }
    },
    {
        path: '/auditor/FINISHED',
        meta: {
            name: 'Finalizado',
            icon: 'fas fa-puzzle-piece',
            requiresAuth: true,
            requiredRole: 'AUDITOR'
        },
        children: [
            {
                path: '',
                component: () => import('@/views/auditor/AuditorProcessesPage.vue'),
         },
            {
                path: ':processID',
                component: () => import('@/views/auditor/AuditorFinishProcessPage.vue')
            }
        ]
    },
    {
        path: '/auditor/APPEAL',
        meta: {
            name: 'Apelaciones',
            icon: 'fa-solid fa-file-circle-exclamation',
            requiresAuth: true,
            requiredRole: 'AUDITOR'
        },
        children: [
            {
                path: '',
                component: () => import('@/views/auditor/AuditorProcessesPage.vue'),
            },
            {
                path: ':processID',
                component: () => import('@/views/auditor/AuditorAppealPage.vue'),
            }
        ]
    },

]