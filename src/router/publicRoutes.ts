import type { RouteRecordRaw } from "vue-router";

import { useAuth } from '@/hooks/useAuth'
const { authData } = useAuth()

/**
 * rutas de la aplicación que no requieren autenticación
 */

export const publicRoutes: Array<RouteRecordRaw> = [
    {
        path: '/login',
        name: 'Login',
        component: () => import('@/views/public/LoginPage.vue'),
        meta: {
            name: 'Iniciar Sesión',
            requiresAuth: false
        }
    },
    {
        path: '/',
        name: 'LandingPage',
        component: () => import('@/views/public/LandingPage.vue'),
        meta: {
            name: 'Inicio',
            requiresAuth: false
        }
    },
    {
        path: '/register',
        name: 'Register',
        component: () => import('@/views/public/RegisterPage.vue'),
        meta: {
            name: '<PERSON><PERSON>',
            requiresAuth: false
        }
    },
    {
        path: '/recoveryPassword',
        name: 'Recovery',
        component: () => import('@/views/public/RecoveryPasswordPage.vue'),
        meta: {
            name: 'Recuperar',
            requiresAuth: false
        }
    }

];