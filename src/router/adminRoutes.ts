export const adminRoutes = [
    {

        path: '/admin/dashboard',
        component: () => import('@/views/admin/AdminDashboardPage.vue'),
        meta: {
            name: 'Dashboard',
            icon: 'fas fa-home',
            requiresAuth: true,
            requiredRole: 'ADMIN'
        }
    },
    {
        path: '/admin/institutions',
        meta: {
            name: 'Instituciones',
            icon: 'fas fa-building',
            requiresAuth: true,
            requiredRole: 'ADMIN'
        },
        children: [
            {
                path: '',
                component: () => import('@/views/admin/AdminInstitutionsPage.vue'),
            },
            {
                path: ':institutionID',
                component: () => import('@/views/gestor/GestorDashboardPage.vue'),
            }
        ]
    },
    {
        path: '/admin/auditors',
        meta: {
            name: 'Auditores',
            icon: 'fas fa-puzzle-piece',
            requiresAuth: true,
            requiredRole: 'ADMIN'
        },
        children: [
            {
                path: '',
                component: () => import('@/views/admin/AdminAuditorsPage.vue'),
            },
            {
                path: ':auditorID/processes',
                component: () => import('@/views/admin/AdminProcessesPage.vue'),
                meta: {
                    inferedTitle: true
                }
            }
        ]
    },
    {
        path: '/admin/models',
        meta: {
            name: 'Modelos',
            icon: 'fas fa-cubes',
            requiresAuth: true,
            requiredRole: 'ADMIN'
        },
        children: [
            {
                path: '',
                component: () => import('@/views/admin/AdminModelsPage.vue'),
            },
            {
                path: 'new',
                component: () => import('@/views/admin/AdminNewModelPage.vue'),
                meta: {
                    name: 'Nuevo Modelo'
                }
            }
        ]
    },
    {
        path: '/admin/requests',
        component: () => import('@/views/admin/AdminRequestsPage.vue'),
        meta: {
            name: 'Solicitudes',
            icon: 'fas fa-envelope',
            requiresAuth: true,
            requiredRole: 'ADMIN'
        }
    },

]