import { createRouter, createWebHistory } from 'vue-router'
import { gestorRoutes } from './gestorRoutes';
import { auditorRoutes } from './auditorRoutes';
import { adminRoutes } from './adminRoutes';
import { publicRoutes } from './publicRoutes';
import { useAuth } from '@/hooks/useAuth';
import { surveyRoutes } from './surveyRoutes';

export interface Route {
  path: string;
  component?: () => Promise<any>;
  meta: {
    name: string;
    icon?: string;
  };
}

const getRoutesByRole = () => {
  const { authData } = useAuth();
  const role = authData.value?.role;

  const routes = {
    AUDITOR: auditorRoutes,
    MANAGER: gestorRoutes,
    ADMIN: adminRoutes
  } as Record<string, any[]>

  return role ? routes[role] : [];
}

export const routesByRole = getRoutesByRole();
export const routes = [...publicRoutes, getRoutesByRole()];

const router = createRouter({
  history: createWebHistory(),
  routes: [...publicRoutes, ...adminRoutes, ...gestorRoutes, ...auditorRoutes, ...surveyRoutes],

  scrollBehavior(_to, _from, _savedPosition) {
    const layoutId = document.getElementById('main');
    if (layoutId) {
      layoutId.scrollTop = 0;
      layoutId.style.scrollBehavior = 'smooth';
    }
  }
});

router.beforeEach((to, _from, next) => {
  const { authData, getToken, logout } = useAuth();
  const isLoggedIn = getToken() !== undefined;
  const tokenExpired = (authData.value && authData.value.exp <= Math.floor(Date.now() / 1000)) as boolean;

  if (handleTokenExpiration(tokenExpired, logout, next)) return;

  if (to.meta.requiresAuth) {
    handleAuthRequired(to, isLoggedIn, authData.value?.role, next);
  } else {
    handleNoAuthRequired(to, isLoggedIn, next);
  }
});

// manejar la expiración del token
const handleTokenExpiration = (tokenExpired: boolean, logout: () => void, next: (path: string) => void): boolean => {
  if (tokenExpired) {
    logout();
    next('/login');
    return true;
  }
  return false;
}

// manejar si es que se requiere autenticación
const handleAuthRequired = (to: any, isLoggedIn: boolean, userRole: string | undefined, next: (path?: string) => void) => {
  if (!isLoggedIn) {
    next('/login');
  } else if (to.meta.requiredRole && userRole !== to.meta.requiredRole) {
    next(getRoutesByRole()[0].path);
  } else {
    next();
  }
}

// manejar si no se requiere autenticación
const handleNoAuthRequired = (to: any, isLoggedIn: boolean, next: (path?: string) => void) => {
  if (isLoggedIn) {
    next(getRoutesByRole()[0].path);
  } else if (!to.matched.length) {
    next('/login');
  } else {
    next();
  }
}

export default router
