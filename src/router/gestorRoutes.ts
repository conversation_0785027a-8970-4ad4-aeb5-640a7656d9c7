/**
 * rutas relacionadas con el rol de gestor
 */

export const gestorRoutes = [
    {
        path: '/gestor/dashboard',
        component: () => import('@/views/gestor/GestorDashboardPage.vue'),
        meta: {
            name: '<PERSON><PERSON>',
            icon: 'fas fa-home',
            requiresAuth: true,
            requiredRole: 'MANAGER'
        }
    },
    {
        path: '/gestor/requestProcess',
        meta: {
            name: 'Proceso',
            icon: 'fa-solid fa-industry',
            requiresAuth: true,
            requiredRole: 'MANAGER'
        },
        children: [
            {
                path: '',
                component: () => import('@/views/gestor/GestorRequestProcessPage.vue'),
            },
            {
                path: 'new',
                component: () => import('@/views/gestor/GestorNewProcessPage.vue'),
            }
        ]
    },
    {
        path: '/gestor/process',
        meta: {
            name: 'Mi institución',
            icon: 'fa-solid fa-building',
            requiresAuth: true,
            requiredRole: 'MANAGER'
        },
        children: [
            {
                path: '',
                component: () => import('@/views/gestor/GestorActualProcessPage.vue'),
            },
            {
                path: ':processID/evidence',
                component: () => import('@/views/gestor/UploadEvidencePage.vue'),
            },
            {
                path: ':processID/appeal',
                component: () => import('@/views/gestor/GestorAppealPage.vue'),
            },
            {
                path: 'appeal/section',
                component: () => import('@/views/gestor/GestorAppealSectionPage.vue'),
            },
            
        ]
    },
    {
        path: '/gestor/recommendations',
        component: () => import('@/views/gestor/RecommendationsPage.vue'),
        meta: {
            name: 'Sugerencias',
            icon: 'fa-solid fa-comments',
            requiresAuth: true,
            requiredRole: 'MANAGER'
        }
    },
    {
        path: '/gestor/record',
        meta: {
            name: 'Historial',
            icon: 'fa-solid fa-clock-rotate-left',
            requiresAuth: true,
            requiredRole: 'MANAGER',
        },
        children: [
            {
                path: '',
                component: () => import('@/views/gestor/GestorHistoryPage.vue'),
            },
            {
                path: ':processID',
                component: () => import('@/views/gestor/GestorHistoricalProcessPage.vue'),
            },
            {
                path: ':processID/reporter',
                component: () => import('@/views/gestor/GestorReportPage.vue'),
            }

        ]
    }

]