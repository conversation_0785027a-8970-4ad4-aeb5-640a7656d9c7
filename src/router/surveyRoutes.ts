export const surveyRoutes = [
    {
        path: '/survey/:id',
        name: 'Survey',
        component: () => import('@/views/public/survey/SurveyTermsPage.vue'),
        meta: {
            name: 'Terms',
            icon: 'fa-solid fa-industry',
            hideHeader: true,
            hideNavbar: true,
            requiresAuth: false
        },
    },
    {
        path: '/survey/:surveyId/questions',
        name: 'SurveyQuestions',
        component: () => import('@/views/public/survey/SurveyPage.vue'),
        hideNavbar: true,
        props: true,
        meta: {
            name: 'Survey',
            icon: 'fa-solid fa-industry',
            hideNavbar: true,
            hideHeader: true,
            requiresAuth: false
        },
    },
];