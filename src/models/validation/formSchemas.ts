export interface InstitutionRequestFormSchema {
    managerEmail: string;
    institutionName: string;
    phoneNumber: string;
}

export interface InstitutionFormSchema {
    baseInfo?: InstitutionBaseInfoSchema
    legalInfo?: InstitutionLegalInfoFormSchema;
    gestorInfo?: GestorFormSchema;
}

export interface InstitutionBaseInfoSchema {
    name: string;
    address: string;
    city: string;
    region: string;
    whatsapp: string;
    color: string;
    acronym: string;
    email: string;
    password: string | null;
    confirmPassword: string | null;
}

export interface InstitutionLegalInfoFormSchema {
    phoneNumber: string;
    companyName: string;
    website: string;
    companyRut: string;
}

interface UserSchema {
    username: string;
    email: string;
    password?: string;
    confirmPassword?: string;
    phoneNumber: string;
}

export interface GestorFormSchema extends UserSchema {
    position: string;
    rut: string;
}

export interface AuditorFormSchema extends UserSchema {
    city: string;
    acronym: string;
    color: string;
}

export interface MaturityModelFormSchema {
    name: string;
    evaluationContext: string;
    description: string;
    timeLimit: number;
}

export interface DimensionFormSchema {
    name: string;
    description: string;
    jsonQuestions: string;
    evidenceRequirements?: string;
}