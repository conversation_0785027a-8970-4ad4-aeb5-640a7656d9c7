import { computed } from "vue";
import { helpers, required, minLength, email, maxLength } from "@vuelidate/validators";
import { validatePassword, validateRUT } from "@/helpers/validators";

const PASSWORD_RULES = {
    password: {
        required: helpers.withMessage("Requerido", required),
        minLength: helpers.withMessage("Mínimo 8 caracteres", minLength(8)),
        valid: helpers.withMessage("Debe contener una mayúscula, una minúscula, un dígito, un carácter especial y sin espacios.", validatePassword)
    },
    confirmPassword: {
        required: helpers.withMessage("Requerido", required),
        sameAsPassword: helpers.withMessage("Las contraseñas no coinciden", (value, vm) => value === vm.password)
    }

}

export const recoveryPassword_rules = {
    email: {
      required: helpers.withMessage('Este campo es requerido', required),
      email: helpers.withMessage('Debe ser un correo válido', email)
    },
    confirmEmail: {
      required: helpers.withMessage('Este campo es requerido', required),
      sameAsEmail: helpers.withMessage(
        'Los correos no coinciden',
        function (value, vm) {
          return value === vm.email
        }
      )
    }
  }
export const recoveryReset_rules = {
    password: PASSWORD_RULES.password,
    confirmPassword: PASSWORD_RULES.confirmPassword
  };

  export const recoveryCode_rules = {
    code: {
      required: helpers.withMessage('Requerido', required),
      valid: helpers.withMessage(
        'Debe ser un código alfanumérico válido',
        helpers.regex(/^[a-zA-Z0-9]+$/)
      )
    }
  }
  
export const COMMON_RULES = {
    username: {
        required: helpers.withMessage("Requerido", required),
        minLength: helpers.withMessage("Mínimo 3 caracteres", minLength(3)),        
    },
    email: {
        required: helpers.withMessage("Requerido", required),
        email: helpers.withMessage("Email inválido", email)
    },
    city: {
        required: helpers.withMessage("Requerido", required),
        minLength: helpers.withMessage("Mínimo 3 caracteres", minLength(3))
    },
    acronym: {
        required: helpers.withMessage("Requerido", required),
        minLength: helpers.withMessage("Mínimo 1 caracter", minLength(1)),
        maxLength: helpers.withMessage("Máximo 5 caracteres", maxLength(5))
    },
    rut: {
        required: helpers.withMessage("Requerido", required),
        valid: helpers.withMessage("RUT inválido", validateRUT)
    },
    phoneNumber: {
        required: helpers.withMessage("Requerido", required),
        minLength: helpers.withMessage("Mínimo 3 caracteres", minLength(3)),
    },
};


export const createInstitutionBaseRules = (isCreate: boolean) => computed(() => {
    const rules = {
        name: {
            required: helpers.withMessage("Requerido", required),
            minLength: helpers.withMessage("Mínimo 5 caracteres", minLength(5))
        },
        region: {
            required: helpers.withMessage("Requerido", required),
            minLength: helpers.withMessage("Mínimo 5 caracteres", minLength(5))
        },
        city: COMMON_RULES.city,
        address: {
            required: helpers.withMessage("Requerido", required),
            minLength: helpers.withMessage("Mínimo 5 caracteres", minLength(5))
        },
        acronym: COMMON_RULES.acronym,
        whatsapp: {},
        email: COMMON_RULES.email,
    };
    if (isCreate) {
        Object.assign(rules, PASSWORD_RULES);
    }

    return rules;
});


export const createInstitutionLegalRules = computed(() => ({
    website: {
        required: helpers.withMessage("Requerido", required),
        minLength: helpers.withMessage("Mínimo 3 caracteres", minLength(3))
    },
    phoneNumber: {
        required: helpers.withMessage("Requerido", required),
        minLength: helpers.withMessage("Mínimo 3 caracteres", minLength(3))
    },
    companyRut: {
        required: helpers.withMessage("Requerido", required),
        valid: helpers.withMessage("RUT inválido", validateRUT)
    },
    // razón social
    companyName: {
        required: helpers.withMessage("Requerido", required),
        minLength: helpers.withMessage("Mínimo 3 caracteres", minLength(3))
    },
}));

export const createGestorRules = (includePassword: boolean = false) => {
    return computed(() => {
        let rules = {
            username: COMMON_RULES.username,
            email: COMMON_RULES.email,
            position: {
                required: helpers.withMessage("Requerido", required),
                minLength: helpers.withMessage("Mínimo 3 caracteres", minLength(3))
            },
            rut: COMMON_RULES.rut,
            phoneNumber: COMMON_RULES.phoneNumber
        }

        if (includePassword) {
            rules = {
                ...rules,
                ...PASSWORD_RULES
            };
        }

        return rules;
    });
};

export const createAuditorRules = computed(() => ({
    username: COMMON_RULES.username,
    email: COMMON_RULES.email,
    city: COMMON_RULES.city,
    acronym: COMMON_RULES.acronym,
    phoneNumber: COMMON_RULES.phoneNumber,
    ...PASSWORD_RULES
}));

export const updateAuditorRules = computed(() => ({
    username: COMMON_RULES.username,
    email: COMMON_RULES.email,
    city: COMMON_RULES.city,
    acronym: COMMON_RULES.acronym,
    phoneNumber: COMMON_RULES.phoneNumber,
}));

export const addEmployeeRules = computed(() => ({
    name: COMMON_RULES.username,
    email: COMMON_RULES.email,
    rut: COMMON_RULES.rut,
}));

export const createModelRules = computed(() => ({
    name: {
        required: helpers.withMessage("Requerido", required),
        minLength: helpers.withMessage("Mínimo 3 caracteres", minLength(5))
    },
    evaluationContext: {
        required: helpers.withMessage("Requerido", required),
        minLength: helpers.withMessage("Mínimo 5 caracteres", minLength(5))
    },
    description: {
        required: helpers.withMessage("Requerido", required),
        minLength: helpers.withMessage("Mínimo 10 caracteres", minLength(5))
    }
}));

export const institutionRegisterRequestRules = computed(() => ({
    managerEmail: COMMON_RULES.email,
    institutionName: COMMON_RULES.username,
    phoneNumber: COMMON_RULES.phoneNumber,
}));

export const loginRules = computed(() => ({
    username: {
        required: helpers.withMessage("Requerido", required)
    },
    password: {
        required: helpers.withMessage("Requerido", required)
    }
}));