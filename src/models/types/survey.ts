import type { Question } from "./dimension";

export interface Survey {
    id: number;
    isActive: boolean;
    description: string;
    title: string;
    name: string;
    sections: Section[];
}


export interface Section {
    title: string;
    questions: Question[];
}

export interface SurveyQuestion {
    id: number;
    question: string;
    surveyId: number;
    level: number;
    questionNumber: string;
}

export interface SurveyAnswer {
    id: number;
    questionId: number;
    answer: number;
}

export interface CreateSurvey {
    name: string;
    description: string;
    isActive: boolean;
    questions: SurveyQuestion[];
}
