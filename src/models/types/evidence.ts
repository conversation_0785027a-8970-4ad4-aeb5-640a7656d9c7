
export interface Evidence {
    id?: number;
    title: string;
    approvalRequirements: string;
    type: 'image' | 'PDF';
    src?: string;
    file?: File;
    uploaded?: boolean;
    evidenceStatus?: EvidenceStatus;
    comment?: string;
}

export enum EvidenceStatus {
    NOT_EVALUATED = "NOT_EVALUATED",
    ACCEPTED = "ACCEPTED",
    REJECTED = "REJECTED",
    ACCEPTED_WITH_COMMENT = "ACCEPTED_WITH_COMMENT",
    APPEALED = "APPEALED",
    APPEAL = "APPEAL"

}

export interface EvidenceRequirement {
    title: string;
    approvalRequirements: string;
    type: 'image' | 'pdf';

}