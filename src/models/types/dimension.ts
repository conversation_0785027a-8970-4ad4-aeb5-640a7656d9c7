import type { Evidence, EvidenceRequirement } from "./evidence";

export interface Dimension {
    [x: string]: any;
    id?: number;
    color?: string;
    level?: number;
    name: string;
    icon?: string;
    description: string;
    average?: number;
    evidenceRequirements?: EvidenceRequirement[];
    evidences?: Evidence[];
    questions?: Question[];
}


export interface Question {
    id?: number;
    text: string;
    answer?: string[];
}



export interface DimensionRanking extends Dimension {
    levelAverage: number;
}

export interface ResponsesByDimension {
    dimension: Dimension;
    responsePercentage: number;
}
