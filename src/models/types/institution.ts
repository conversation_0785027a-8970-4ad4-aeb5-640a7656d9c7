import type { Dimension } from "./dimension";
import type { Process } from "./process";
import type { Auditor, Gestor } from "./user";

export interface Institution {
    id: number;
    address: string;
    city: string;
    region: string;
    name: string;
    companyName: string;
    email: string;
    website: string;
    companyRut: string;
    phoneNumber: string;
    whatsapp: string;
    color: string;
    acronym: string;
    level: number;
    nemployees: number;
    manager?: Gestor | null;
    auditor?: Auditor | null;
    processes: Process[];
    nprocesses: number;
    dimensions?: Dimension[]
}

export interface SimpleInstitutionDTO {
    id: number;
    name: string;
    acronym: string;
    color: string;
    city: string;
}

export interface CreateInstitution {
    name: string;
    isActive: boolean;
    street: string;
    city: string;
    region: string;
    managerId: number;
    processId: number;
}

export interface Level {
    id: number;
    name: string;
    description: string;
}

export interface LevelPercentage {
    level: number;
    percentage: number;
    ninstitutions: number;
    color?: string;
}

export interface InstitutionRequest {
    id?: number;
    institutionName: string;
    phoneNumber: string;
    managerEmail: string;
    requestStatus: string;
}
export interface DashboardInstitution {
    statistics(statistics: any): unknown;
    processResult(processResult: any): unknown;
    id: number;
    peopleAnswered: number;
    dimensionLevels: number[];
    institutionLevel: number;
    daysPassed: number;
    daysLeft: number;
    answeredSurveys: number;
    surveyQuorum: number;
    totalSurveys: number;
    estimatedCompletionDate: string;
    processStartDate: string;
    processEndDate: string;
    responseAverages: ResponseAverage[];
    estimationPoints: { [key: string]: string };
    process:number,
    SD: number,
    min: number,
    twoSigma: number,
    median: number,
    max:number,
    variance: number,
    mean: number,
    GM: number,
    sum: number,
    populationVariance: number,
    skewness: number,
    kurtosis: number
  }
  
  export interface ResponseAverage {
    completionPercentage: number;
    peopleAnswered: number;
    realAverage: number;
    idealAverage: number;
    date: string;
  }

 interface DimensionLevel {
    id: number;
    name: string;
    description: string | null;
    level: number;
    comment: string | null;
}

export interface ResponseTrending {
    result(result: any): unknown;
    processName: string;
    dimensionLevels: DimensionLevel[];
    institutionLevel: number;
}