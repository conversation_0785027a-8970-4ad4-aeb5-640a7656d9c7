import type { EvidenceStatus } from "./evidence";
import type { Institution } from "./institution";

export interface Step {
    number: number;
    icon: string;
    title: string;
    status?: string;
    buttonText?: string;
    expandedTitle?: string;
    buttonEnabled?: boolean;
    description?: string;
    color: string
}

export interface PaginationResponse<T> {
    data: T[];
    totalPages: number;
}
interface DimensionLevel {
    id: number | null;
    name: string;
    average: number;
}
export interface DashboardKPIStats {
    top: Institution[] | { id: number; address: string; city: string; region: string; name: string; companyName: string; email: string; website: string; companyRut: string; phoneNumber: string; whatsapp: string; color: string; acronym: string; level: number; nemployees: number; manager?: { position: string; rut: string; institution?: any | undefined; id: number; username: string; email: string; userRole: string; phoneNumber?: string | undefined; city: string; nassignedInstitutions: number; nauditsPerformed: number; createdAt: Date; updatedAt: Date; } | null | undefined; auditor?: { nassignedInstitutions: number; nauditsPerformed: number; color: string; city: string; acronym: string; id: number; username: string; email: string; userRole: string; phoneNumber?: string | undefined; createdAt: Date; updatedAt: Date; rut: string; position: string; institution?: any | undefined; } | null | undefined; processes: { id: number; name: string; institutionId: number; startDate: string; endDate: string; surveysCompleted: number; levelAchieved: string; status: string; requestStatus: string; employees: { name: string; email: string; rut: string; }[]; nemployees: number; employeesNames: string; employeesEmails: string; employeesRut: string; milestones: string[]; simpleInstitutionDTO: { id: number; name: string; acronym: string; color: string; city: string; }; }[]; nprocesses: number; dimensions?: { id?: number | undefined; color?: string | undefined; level?: number | undefined; name: string; icon?: string | undefined; description: string; average?: number | undefined; evidenceRequirements?: { title: string; approvalRequirements: string; type: "image" | "pdf"; }[] | undefined; evidences?: { id?: number | undefined; title: string; approvalRequirements: string; type: "image" | "PDF"; src?: string | undefined; file?: { readonly lastModified: number; readonly name: string; readonly webkitRelativePath: string; readonly size: number; readonly type: string; arrayBuffer: () => Promise<ArrayBuffer>; slice: (start?: number, end?: number, contentType?: string) => Blob; stream: () => ReadableStream<Uint8Array>; text: () => Promise<string>; } | undefined; uploaded?: boolean | undefined; evidenceStatus?: EvidenceStatus | undefined; comment?: string | undefined; }[] | undefined; questions?: { id?: number | undefined; text: string; answer?: string[] | undefined; }[] | undefined; }[] | undefined; }[];
    numberOfInstitutions: number;
    numberOfAuditors: number;
    totalCompletedAudits: number;
    processesInProgress: number;
    peopleSurveyed: number;
    allDimensionLevels: DimensionLevel[];
}
export interface KPICard {
    title: string;
    value: string;
    icon?: string;
    color?: string;
}

export interface LoginResponse {
    username: string;
}