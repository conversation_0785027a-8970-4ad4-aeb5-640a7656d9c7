import type { Institution, SimpleInstitutionDTO } from "./institution";

export interface User {
    id: number;
    username: string;
    email: string;
    userRole: string;
    phoneNumber?: string;
    city:string;
    nassignedInstitutions: number;
    nauditsPerformed: number;
    createdAt: Date;
    updatedAt: Date;
    rut: string;
    position: string;
    institution?: Institution;
}

export interface Auditor extends User {
    nassignedInstitutions: number;
    nauditsPerformed: number;
    color: string;
    city: string;
    acronym: string;
}

export interface Gestor extends User {
    position: string;
    rut: string;
    institution?: Institution;
}

export enum UserRole {
    ADMIN = 'Admin',
    MANAGER = 'Gestor',
    AUDITOR = 'Auditor',
}

export interface AuthData {
    id: number;
    token: string;
    username: string;
    role: string;
    exp: number;
    simpleInstitutionDTO?: SimpleInstitutionDTO;
}
