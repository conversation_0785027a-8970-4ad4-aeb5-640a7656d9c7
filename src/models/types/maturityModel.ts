import type { Dimension } from "./dimension";
import type { Process } from "./process";

export type MaturityModel = {
    id?: number;
    isActive?: boolean;
    name: string;
    description: string;
    timeLimit: number;
    modelFileData?: Uint8Array;
    modelFileName?: string;
    modelFileType?: string;
    createdAt?: Date;
    processes?: Process[];
    dimensions?: Dimension[];
    nquestions?: number;
    ndimensiones?: number;
};
