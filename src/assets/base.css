/* color palette from <https://github.com/vuejs/theme> */
:root {
  --vt-c-white: #fff;
  --vt-c-white-soft: #f8f8f8;
  --vt-c-white-mute: #f2f2f2;
  --vt-c-black: #181818;
  --vt-c-black-soft: #222;
  --vt-c-black-mute: #282828;
  --vt-c-indigo: #2c3e50;
  --vt-c-divider-light-1: rgb(60 60 60 / 29%);
  --vt-c-divider-light-2: rgb(60 60 60 / 12%);
  --vt-c-divider-dark-1: rgb(84 84 84 / 65%);
  --vt-c-divider-dark-2: rgb(84 84 84 / 48%);
  --vt-c-text-light-1: var(--vt-c-indigo);
  --vt-c-text-light-2: rgb(60 60 60 / 66%);
  --vt-c-text-dark-1: var(--vt-c-white);
  --vt-c-text-dark-2: rgb(235 235 235 / 64%);
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--vt-c-black);
    --color-background-soft: var(--vt-c-black-soft);
    --color-background-mute: var(--vt-c-black-mute);
    --color-border: var(--vt-c-divider-dark-2);
    --color-border-hover: var(--vt-c-divider-dark-1);
    --color-heading: var(--vt-c-text-dark-1);
    --color-text: var(--vt-c-text-dark-2);
  }
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background);
  transition:
    color 0.5s,
    background-color 0.5s;
  line-height: 1.6;
  font-family: 'Poppins', sans-serif;
  text-rendering: optimizelegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}