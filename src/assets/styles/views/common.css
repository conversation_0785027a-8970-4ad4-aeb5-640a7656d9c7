body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: rgb(242, 242, 242);
  min-height: 100vh;
}

.content {
  display: flex;
  margin-left: 2%;
  margin-right: 2%;
}

.title {
  font-weight: bold;
  font-size: 24px;
}


.subtitle {
  font-size: 1rem;
  color: #666;
}

.card-title {
  font-weight: 600;
  font-size: 0.9rem;
  text-align: center;
}

.card-description {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
}

.subtitle-right {
  margin-bottom: 20px;
  text-align: end;
  font-size: 1rem;
  color: #666;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.card {
  background-color: #ffffff;
  border-radius: 10px;
}

.line-chart-container {
  background-color: white;
}

.line-chart-container .subtitle {
  margin-bottom: 10px;
}

.chart-container {
  background-color: white;
}

/*Estilos para Carrusel*/

.carousel-inner {
  display: flex;
  flex-wrap: nowrap;
  gap: 50px;
  padding: 30px;
}

.carousel-item {
  flex: 0 0 auto;
  width: auto;
}


.left-content {
  flex: 1;
}

.right-content {
  margin-left: -10%;
  flex: 1;
}