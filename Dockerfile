# Usar la imagen base de Node.js
FROM node:21-alpine

# Establecer el directorio de trabajo
WORKDIR /app

# Copiar package.json y package-lock.json
COPY package*.json ./

# Instalar dependencias
RUN npm install

# Copiar el resto del código de la aplicación
COPY . .

# ---------------------------
# Declarar las variables de entorno como ARG
# ---------------------------
ARG VITE_API_URL
ARG VITE_SURVEY_API_URL
ARG VITE_USER_API_URL
ARG VITE_RESULT_API_URL
ARG VITE_EMPLOYEE_EXCEL_COLUMNS
ARG VITE_MODEL_EXCEL_COLUMNS

# Declarar las mismas como ENV para asegurar acceso en npm run build
ENV VITE_API_URL=$VITE_API_URL
ENV VITE_SURVEY_API_URL=$VITE_SURVEY_API_URL
ENV VITE_USER_API_URL=$VITE_USER_API_URL
ENV VITE_RESULT_API_URL=$VITE_RESULT_API_URL
ENV VITE_EMPLOYEE_EXCEL_COLUMNS=$VITE_EMPLOYEE_EXCEL_COLUMNS
ENV VITE_MODEL_EXCEL_COLUMNS=$VITE_MODEL_EXCEL_COLUMNS

# Construir la aplicación para producción
RUN npm run build

# Instalar el paquete serve para servir los archivos estáticos
RUN npm install -g serve

# Comando para ejecutar la aplicación
CMD ["serve", "-s", "dist", "-l", "80"]

# Exponer el puerto 80
EXPOSE 80
