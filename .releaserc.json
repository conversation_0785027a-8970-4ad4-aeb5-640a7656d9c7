{"branches": ["main", {"name": "develop", "prerelease": "dev", "channel": "dev"}, {"name": "pre-prod", "prerelease": "rc", "channel": "rc"}], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", ["@semantic-release/git", {"assets": ["CHANGELOG.md"], "message": "chore(release): ${nextRelease.version} [skip ci]"}], "@semantic-release/github"], "preset": "conventionalcommits"}