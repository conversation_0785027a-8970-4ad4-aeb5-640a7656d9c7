import { describe, it, expect } from 'vitest';
import {
    mapInstitutionToInstitutionFormSchema,
    mapInstitutionFormSchemaToInstitution,
    mapInstitutionToBaseInstitutionInfoSchema,
    mapInstitutionToLegalInfoSchema,
    mapInstitutionToGestorInfoSchema,
    mapRoleToHumanReadable
} from '../../src/mappers';
import type { Institution } from "../../src/models/types/index";
import type { InstitutionFormSchema } from "../../src/models/validation/formSchemas";


/**
 * Tests para las funciones de mapeo
 * 
 * - Debería mapear correctamente una institución a un esquema de formulario
 * - Debería mapear correctamente un esquema de formulario a una institución
 * - Debería mapear correctamente una institución a un esquema de información base
 * - Debería mapear correctamente una institución a un esquema de información legal
 * - Debería mapear correctamente una institución a un esquema de información de gestor
 */

describe('Mapping functions', () => {
    const mockInstitution: Institution = {
        id: 1,
        name: 'Institution Name',
        companyName: 'Company Name',
        level: 1,
        nemployees: 10,
        address: '123 Street',
        city: 'City',
        region: 'Region',
        whatsapp: '1234567890',
        color: 'blue',
        acronym: 'INSTIT',
        email: '<EMAIL>',
        phoneNumber: '0987654321',
        website: 'https://institution.com',
        companyRut: '12345678-9',
        processes: [],
        nprocesses: 0,
        manager: {
            id: 2,
            username: 'gestor',
            email: '<EMAIL>',
            position: 'Manager',
            rut: '98765432-1',
            userRole: 'GESTOR',
            phoneNumber: '+569 12345678',
            city: '',
            nassignedInstitutions: 0,
            nauditsPerformed: 0,
            createdAt: new Date('2025-04-11T14:06:16.105Z'),
            updatedAt: new Date('2025-04-11T14:06:16.105Z'),
        }
    };

    it('debería mapear correctamente una Institution a un InstitutionFormSchema', () => {
        const result = mapInstitutionToInstitutionFormSchema(mockInstitution);
        expect(result).toEqual({
            baseInfo: {
                name: 'Institution Name',
                address: '123 Street',
                city: 'City',
                region: 'Region',
                whatsapp: '1234567890',
                color: 'blue',
                acronym: 'INSTIT',
                email: '<EMAIL>',
                password: '',
                confirmPassword: ''
            },
            legalInfo: {
                phoneNumber: '0987654321',
                companyName: 'Institution Name',
                website: 'https://institution.com',
                companyRut: '12345678-9'
            },
            gestorInfo: {
                username: 'gestor',
                email: '<EMAIL>',
                position: 'Manager',
                rut: '98765432-1',
                phoneNumber: '+569 12345678',
                password: '',
                confirmPassword: ''
            }
        });
    });

    it('debería mapear un InstitutionFormSchema a una Institution correctamente', () => {
        const formSchema: InstitutionFormSchema = {
            baseInfo: {
                name: 'Institution Name',
                address: '123 Street',
                city: 'City',
                region: 'Region',
                whatsapp: '1234567890',
                color: 'blue',
                acronym: 'INSTIT',
                email: '<EMAIL>',
                password: '',
                confirmPassword: ''
            },
            legalInfo: {
                phoneNumber: '0987654321',
                companyName: 'Institution Name',
                website: 'https://institution.com',
                companyRut: '12345678-9'
            },
            gestorInfo: {
                username: 'gestor',
                email: '<EMAIL>',
                position: 'Manager',
                rut: '98765432-1',
                password: '',
                phoneNumber: '+569 12345678',
                confirmPassword: ''
            }
        };
    
        const result = mapInstitutionFormSchemaToInstitution(1, 2, formSchema);
    
        expect(result).toEqual({
            id: 1,
            name: 'Institution Name',
            companyName: 'Institution Name',
            level: 1,
            nemployees: 0,
            address: '123 Street',
            city: 'City',
            region: 'Region',
            whatsapp: '1234567890',
            color: 'blue',
            acronym: 'INSTIT',
            email: '<EMAIL>',
            phoneNumber: '0987654321',
            website: 'https://institution.com',
            companyRut: '12345678-9',
            processes: [],
            nprocesses: 0,
            manager: {
                id: 2,
                username: 'gestor',
                email: '<EMAIL>',
                position: 'Manager',
                rut: '98765432-1',
                userRole: 'GESTOR',
                phoneNumber: '+569 12345678',
                city: '',
                nassignedInstitutions: 0,
                nauditsPerformed: 0,
                createdAt: expect.any(Date),
                updatedAt: expect.any(Date),
                updatedAtts: {}
            }
        });
    });
    

    it('debería mapear una Institution a un InstitutionBaseInfoSchema correctamente', () => {
        const result = mapInstitutionToBaseInstitutionInfoSchema(mockInstitution);
        expect(result).toEqual({
            name: 'Institution Name',
            address: '123 Street',
            city: 'City',
            region: 'Region',
            whatsapp: '1234567890',
            color: 'blue',
            acronym: 'INSTIT',
            email: '<EMAIL>',
            password: '',
            confirmPassword: ''
        });
    });

    it('debería mapear una Institution a un InstitutionLegalInfoFormSchema correctamente', () => {
        const result = mapInstitutionToLegalInfoSchema(mockInstitution);
        expect(result).toEqual({
            phoneNumber: '0987654321',
            companyName: 'Institution Name',
            website: 'https://institution.com',
            companyRut: '12345678-9'
        });
    });

    it('debería mapear una Institution a un GestorFormSchema correctamente', () => {
        const result = mapInstitutionToGestorInfoSchema(mockInstitution);
        expect(result).toEqual({
            username: 'gestor',
            email: '<EMAIL>',
            position: 'Manager',
            rut: '98765432-1',
            password: '',
            phoneNumber: '+569 12345678',
            confirmPassword: ''
        });
    });
    it('debería retornar "Administrador" para el rol "ADMIN" ', () => {
        const result = mapRoleToHumanReadable('ADMIN');
        expect(result).toBe('Administrador');
    });

    it('debería devolver "Gestor Institucional" para el rol "MANAGER" ', () => {
        const result = mapRoleToHumanReadable('MANAGER');
        expect(result).toBe('Gestor Institucional');
    });

    it('debería devolver "Auditor" para el rol "AUDITOR" ', () => {
        const result = mapRoleToHumanReadable('AUDITOR');
        expect(result).toBe('Auditor');
    });

    it('debería retornar el rol original si es que no está presente en las opciones', () => {
        const result = mapRoleToHumanReadable('USER');
        expect(result).toBe('USER');
    });
});
