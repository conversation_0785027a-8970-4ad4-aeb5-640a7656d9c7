import { describe, it, expect } from 'vitest';
import { formatLabel, extractInitials, extractCapitalizedInitials } from '../../src/helpers/stringHelpers';

describe('String Helpers', () => {
    describe('formatLabel', () => {
        it('should format the label for zero count', () => {
            const result = formatLabel(0, 'carro', 'carros');
            expect(result).toBe('Sin carros');
        });

        it('should format the label for singular count', () => {
            const result = formatLabel(1, 'carro', 'carros');
            expect(result).toBe('1 carro');
        });

        it('should format the label for plural count', () => {
            const result = formatLabel(3, 'carro', 'carros');
            expect(result).toBe('3 carros');
        });

        it('should format the label for null count', () => {
            const result = formatLabel(null!, 'carro', 'carros');
            expect(result).toBe('Sin carros');
        });
    });

    describe('extractInitials', () => {
        it('should extract initials from a single name', () => {
            const result = extractInitials('<PERSON>');
            expect(result).toBe('J');
        });

        it('should extract initials from a full name', () => {
            const result = extractInitials('Juan Pérez');
            expect(result).toBe('JP');
        });

        it('should extract initials from a name with multiple words', () => {
            const result = extractInitials('Juan de la Cruz Pérez');
            expect(result).toBe('JDLCP');
        });
    });

    describe('extractCapitalizedInitials', () => {
        it('should extract capitalized initials from a single name', () => {
            const result = extractCapitalizedInitials('Juan');
            expect(result).toBe('J');
        });

        it('should extract capitalized initials from a name with mixed case', () => {
            const result = extractCapitalizedInitials('juan Pérez');
            expect(result).toBe('P');
        });

        it('should extract only capitalized initials from a name with multiple words', () => {
            const result = extractCapitalizedInitials('juan de la Cruz Pérez');
            expect(result).toBe('CP');
        });

        it('should return an empty string if no capital letters are found', () => {
            const result = extractCapitalizedInitials('juan perez');
            expect(result).toBe('');
        });
    });
});
