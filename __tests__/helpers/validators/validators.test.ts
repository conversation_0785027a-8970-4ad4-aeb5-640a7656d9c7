import { describe, it, expect } from 'vitest';
import {
    validateRUT,
    validatePassword,
    validatePhone,
    validateRutFormat,
    calculateCheckDigit
} from '../../../src/helpers/validators';

describe('validation functions', () => {

    it('debería validar correctamente un RUT', () => {
        expect(validateRUT('11.111.111-1')).toBe(true);
        expect(validateRUT('12.345.678-K')).toBe(false);
    });


    it('debería validar correctamente una contraseña', () => {
        expect(validatePassword('Password1!')).toBe(true);
        expect(validatePassword('password')).toBe(false);
        expect(validatePassword('PASSWORD1!')).toBe(false);
        expect(validatePassword('Pass1')).toBe(false);
    });

    it('debería validar correctamente un número de teléfono', () => {
        expect(validatePhone('+56 9 12345678')).toBe(true);
        expect(validatePhone('+56 9 1234567')).toBe(false);
        expect(validatePhone('12345678')).toBe(false);
    });

    it('debería validar correctamente el formato de un RUT', () => {
        expect(validateRutFormat('12.345.678-9')).toBe(true);
        expect(validateRutFormat('12345678-9')).toBe(false);
        expect(validateRutFormat('12345678')).toBe(false);
        expect(validateRutFormat('12.345.678')).toBe(false);
    });


    it('debería calcular correctamente el dígito verificador de un RUT', () => {
        expect(calculateCheckDigit('11.111.111')).toBe('1');
    });

});
