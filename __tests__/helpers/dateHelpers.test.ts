import { describe, it, expect } from 'vitest';
import { addMonthsToDate, parseStringToDate, addDaysToDate } from '../../src/helpers/dateHelpers';

describe('Date Helpers', () => {
    describe('parseStringToDate', () => {
        it('should parse a valid date string correctly', () => {
            const dateStr = '2024-08-23';
            const parsedDate = parseStringToDate(dateStr);
            expect(parsedDate.getFullYear()).toBe(2024);
            expect(parsedDate.getMonth()).toBe(7);
            expect(parsedDate.getDate()).toBe(23);
        });

        it('should return an invalid date for incorrect format', () => {
            const dateStr = '2024/08/23';
            const parsedDate = parseStringToDate(dateStr);
            expect(parsedDate.toString()).toBe('Invalid Date');
        });
    });

    describe('addMonthsToDate', () => {
        it('should add the correct number of months', () => {
            const date = new Date(2024, 7, 23);
            const result = addMonthsToDate(date, 5);
            expect(result.getMonth()).toBe(0);
            expect(result.getFullYear()).toBe(2025);
        });

        it('should handle year changes when adding months', () => {
            const date = new Date(2024, 10, 15);
            const result = addMonthsToDate(date, 3);
            expect(result.getMonth()).toBe(1);
            expect(result.getFullYear()).toBe(2025);
        });
    });

    describe('addDaysToDate', () => {
        it('should add the correct number of days', () => {
            const date = new Date(2024, 7, 23);
            const result = addDaysToDate(date, 10);
            expect(result.getDate()).toBe(2);
            expect(result.getMonth()).toBe(8);
        });

        it('should handle month and year changes when adding days', () => {
            const date = new Date(2024, 11, 28);
            const result = addDaysToDate(date, 10);
            expect(result.getDate()).toBe(7);
            expect(result.getMonth()).toBe(0);
            expect(result.getFullYear()).toBe(2025);
        });
    });
});

