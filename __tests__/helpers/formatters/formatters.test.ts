import { describe, it, expect } from 'vitest';
import {
    formatRUT,
    formatPhoneNumber,
    formatRutToFlat,
    formatDateForChile,
    formatDateToISO,
    formatDateToString
} from '../../../src/helpers/formatters';


describe('formatter functions', () => {

    it('debería formatear correctamente un RUT', () => {
        expect(formatRUT('123456789')).toBe('12.345.678-9');
    });

    it('debería formatear correctamente un RUT a formato plano (con guión pero sin puntos)', () => {
        expect(formatRutToFlat('12.345.678-9')).toBe('12345678-9');
        expect(formatRutToFlat('12.345.678-9')).toBe('12345678-9');
    });


    it('debería formatear correctamente un número de teléfono', () => {
        expect(formatPhoneNumber('12345678')).toBe('+56 9 12345678');
        expect(formatPhoneNumber('+56 912345678')).toBe('+56 9 12345678');
    });

    it('debería formatear correctamente una fecha en formato chileno', () => {
        expect(formatDateForChile('2024-08-22')).toBe('22-08-2024');
        expect(formatDateForChile('2024-08-22', '/')).toBe('22/08/2024');

        const expectedDate = new Date().toISOString().slice(0, 10).split('-').reverse().join('-');
        expect(formatDateForChile(null)).toBe(expectedDate);
    });

    it('debería formatear correctamente una fecha a string en formato YYYY-MM-DD', () => {
        const date = new Date('2024-08-22');
        expect(formatDateToString(date)).toBe('2024-08-22');
    });

    it('debería formatear correctamente una fecha a ISO', () => {
        expect(formatDateToISO('2024-08-22')).toBe('2024-08-22T00:00:00.000Z');
    });

});
