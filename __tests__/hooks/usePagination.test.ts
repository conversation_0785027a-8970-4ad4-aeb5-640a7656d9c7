import { describe, it, expect, vi, beforeEach } from 'vitest';
import { usePagination } from '../../src/hooks/usePagination';

/**
 * Tests para el hook `usePagination`
 * 
 * - Debe inicializar con datos vacíos y cero páginas
 * - Debe obtener los datos de la página y actualizar el estado al cambiar de página
 * - Debe manejar correctamente las acciones de avanzar y retroceder página
 */

const mockFetchFunction = vi.fn();

describe('usePagination', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        vi.stubGlobal('scrollTo', vi.fn());
    });

    it('debe inicializar con datos vacíos y cero páginas', () => {
        mockFetchFunction.mockResolvedValue({
            data: [],
            totalPages: 0,
        });

        const { data, page, totalPages, fetchPageData, handleNextPage, handlePreviousPage } = usePagination(mockFetchFunction);

        expect(data.value).toEqual([]);
        expect(page.value).toBe(0);
        expect(totalPages.value).toBe(0);
        expect(fetchPageData).toBeDefined();
        expect(handleNextPage).toBeDefined();
        expect(handlePreviousPage).toBeDefined();
    });

    it('debe obtener los datos de la página y actualizar el estado al cambiar de página', async () => {
        const mockResponse = {
            data: [{ id: 1, name: 'Item 1' }],
            totalPages: 1,
        };

        mockFetchFunction.mockResolvedValue(mockResponse);

        const { data, page, totalPages, fetchPageData } = usePagination(mockFetchFunction);

        page.value = 1;
        await fetchPageData(page.value);

        expect(mockFetchFunction).toHaveBeenCalledWith(1);
        expect(data.value).toEqual(mockResponse.data);
        expect(totalPages.value).toBe(mockResponse.totalPages);
    });

    it('debe manejar correctamente las acciones de avanzar y retroceder página', async () => {
        mockFetchFunction.mockResolvedValue({
            data: [{ id: 1, name: 'Item 1' }],
            totalPages: 2,
        });

        const { page, handleNextPage, handlePreviousPage } = usePagination(mockFetchFunction);

        handleNextPage();
        expect(page.value).toBe(1);

        handlePreviousPage();
        expect(page.value).toBe(0);

        handlePreviousPage();
        expect(page.value).toBe(0);
    });
});
