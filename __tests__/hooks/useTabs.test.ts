import { describe, it, expect, beforeEach } from 'vitest';
import { ref } from 'vue';
import { useTabs } from '../../src/hooks/useTabs';

/**
 * Tests para el hook `useTabs`
 * 
 * - Debe inicializar con el primer tab como activo
 * - Debe establecer el tab activo correctamente
 * - Debe avanzar al siguiente tab correctamente
 * - Debe retroceder al tab anterior correctamente
 * - Debe devolver el componente correcto del tab activo
 */

describe('useTabs', () => {
    let tabsRef: any;
    let hook: any;

    beforeEach(() => {
        tabsRef = ref([
            { name: 'tab1', component: {} },
            { name: 'tab2', component: {} },
            { name: 'tab3', component: {} },
        ]);

        hook = useTabs(tabsRef);
    });

    it('debe inicializar con el primer tab como activo', () => {
        expect(hook.activeTab.value).toEqual(tabsRef.value[0]);
    });

    it('debe establecer el tab activo correctamente', () => {
        hook.setActiveTab('tab2');
        expect(hook.activeTab.value.name).toBe('tab2');

        hook.setActiveTab('invalidTab');
        expect(hook.activeTab.value.name).toBe('tab1');
    });

    it('debe avanzar al siguiente tab correctamente', () => {
        hook.setActiveTab('tab1');
        hook.nextTab();
        expect(hook.activeTab.value.name).toBe('tab2');

        hook.nextTab();
        expect(hook.activeTab.value.name).toBe('tab3');

        hook.nextTab();
        expect(hook.activeTab.value.name).toBe('tab3');
    });

    it('debe retroceder al tab anterior correctamente', () => {
        hook.setActiveTab('tab3');
        hook.prevTab();
        expect(hook.activeTab.value.name).toBe('tab2');

        hook.prevTab();
        expect(hook.activeTab.value.name).toBe('tab1');

        hook.prevTab();
        expect(hook.activeTab.value.name).toBe('tab1');
    });

    it('debe devolver el componente correcto del tab activo', () => {
        const expectedComponent = tabsRef.value[0].component;
        expect(hook.activeTabComponent.value).toBe(expectedComponent);
    });
});
