import { describe, it, expect, vi, beforeEach, type Mock } from 'vitest';
import { useMaturityModels } from '../../src/hooks/useMaturityModels';
import { fetchAllMaturityModels } from '../../src/services/api/maturityModelService';

/**
 * Tests para el hook `useMaturityModels`
 * 
 * - Debe obtener y setear los modelos de madurez
 * - Debe manejar errores de forma adecuada
 */


vi.mock('../../src/services/api/maturityModelService', () => ({
    fetchAllMaturityModels: vi.fn(),
}));

describe('useMaturityModels', () => {
    const mockFetchAllMaturityModels = fetchAllMaturityModels as Mock

    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('debe obtener y setear los modelos de madurez', async () => {
        const mockData = [{ id: 1, name: 'Model A' }, { id: 2, name: 'Model B' }];
        mockFetchAllMaturityModels.mockResolvedValueOnce(mockData);

        const { maturityModels, fetchPageData, page } = useMaturityModels();

        await fetchPageData(page.value);

        // expect(maturityModels.value).toEqual(mockData);
        expect(mockFetchAllMaturityModels).toHaveBeenCalled();
    });

    it('debe manejar errores de forma adecuada', async () => {
        mockFetchAllMaturityModels.mockRejectedValueOnce(new Error('Fetch failed'));

        const { maturityModels, fetchPageData, page } = useMaturityModels();
        await fetchPageData(page.value);

        expect(maturityModels.value).toEqual([]);
        expect(mockFetchAllMaturityModels).toHaveBeenCalled();
    });
});
