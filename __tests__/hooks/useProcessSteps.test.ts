import { describe, it, expect, beforeEach } from 'vitest'
import { useProcessSteps } from '../../src/hooks/process/useProcessSteps'
import { nextTick, ref } from 'vue'

/**
 * Tests para el hook `useProcessSteps`
 *
 * - Debe inicializar con los valores correctos
 * - Debe actualizar el texto del botón y el estado del proceso al cambiar de paso
 */

describe('useProcessSteps', () => {
  const process = ref<any>({ id: 1 })

  let steps

  beforeEach(() => {
    steps = useProcessSteps(process)
  })

  it('debe inicializar con los valores correctos', () => {
    const { buttonLabel, processStatus, currentStep } = steps

    expect(currentStep.value).toBe(1)
    expect(buttonLabel.value).toBe('Enviar Encuestas')
    expect(processStatus.value).toEqual({
      color: 'text-neutral-800',
      description:
        'Actualmente, tiene un proceso en curso. Los resultados que se muestran a continuación correponden al último proceso finalizado. Una vez que el proceso actual se complete, sus resultados se actualizarán en esta vista automáticamnte. Puede monitorear el avance del proceso de evaluación con el botón monitorear',
      icon: 'fa-solid fa-rotate',
      title: 'Actualmente tiene un proceso en curso'
    })
  })

  it('debe actualizar el texto del botón y el estado del proceso al cambiar de paso', async () => {
    const { buttonLabel, processStatus, currentStep, increaseStep } = steps

    increaseStep()

    await nextTick()

    expect(currentStep.value).toBe(2)
    expect(buttonLabel.value).toBe('Enviar Encuestas')
    expect(processStatus.value).toEqual({
      color: 'text-neutral-800',
      description:
        'Actualmente, tiene un proceso en curso. Los resultados que se muestran a continuación correponden al último proceso finalizado. Una vez que el proceso actual se complete, sus resultados se actualizarán en esta vista automáticamnte. Puede monitorear el avance del proceso de evaluación con el botón monitorear',
      icon: 'fa-solid fa-rotate',
      title: 'Actualmente tiene un proceso en curso'
    })
  })
})
