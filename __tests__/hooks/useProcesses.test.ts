import { describe, it, expect, vi, beforeEach, afterEach, type Mock } from 'vitest';
import { ref } from 'vue';
import { useProcesses } from '../../src/hooks/process/useProcesses';
import { fetchProcessesByAuditorId } from '../../src/services/api/processService';

/**
 * Tests para el hook `useProcesses`
 */

vi.mock('vue-router', () => ({
    useRoute: () => ({
        path: '/auditor/IN_PROGRESS/1'
    })
}));

vi.mock('../../src/services/api/processService', () => ({
    fetchProcessesByAuditorId: vi.fn()
}));

describe('useProcesses Hook', () => {
    let mockFetchProcessesByAuditor: Mock;
    let mockFetchPageData: Mock;
    let mockUsePagination: any;

    beforeEach(() => {
        mockFetchProcessesByAuditor = fetchProcessesByAuditorId as Mock;
        mockFetchPageData = vi.fn();
        mockUsePagination = vi.fn(() => ({
            data: ref([]),
            page: ref(1),
            fetchPageData: mockFetchPageData,
            totalPages: ref(1),
            handleNextPage: vi.fn(),
            handlePreviousPage: vi.fn()
        }));

        vi.mock('../src/hooks/usePagination', () => ({
            usePagination: mockUsePagination
        }));
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('handleGetButtonTextByStatus', () => {
        it('debe retornar "Auditar" cuando el estado es "AUDIT"', () => {
            const { handleGetButtonTextByStatus } = useProcesses();
            const result = handleGetButtonTextByStatus('AUDIT');
            expect(result).toBe('Auditar');
        });

        it('debe retornar "Resumen" cuando el estado es "FINISHED"', () => {
            const { handleGetButtonTextByStatus } = useProcesses();
            const result = handleGetButtonTextByStatus('FINISHED');
            expect(result).toBe('Resumen');
        });

        it('debe retornar "Seguimiento" cuando el estado es "IN_PROGRESS"', () => {
            const { handleGetButtonTextByStatus } = useProcesses();
            const result = handleGetButtonTextByStatus('IN_PROGRESS');
            expect(result).toBe('Seguimiento');
        });

        it('debe retornar "Seguimiento" para un estado no definido', () => {
            const { handleGetButtonTextByStatus } = useProcesses();
            const result = handleGetButtonTextByStatus('UNKNOWN_STATUS');
            expect(result).toBe('Seguimiento');
        });
    });

    describe('canAction', () => {
        it('debe retornar "true" cuando el estado es diferente a "UNINITIATED"', () => {
            const { canAction } = useProcesses();
            expect(canAction('IN_PROGRESS')).toBe(true);
            expect(canAction('FINISHED')).toBe(true);
            expect(canAction('AUDIT')).toBe(true);
        });

        it('debe retornar "false" cuando el estado es "UNINITIATED"', () => {
            const { canAction } = useProcesses();
            const result = canAction('UNINITIATED');
            expect(result).toBe(false);
        });
    });

    describe('fetchProcesses', () => {
        it('debe filtrar instituciones basado en el término de búsqueda', async () => {
            const { handleSearch, processes } = useProcesses();

            mockFetchProcessesByAuditor.mockResolvedValueOnce({
                data: [{ name: 'Institution A' }]
            });

            await handleSearch('Institution A');

            expect(processes.value).toEqual([]);
        });

        it('debe generar el link correcto para el router', () => {
            const { getRouterLinkByStatusAndID } = useProcesses();

            expect(getRouterLinkByStatusAndID('AUDIT', 1)).toBe('/auditor/AUDIT/1');
        });
    });
});
