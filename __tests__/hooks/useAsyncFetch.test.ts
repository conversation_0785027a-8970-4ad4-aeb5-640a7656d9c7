import { describe, it, expect, vi } from 'vitest';
import { useAsyncFetch } from '../../src/hooks/useAsyncFetch';

describe('useAsyncFetch', () => {
    it('should initialize with null data, isLoading false, and null error', () => {
        const mockAsyncFunction = vi.fn(async () => 'test');
        const { data, isLoading, error } = useAsyncFetch(mockAsyncFunction);

        expect(data.value).toBeNull();
        expect(isLoading.value).toBe(false);
        expect(error.value).toBeNull();
    });

    it('should set isLoading to true when fetchData is called', async () => {
        const mockAsyncFunction = vi.fn(async () => 'test');
        const { fetchData, isLoading } = useAsyncFetch(mockAsyncFunction);

        const promise = fetchData();
        expect(isLoading.value).toBe(true);

        await promise;
    });

    it('should set data correctly after successful async function call', async () => {
        const mockResponse = 'success';
        const mockAsyncFunction = vi.fn(async () => mockResponse);
        const { fetchData, data } = useAsyncFetch(mockAsyncFunction);

        await fetchData();

        expect(data.value).toBe(mockResponse);
    });

    it('should set error if async function throws', async () => {
        const mockError = new Error('Test error');
        const mockAsyncFunction = vi.fn(async () => {
            throw mockError;
        });
        const { fetchData, error } = useAsyncFetch(mockAsyncFunction);

        await fetchData();

        expect(error.value).toBe(mockError);
    });

    it('should set isLoading to false after fetchData is called', async () => {
        const mockAsyncFunction = vi.fn(async () => 'test');
        const { fetchData, isLoading } = useAsyncFetch(mockAsyncFunction);

        await fetchData();

        expect(isLoading.value).toBe(false);
    });

    it('should pass parameters to async function', async () => {
        const mockAsyncFunction = vi.fn(async (params: string) => `response for ${params}`);
        const { fetchData, data } = useAsyncFetch(mockAsyncFunction);

        await fetchData('test-param');

        expect(mockAsyncFunction).toHaveBeenCalledWith('test-param');
        expect(data.value).toBe('response for test-param');
    });
});
