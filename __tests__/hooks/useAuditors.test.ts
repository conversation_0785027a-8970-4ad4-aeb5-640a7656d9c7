import { describe, it, expect, vi, beforeEach, afterEach, type Mock } from 'vitest';
import { ref } from 'vue';
import { useAuditors } from '../../src/hooks/useAuditors';
import { fetchAllAuditors, createAuditor, updateAuditor, toggleAuditorStatus } from '../../src/services/api/auditorService';
import { usePagination } from '../../src/hooks/usePagination';
import type { AuditorFormSchema } from '../../src/models/validation/formSchemas';

/**
 * Tests para el hook `useAuditors`
 * 
 * - Debe inicializar con valores correctos
 * - Debe manejar la búsqueda de auditores correctamente
 * - Debe manejar la creación de un auditor correctamente
 * - Debe manejar la actualización de un auditor correctamente
 */

vi.mock('../../src/services/api/auditorService', () => ({
    fetchAllAuditors: vi.fn(),
    createAuditor: vi.fn(),
    updateAuditor: vi.fn(),
    toggleAuditorStatus: vi.fn()
}));

vi.mock('../../src/hooks/usePagination', () => ({
    usePagination: vi.fn(() => ({
        data: ref([]),
        page: ref(1),
        totalPages: ref(1),
        fetchPageData: vi.fn(),
        handleNextPage: vi.fn(),
        handlePreviousPage: vi.fn()
    }))
}));

describe('useAuditors', () => {
    let mockFetchAllAuditors: Mock;
    let mockCreateAuditor: Mock;
    let mockUpdateAuditor: Mock;
    let mockToggleAuditorStatus: Mock;

    beforeEach(() => {
        mockFetchAllAuditors = fetchAllAuditors as Mock;
        mockCreateAuditor = createAuditor as Mock;
        mockUpdateAuditor = updateAuditor as Mock;
        mockToggleAuditorStatus = toggleAuditorStatus as Mock;

        (usePagination as Mock).mockReturnValue({
            data: ref([]),
            page: ref(1),
            totalPages: ref(1),
            fetchPageData: vi.fn(),
            handleNextPage: vi.fn(),
            handlePreviousPage: vi.fn(),
            isLoading: ref<boolean>(false)
        });
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    it('debería inicializar con los valores correctos', () => {
        const { auditors, page, totalPages } = useAuditors();

        expect(auditors.value).toEqual([]);
        expect(page.value).toBe(1);
        expect(totalPages.value).toBe(1);
    });

    it('debería manejar la búsqueda correctamente', async () => {
        const { handleSearchAuditors, auditors } = useAuditors();

        mockFetchAllAuditors.mockResolvedValueOnce({
            data: [
                { username: 'auditor1' }
            ]
        });

        await handleSearchAuditors('auditor1');

        expect(auditors.value).toEqual([{ username: 'auditor1' }]);
    });

    it('debería manejar la creación de un auditor', async () => {
        const { handleCreateAuditor, auditors } = useAuditors();

        const newAuditor = { id: 1, username: 'new_auditor' } as any;
        mockCreateAuditor.mockResolvedValueOnce(newAuditor);

        await handleCreateAuditor({ username: 'new_auditor' } as AuditorFormSchema);

        expect(auditors.value).toEqual(expect.arrayContaining([expect.objectContaining(newAuditor)]));
    });

    it('debería manejar la actualización de un auditor', async () => {
        const { handleUpdateAuditor, auditors } = useAuditors();

        const initialAuditor = { id: 1, username: 'initial_auditor' } as any;
        const updatedAuditor = { id: 1, username: 'updated_auditor' } as any;

        auditors.value = [initialAuditor];
        mockUpdateAuditor.mockResolvedValueOnce(updatedAuditor);

        await handleUpdateAuditor(1, { username: 'updated_auditor' } as AuditorFormSchema);

        expect(auditors.value).toContainEqual(updatedAuditor);
    });

    it('debería manejar el cambio de estado de un auditor', async () => {
        const { handleToggleAuditorStatus } = useAuditors();

        const updatedAuditor = { id: 1, username: 'auditor' } as any;
        mockToggleAuditorStatus.mockResolvedValueOnce(updatedAuditor);

        await handleToggleAuditorStatus(1);

        expect(mockToggleAuditorStatus).toHaveBeenCalledWith(1);
    });
});
