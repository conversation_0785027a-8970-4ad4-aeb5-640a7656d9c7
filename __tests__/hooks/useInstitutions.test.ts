import { describe, it, expect, vi, beforeEach, afterEach, type Mock } from 'vitest';
import { useInstitutions } from '../../src/hooks/useInstitutions';
import { fetchAllInstitutions, createInstitution, updateInstitution, fetchInstitutionByManagerID, assignAuditorToInstitution, toggleInstitutionStatus } from '../../src/services/api/institutionService';
import { createManager } from '../../src/services/api/managerService';

/**
 * Tests para el hook `useInstitutions`
 * 
 * - Debe inicializar con los valores correctos
 * - Debe manejar la búsqueda de instituciones
 * - Debe manejar la creación de instituciones
 * - Debe manejar la actualización de instituciones
 * - Debe manejar la obtención de institución por ID de manager
 * - Debe manejar la asignación de auditor a institución
 * - Debe manejar el cambio de estado de la institución
 */

vi.mock('../../src/services/api/institutionService', () => ({
    fetchAllInstitutions: vi.fn(),
    createInstitution: vi.fn(),
    updateInstitution: vi.fn(),
    fetchInstitutionByManagerID: vi.fn(),
    assignAuditorToInstitution: vi.fn(),
    toggleInstitutionStatus: vi.fn(),
    assignManagerToInstitution: vi.fn()
}));

vi.mock('../../src/services/api/managerService', () => ({
    createManager: vi.fn(),
}));

describe('useInstitutions', () => {
    let mockFetchAllInstitutions: any;
    let mockCreateInstitution: any;
    let mockUpdateInstitution: any;
    let mockFetchInstitutionByManagerID: any;
    let mockCreateGestor: any;
    let mockAssignAuditorToInstitution: any;
    let mockToggleInstitutionStatus: any;

    beforeEach(() => {
        mockFetchAllInstitutions = fetchAllInstitutions as Mock;
        mockCreateInstitution = createInstitution as Mock;
        mockUpdateInstitution = updateInstitution as Mock;
        mockFetchInstitutionByManagerID = fetchInstitutionByManagerID as Mock;
        mockCreateGestor = createManager as Mock;
        mockAssignAuditorToInstitution = assignAuditorToInstitution as Mock;
        mockToggleInstitutionStatus = toggleInstitutionStatus as Mock;
        vi.stubGlobal('location', { reload: vi.fn() });
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    it('debe inicializarse con los valores correctos', () => {
        const { institutions, page, totalPages, fetchPageData, handleNextPage, handlePreviousPage } = useInstitutions();

        expect(institutions.value).toEqual([]);
        expect(page.value).toBe(0);
        expect(totalPages.value).toBe(0);
        expect(fetchPageData).toBeDefined();
        expect(handleNextPage).toBeDefined();
        expect(handlePreviousPage).toBeDefined();
    });

    it('debe manejar la búsqueda de instituciones', async () => {
        const { handleSearchInstitutions, institutions } = useInstitutions();

        mockFetchAllInstitutions.mockResolvedValueOnce({
            data: [
                { name: 'Institution A' }
            ]
        });

        await handleSearchInstitutions('Institution A');

        expect(institutions.value).toEqual([{ name: 'Institution A' }]);
    });

    it('debe manejar la creación de instituciones', async () => {
        const { handleCreateInstitution } = useInstitutions();
        const institutionData = { name: 'Institution A', gestorInfo: {}, baseInfo: {} };

        mockCreateInstitution.mockResolvedValueOnce({ id: 1 });
        mockCreateGestor.mockResolvedValueOnce({ id: 1 });
        mockAssignAuditorToInstitution.mockResolvedValueOnce();

        const result = await handleCreateInstitution(institutionData);

        expect(result).toEqual({ id: 1 });
        expect(mockCreateInstitution).toHaveBeenCalledWith(institutionData);
        expect(mockCreateGestor).toHaveBeenCalled();
    });

    it('debe manejar la actualización de instituciones', async () => {
        const { handleUpdateInstitution } = useInstitutions();
        const institutionData = { name: 'Updated Institution' };

        mockUpdateInstitution.mockResolvedValueOnce({ id: 1, ...institutionData });

        const result = await handleUpdateInstitution(1, 1, institutionData);

        expect(result).toEqual({ id: 1, ...institutionData });
        expect(mockUpdateInstitution).toHaveBeenCalledWith(1, expect.anything());
    });

    it('debe manejar la obtención de institución por ID de manager', async () => {
        const { handleGetInstitutionByManagerID } = useInstitutions();

        mockFetchInstitutionByManagerID.mockResolvedValueOnce({ id: 1, name: 'Institution A' });

        const result = await handleGetInstitutionByManagerID(1);

        expect(result).toEqual({ id: 1, name: 'Institution A' });
        expect(mockFetchInstitutionByManagerID).toHaveBeenCalledWith(1);
    });

    it('debe manejar la asignación de auditor a institución', async () => {
        const { handleAssignAuditorToInstitution } = useInstitutions();

        await handleAssignAuditorToInstitution(1, 2);

        expect(mockAssignAuditorToInstitution).toHaveBeenCalledWith(1, 2);
    });

    it('debe manejar el cambio de estado de la institución', async () => {
        const { handleToggleInstitutionStatus } = useInstitutions();

        await handleToggleInstitutionStatus(1);

        expect(mockToggleInstitutionStatus).toHaveBeenCalledWith(1);
    });
});
