import { describe, it, expect, vi } from 'vitest';
import { ref, nextTick } from 'vue';
import { useSearch } from '../../src/hooks/useSearch';


describe('useSearch', () => {
    it('debe llamar a searchFunction después del retraso especificado', async () => {
        const searchFunction = vi.fn();
        const delay = 500;


        const { searchTerm } = useSearch(searchFunction, delay);


        searchTerm.value = 'test';

        await new Promise(resolve => setTimeout(resolve, delay + 100));

        expect(searchFunction).toHaveBeenCalled();
        expect(searchFunction).toHaveBeenCalledWith('test');
    });

    it('debe cancelar la búsqueda anterior si se cambia el término de búsqueda antes del retraso', async () => {
        const searchFunction = vi.fn();
        const delay = 500;

        const { searchTerm } = useSearch(searchFunction, delay);

        searchTerm.value = 'test1';
        await new Promise(resolve => setTimeout(resolve, delay / 2));
        searchTerm.value = 'test2';

        await new Promise(resolve => setTimeout(resolve, delay + 100));

        expect(searchFunction).toHaveBeenCalledTimes(1);
        expect(searchFunction).toHaveBeenCalledWith('test2');
    });

    it('debe aplicar el retraso especificado al llamar a searchFunction', async () => {
        const searchFunction = vi.fn();
        const delay = 300;

        const { searchTerm } = useSearch(searchFunction, delay);

        searchTerm.value = 'test';

        await new Promise(resolve => setTimeout(resolve, delay - 100));

        expect(searchFunction).not.toHaveBeenCalled();

        await new Promise(resolve => setTimeout(resolve, 100));

        expect(searchFunction).toHaveBeenCalled();
        expect(searchFunction).toHaveBeenCalledWith('test');
    });

    it('debe inicializar searchTerm como una cadena vacía', () => {
        const searchFunction = vi.fn();
        const { searchTerm } = useSearch(searchFunction);
        expect(searchTerm.value).toBe('');
    });
});
