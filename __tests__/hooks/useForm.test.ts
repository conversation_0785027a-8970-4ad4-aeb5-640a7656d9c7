import { describe, it, expect, vi } from 'vitest';
import { ref, reactive} from 'vue';
import { useForm } from '../../src/hooks/useForm';
import { useVuelidate } from '@vuelidate/core';

vi.mock('@vuelidate/core', () => ({
    useVuelidate: vi.fn().mockReturnValue({
        value: {
            $validate: vi.fn().mockResolvedValue(true),
            $reset: vi.fn(),
        }
    })
}));

describe('useForm', () => {
    const initialData = reactive({ name: 'John', age: 30 });
    const validationRules = {};
    const onSubmitMock = vi.fn().mockResolvedValue(undefined);

    const createHook = (options = {}) => useForm({
        initialDataRef: ref(initialData),
        validationRules,
        updateCondition: ref(false),
        onSubmit: onSubmitMock,
        ...options
    });

    it('debería inicializar formData con los valores correctos', () => {
        const { formData } = createHook();
        expect(formData).toEqual(initialData);
    });

    it('debería llamar a $validate cuando se haga el submit, y subir la data en caso de ser válida', async () => {
        const { handleSubmit, isLoading } = createHook();
        await handleSubmit();

        expect(isLoading.value).toBe(false);
        expect(useVuelidate().value.$validate).toHaveBeenCalled();
        expect(onSubmitMock).toHaveBeenCalledWith(initialData);
    });

    it('no debería llamar al submit si es que no hay datos nuevos, en modo update', async () => {
        const initialData = reactive({ name: 'John', age: 30 });
        const onSubmitMock = vi.fn().mockResolvedValue(undefined);

        const { handleSubmit, hasChanges } = createHook({
            initialDataRef: ref(initialData),
            updateCondition: ref(true), 
            onSubmit: onSubmitMock
        });
        expect(hasChanges.value).toBe(false);
        await handleSubmit();
        expect(onSubmitMock).not.toHaveBeenCalled();
    });

    it('debería hacer submit sólo con los datos modificados, si updateOnlyModified es true', async () => {
        const modifiedData = { name: 'John', age: 35 };
        const { formData, handleSubmit } = createHook({ updateCondition: ref(true), updateOnlyModified: true });

        formData.age = modifiedData.age;
        await handleSubmit();

        expect(onSubmitMock).toHaveBeenCalledWith({ age: 35 });
    });

    it('debería resetear el formulario cuando el initialDataRef cambie', async () => {
        const { formData, $vForm } = createHook();
        const newData = { name: 'Jane', age: 25 };
        vi.useFakeTimers();

        initialData.name = newData.name;
        initialData.age = newData.age;

        await vi.runAllTimers();

        expect(formData.name).toBe('Jane');
        expect(formData.age).toBe(25);
        expect($vForm.value.$reset).toHaveBeenCalled();
    });

    it('debería setear el estado isLoading a true, cuando se esté haciendo submit', async () => {
        const { handleSubmit, isLoading } = createHook();
        const promise = handleSubmit();

        expect(isLoading.value).toBe(true);
        await promise;
        expect(isLoading.value).toBe(false);
    });

    it('debería manejar los errores en el submit', async () => {
        const errorMock = new Error('Submission failed');
        onSubmitMock.mockRejectedValueOnce(errorMock);
        const { handleSubmit, isLoading } = createHook();

        await handleSubmit();

        expect(isLoading.value).toBe(false);
    });

    it('debería llamar al submit sólo con los campos modificados, cuando updateOnlyModified sea true', async () => {
        const onSubmitMock = vi.fn().mockResolvedValue(undefined);
        const initialData = reactive({ name: 'John', age: 30 });

        const { formData, handleSubmit } = createHook({
            updateCondition: ref(true),
            updateOnlyModified: true,
            initialDataRef: ref(initialData),
            onSubmit: onSubmitMock
        });

        formData.age = 35;

        await handleSubmit();

        expect(onSubmitMock).toHaveBeenCalledWith({ age: 35 });
        expect(onSubmitMock).not.toHaveBeenCalledWith(expect.objectContaining({ name: 'John' }));
    });
});
