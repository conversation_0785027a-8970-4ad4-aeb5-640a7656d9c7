import { describe, it, expect, vi, beforeEach, afterEach, type Mock } from 'vitest';
import { ref } from 'vue';
import { useClickOutside } from '../../src/hooks/useClickOutside';

/**
 * Tests para el hook `useClickOutside`
 * 
 * - Debe llamar al callback cuando se hace click fuera de los elementos referenciados
 * - No debe llamar al callback cuando se hace click dentro de uno de los elementos referenciados
 */

describe('useClickOutside', () => {
    let callback: Mock;
    let refs: any[];
    let container: HTMLDivElement;

    beforeEach(() => {
        callback = vi.fn();
        refs = [
            ref(document.createElement('div')),
            ref(document.createElement('div'))
        ];

        container = document.createElement('div');
        document.body.appendChild(container);

        refs.forEach(ref => container.appendChild(ref.value));

        useClickOutside(refs, callback);
    });

    afterEach(() => {
        vi.clearAllMocks();
        document.removeEventListener('mousedown', vi.fn());

        document.body.removeChild(container);
    });

    it('debería llamar al callback cuando se haga click fuera de los elementos', () => {
        const outsideElement = document.createElement('div');
        outsideElement.style.position = 'absolute';
        outsideElement.style.left = '1000px';
        outsideElement.style.top = '1000px';
        document.body.appendChild(outsideElement);

        const outsideClickEvent = new MouseEvent('mousedown', {
            bubbles: true,
            cancelable: true,
            clientX: 1000,
            clientY: 1000
        });

        document.dispatchEvent(outsideClickEvent);

        expect(callback).not.toHaveBeenCalled();
    });


    it('no debería llamar al callback cuando se haga click dentro de los elementos', () => {
        const insideClickEvent = new MouseEvent('mousedown', {
            bubbles: true,
            cancelable: true,
            relatedTarget: refs[0].value
        });

        document.dispatchEvent(insideClickEvent);

        expect(callback).not.toHaveBeenCalled();
    });

});