import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { useRequests } from '../../src/hooks/useRequests';

/**
 * Tests para el hook `useRequests`
 * 
 * - Debe obtener y actualizar las solicitudes de instituciones correctamente
 * - Debe obtener y actualizar las solicitudes de procesos correctamente
 */

describe('useRequests', () => {
    let mockFetchInstitutions: any;
    let mockFetchProcesses: any;

    beforeEach(() => {
        mockFetchInstitutions = vi.fn();
        mockFetchProcesses = vi.fn();
        vi.mock('@/services/api/institutionService', () => ({
            fetchAllInstitutionsRegisterRequests: mockFetchInstitutions,
        }));

        vi.mock('@/services/api/processService', () => ({
            fetchAllProcessesRequests: mockFetchProcesses,
        }));
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    it('debe obtener y actualizar las solicitudes de instituciones correctamente', async () => {
        const { institutionsRequests, handleFetchInstitutionRequests } = useRequests();

        await handleFetchInstitutionRequests();

        expect(institutionsRequests.value).toBeTruthy();
        expect(mockFetchInstitutions).not.toBeNull();
    });


    it('debe obtener y actualizar las solicitudes de procesos correctamente', async () => {
        const { processesRequests, handleFetchProcessesRequests } = useRequests();

        await handleFetchProcessesRequests();

        expect(processesRequests.value).toBeTruthy();
        expect(mockFetchProcesses).not.toBeNull();
    });
});
