import { describe, it, expect, beforeEach, vi } from 'vitest';
import { fetchAllAuditors, createAuditor, updateAuditor, toggleAuditorStatus } from '../../src/services/api/auditorService';
import { fetchWithHandling } from '../../src/services/handlers/fetchHandler';
import type { Auditor, PaginationResponse } from '../../src/models/types';
import type { AuditorFormSchema } from '../../src/models/validation/formSchemas';
import { getEnvironments } from '../../src/helpers/envHelpers'

/**
 * Tests para el servicio `auditorService`
 * 
 * - Debería obtener todos los auditores correctamente
 * - Debería crear un auditor correctamente
 * - Debería actualizar un auditor correctamente
 * - Debería alternar el estado de un auditor correctamente
 */

const { VITE_USER_API_URL } = getEnvironments();

vi.mock('../../src/services/handlers/fetchHandler', () => ({
    fetchWithHandling: vi.fn()
}));

const fetchWithHandlingMock = vi.mocked(fetchWithHandling);

const mockAuditor = { id: 1, username: 'Auditor 1' } as Auditor;

const mockAuditorsResponse = {
    data: [mockAuditor],
    totalPages: 1
} as PaginationResponse<Auditor>;

const newAuditor = { username: 'New Auditor' } as AuditorFormSchema;
const updatedAuditor = { id: 1, username: 'Updated Auditor' } as Auditor;

beforeEach(() => {
    vi.resetAllMocks();
});

describe('fetchAllAuditors', () => {
    it('debería obtener todos los auditores correctamente', async () => {
        fetchWithHandlingMock.mockResolvedValue(mockAuditorsResponse);

        const result = await fetchAllAuditors(0);

        expect(fetchWithHandling).toHaveBeenCalledWith(`${VITE_USER_API_URL}/auditors?page=0`, {
            errorMessage: 'Error fetching auditors',
        });

        expect(result).not.toBeNull();
    });
});

describe('createAuditor', () => {
    it('debería crear un auditor correctamente', async () => {
        fetchWithHandlingMock.mockResolvedValue({ data: mockAuditor });

        const result = await createAuditor(newAuditor);

        expect(fetchWithHandling).toHaveBeenCalledWith(`${VITE_USER_API_URL}/auditors`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(newAuditor),
            errorMessage: 'Error creating auditor'
        });
        expect(result).toEqual(mockAuditor);
    });
});

describe('updateAuditor', () => {
    it('debería actualizar un auditor correctamente', async () => {
        fetchWithHandlingMock.mockResolvedValue({ data: updatedAuditor });

        const result = await updateAuditor(1, newAuditor);

        expect(fetchWithHandling).toHaveBeenCalledWith(`${VITE_USER_API_URL}/auditors/1`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(newAuditor),
            errorMessage: 'Error updating auditor with ID: 1'
        });
        expect(result).toEqual(updatedAuditor);
    });
});

describe('toggleAuditorStatus', () => {
    it('debería alternar el estado de un auditor correctamente', async () => {
        fetchWithHandlingMock.mockResolvedValue({ data: null });

        await toggleAuditorStatus(1);

        expect(fetchWithHandling).toHaveBeenCalledWith(`${VITE_USER_API_URL}/auditors/1/status`, {
            method: 'PATCH',
            errorMessage: 'Error toggling auditor status with ID: 1'
        });
    });
});
