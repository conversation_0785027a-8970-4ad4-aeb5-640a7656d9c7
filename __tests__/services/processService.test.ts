import { describe, it, expect, beforeEach, vi } from 'vitest';
import { fetchActualProcessByInstitutionID, fetchAllProcessesRequests, fetchProcessByID, modifyProcessStatus } from '../../src/services/api/processService';
import { fetchWithHandling } from '../../src/services/handlers/fetchHandler';
import type { Process, ProcessStatus } from '../../src/models/types';
import { getEnvironments } from '../../src/helpers/envHelpers'

/**
 * Tests para el servicio `processService`
 * 
 * - Debería obtener el proceso actual de una institución correctamente
 * - Debería obtener todas las solicitudes de procesos correctamente
 * - Debería obtener un proceso por ID correctamente
 * - Debería modificar el estado de un proceso correctamente
 */

const { VITE_API_URL } = getEnvironments();

vi.mock('../../src/services/handlers/fetchHandler', () => ({
    fetchWithHandling: vi.fn()
}));

const fetchWithHandlingMock = vi.mocked(fetchWithHandling);


const mockProcess = { id: 1, startDate: '2024-01-01T00:00:00Z', endDate: '2024-12-31T23:59:59Z' } as Process;
const mockProcesses = [mockProcess];
const mockProcessById = { id: 2 } as Process;

beforeEach(() => {
    vi.resetAllMocks();
});

describe('fetchActualProcessByInstitutionID', () => {
    it('debería obtener el proceso actual de una institución correctamente', async () => {
        fetchWithHandlingMock.mockResolvedValue({ data: [mockProcess] });

        const result = await fetchActualProcessByInstitutionID(1);

        expect(fetchWithHandling).toHaveBeenCalledWith(`${VITE_API_URL}/processes/by-institution/1?current=true`, {
            errorMessage: 'Error fetching actual process by institution with ID 1'
        });

        expect(result).not.toBeNull();
        //expect(result.id).not.toBeFalsy();
        //expect(result.id).toEqual(mockProcess.id);
    });
});

describe('fetchAllProcessesRequests', () => {
    it('debería obtener todas las solicitudes de procesos correctamente', async () => {
        fetchWithHandlingMock.mockResolvedValue({ data: mockProcesses });

        const result = await fetchAllProcessesRequests();

        expect(fetchWithHandling).toHaveBeenCalledWith(`${VITE_API_URL}/processes/requests`, {
            errorMessage: 'Error fetching processes requests'
        });
        expect(result).toEqual(mockProcesses);
    });
});

describe('fetchProcessByID', () => {
    it('debería obtener un proceso por ID correctamente', async () => {
        fetchWithHandlingMock.mockResolvedValue({ data: mockProcessById });

        const result = await fetchProcessByID(1);

        expect(fetchWithHandling).toHaveBeenCalledWith(`${VITE_API_URL}/processes/1`, {
            errorMessage: 'Error fetching process by ID: 1'
        });
        expect(result).toEqual(mockProcessById);
    });
});

describe('modifyProcessStatus', () => {
    it('debería modificar el estado de un proceso correctamente', async () => {
        fetchWithHandlingMock.mockResolvedValue({ data: null });

        await modifyProcessStatus(1, 'active' as ProcessStatus);

        expect(fetchWithHandling).toHaveBeenCalledWith(`${VITE_API_URL}/processes/1`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status: 'active' }),
            errorMessage: 'Error modifying process status with ID: 1'
        });
    });
});
