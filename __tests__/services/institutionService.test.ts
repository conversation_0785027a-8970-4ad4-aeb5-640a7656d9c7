import { describe, it, expect, vi, beforeEach } from 'vitest';
import * as institutionService from '../../src/services/api/institutionService';
import { fetchWithHandling } from '../../src/services/handlers/fetchHandler';
import { fetchActualProcessByInstitutionID } from '../../src/services/api/processService';
import { getEnvironments } from '../../src/helpers/envHelpers'

/**
 * Tests para el servicio `institutionService`
 * 
 * - Debería obtener todas las instituciones correctamente
 * - Debería obtener todas las instituciones con procesos actuales
 * - Debería filtrar instituciones por estado de proceso
 * - Debería devolver la institución por id del gestor
 */

const { VITE_API_URL } = getEnvironments();

vi.mock('../../src/services/handlers/fetchHandler', () => ({
    fetchWithHandling: vi.fn()
}));
vi.mock('../../src/services/api/processService', () => ({
    fetchActualProcessByInstitutionID: vi.fn()
}));

const fetchWithHandlingMock = vi.mocked(fetchWithHandling);

const mockInstitutionsResponse = {
    data: {
        institutions: [
            { id: 1, name: 'Institution 1', processes: [] },
            {
                id: 2, name: 'Institution 2', processes: [
                    { id: 101, status: 'active' }
                ]
            },
        ],
        totalPages: 1
    }
};


beforeEach(() => {
    vi.resetAllMocks();
});

describe('fetchAllInstitutions', () => {
    it('debería obtener todas las instituciones correctamente', async () => {

        fetchWithHandlingMock.mockResolvedValue(mockInstitutionsResponse);

        const result = await institutionService.fetchAllInstitutions(0);

        expect(fetchWithHandling).toHaveBeenCalledWith(`${VITE_API_URL}/institutions?page=0`, {
            errorMessage: 'Error fetching institutions',
        });

        expect(result).not.toBeNull();
        expect(result.data).not.toBeFalsy();
        expect(result.data.length).toBe(2);
    });
});


describe('fetchInstitutionByManagerID', () => {
    it('debería devolver la institución por id del gestor', async () => {
        fetchWithHandlingMock.mockResolvedValue(mockInstitutionsResponse);

        const result = await institutionService.fetchInstitutionByManagerID(1);

        expect(result).toEqual(mockInstitutionsResponse.data[0]);
    });
});

