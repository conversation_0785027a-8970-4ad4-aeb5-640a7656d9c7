import { describe, it, expect, vi, beforeEach } from 'vitest';
import { uploadEvidence, deleteEvidence, fetchEvidencesWithDimensionsByProcessID, downloadEvidence, approveEvidence, rejectEvidence } from '../../src/services/api/evidenceService';
import { fetchWithHandling } from '../../src/services/handlers/fetchHandler';
import { getEnvironments } from '../../src/helpers/envHelpers';

const { VITE_API_URL } = getEnvironments();


vi.mock('../../src/services/handlers/fetchHandler', () => ({
    fetchWithHandling: vi.fn(),
}));

vi.mock('../../src/hooks/useAuth', () => ({
    useAuth: () => ({
        getToken: vi.fn().mockReturnValue('mocked-token'),
    }),
}));

vi.mock('../../src/mappers', () => ({
    mapToDimensionWithEvidences: vi.fn().mockImplementation((dimension, evidence) => ({ dimension, evidence })),
}));

const fetchWithHandlingMock = vi.mocked(fetchWithHandling);

describe('evidenceService', () => {
    const mockFormData = new FormData();
    const mockData = { dimension: 'mockDimension', evidence: 'mockEvidence' };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('debe subir una evidencia correctamente', async () => {
        fetchWithHandlingMock.mockResolvedValue({});

        await uploadEvidence(1, 2, mockFormData);

        expect(fetchWithHandling).toHaveBeenCalledWith(
            `${VITE_API_URL}/evidences/1/2/upload`,
            expect.objectContaining({
                method: 'POST',
                body: mockFormData,
                token: 'mocked-token',
            })
        );
    });

    it('debe eliminar una evidencia correctamente', async () => {
        fetchWithHandlingMock.mockResolvedValue({});

        await deleteEvidence(3);

        expect(fetchWithHandling).toHaveBeenCalledWith(
            `${VITE_API_URL}/evidences/3`,
            expect.objectContaining({
                method: 'DELETE',
                token: 'mocked-token',
            })
        );
    });

    it('debe obtener evidencias con dimensiones por id de proceso', async () => {
        fetchWithHandlingMock.mockResolvedValue({ data: [mockData] });

        const result = await fetchEvidencesWithDimensionsByProcessID(4);

        expect(fetchWithHandling).toHaveBeenCalledWith(
            `${VITE_API_URL}/evidences/by-process/4`,
            expect.objectContaining({
                token: 'mocked-token',
            })
        );

        expect(result).toEqual([mockData]);
    });

    it('debe descargar una evidencia correctamente', async () => {
        const mockBlob = new Blob();
        fetchWithHandlingMock.mockResolvedValue(mockBlob);

        const result = await downloadEvidence(5);

        expect(fetchWithHandling).toHaveBeenCalledWith(`${VITE_API_URL}/evidences/5`,
            expect.objectContaining({
                errorMessage: 'Error fetching preview for evidence: 5',
                responseType: 'blob',
                token: 'mocked-token',
            })
        );

        expect(result).toBe(mockBlob);
    });

    it('debe aprobar una evidencia correctamente', async () => {
        fetchWithHandlingMock.mockResolvedValue({});

        await approveEvidence(6, 'Test comment');

        expect(fetchWithHandling).toHaveBeenCalledWith(
            `${VITE_API_URL}/evidences/6/accept`,
            expect.objectContaining({
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ comment: 'Test comment' }),
                token: 'mocked-token',
            })
        );
    });

    it('debe rechazar una evidencia correctamente', async () => {
        fetchWithHandlingMock.mockResolvedValue({});

        await rejectEvidence(7, 'Test comment');

        expect(fetchWithHandling).toHaveBeenCalledWith(
            `${VITE_API_URL}/evidences/7/reject`,
            expect.objectContaining({
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ comment: 'Test comment' }),
                token: 'mocked-token',
            })
        );
    });
});
