import { describe, it, expect, vi, beforeEach } from "vitest"
import { createSurvey, fetchSurveyLinkByProcessID, fetchSurveyByID } from "../../src/services/api/surveyService"
import { fetchWithHandling } from "../../src/services/handlers/fetchHandler"
import { getEnvironments } from "../../src/helpers/envHelpers"

vi.mock("../../src/hooks/useAuth", () => ({
  useAuth: () => ({
    getToken: vi.fn().mockReturnValue("mocked_token"),
  }),
}))

const { VITE_SURVEY_API_URL } = getEnvironments()

vi.mock("../../src/services/handlers/fetchHandler", () => ({
  fetchWithHandling: vi.fn(),
}))

const fetchWithHandlingMock = vi.mocked(fetchWithHandling)

describe("surveyService", () => {
  const mockSurveyUrl = "http://example.com/survey"
  const mockSurveyData = { id: 1, title: "Survey Title" }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("debe crear una encuesta correctamente y devolver la URL", async () => {
    fetchWithHandlingMock.mockResolvedValue({ surveyUrl: mockSurveyUrl })

    const result = await createSurvey(1, "Test Process")

    expect(fetchWithHandlingMock).toHaveBeenCalledWith(
      `${VITE_SURVEY_API_URL}/surveys/process/1`,
      expect.objectContaining({
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ title: "Test Process" }),
        errorMessage: "Error al crear una encuesta para el proceso con ID: 1",
        token: "mocked_token",
      }),
    )
    expect(result).toBe(mockSurveyUrl)
  })

  it("debe lanzar un error si la creación de la encuesta falla", async () => {
    fetchWithHandlingMock.mockRejectedValue(new Error("Network Error"))

    await expect(createSurvey(1, "Test Process")).rejects.toThrow("Error al crear una encuesta: Network Error")
  })

  it("debe obtener el enlace de la encuesta correctamente", async () => {
    fetchWithHandlingMock.mockResolvedValue({ data: { surveyUrl: mockSurveyUrl } })

    const result = await fetchSurveyLinkByProcessID(1)

    expect(fetchWithHandlingMock).toHaveBeenCalledWith(
      `${VITE_SURVEY_API_URL}/surveys/process/1/url`,
      expect.objectContaining({
        errorMessage: "Error fetching survey link by process ID: 1",
        token: "mocked_token",
      }),
    )

    expect(result).toBe(mockSurveyUrl)
  })

  it("debe lanzar un error si la obtención del enlace falla", async () => {
    fetchWithHandlingMock.mockRejectedValue(new Error("Network Error"))

    await expect(fetchSurveyLinkByProcessID(1)).rejects.toThrow(
      "Error al obtener el enlace de la encuesta por ID de proceso: Network Error",
    )
  })

  it("debe obtener la encuesta correctamente", async () => {
    global.fetch = vi.fn().mockResolvedValue(
      {
      ok: true,
      json: async () => mockSurveyData,
    } as Response,
    )

    const result = await fetchSurveyByID(1)

    expect(global.fetch).toHaveBeenCalledWith(`${VITE_SURVEY_API_URL}/surveys/1`)
    expect(result).toEqual(mockSurveyData)
  })

  it("debe lanzar un error si la obtención de la encuesta falla", async () => {
    global.fetch = vi.fn().mockResolvedValue(
      {
      ok: false,
    } as Response,
    )

    await expect(fetchSurveyByID(1)).rejects.toThrow("Error al obtener la encuesta por ID")
  })

  it("debe lanzar un error si hay un problema en la red al obtener la encuesta", async () => {
    global.fetch = vi.fn().mockRejectedValue(new Error("Network Error"))

    await expect(fetchSurveyByID(1)).rejects.toThrow("Error al obtener una encuesta por ID: Network Error")
  })
})
