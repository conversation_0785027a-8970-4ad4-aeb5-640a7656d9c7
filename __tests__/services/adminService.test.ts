import { describe, it, expect, beforeEach, vi } from 'vitest';
import { fetchDashboardKPIStats } from '../../src/services/api/adminService';
import type { DashboardKPIStats, KPICard } from '../../src/models/types/index';

/**
 * Tests para el servicio `adminService`
 * 
 * - Debería obtener las estadísticas del dashboard y mapearlas correctamente
 * - Debería lanzar un error si fetch falla
 */

vi.stubGlobal('fetch', vi.fn());

const fetchMock = vi.mocked(fetch);

const mockKPIStats: DashboardKPIStats = {
    numberOfInstitutions: 10,
    numberOfAuditors: 5,
    totalCompletedAudits: 50,
    processesInProgress: 3,
    peopleSurveyed: 100
};

const mockKPICards: KPICard[] = [
    {
        title: "Instituciones",
        value: "10",
        icon: "fas fa-building",
        color: "#4E36E2",
    },
    {
        title: "Auditores",
        value: "5",
        icon: "fas fa-user-tie",
        color: "#48A9F8",
    },
    {
        title: "Auditorias Realizadas",
        value: "50",
        icon: 'fas fa-clipboard-check',
        color: '#2AA88A'
    },
    {
        title: 'Procesos en curso',
        value: "3",
        icon: 'fas fa-spinner',
        color: '#8BC740'
    },
    {
        title: 'Personas Encuestadas',
        value: "100",
        icon: 'fas fa-people-group',
        color: '#3666E2'
    }
];

beforeEach(() => {
    vi.resetAllMocks();
});

describe('fetchDashboardKPIStats', () => {
    it('debería obtener las estadísticas del dashboard y mapearlas correctamente', async () => {
        fetchMock.mockResolvedValue({
            ok: true,
            json: async () => ({ data: mockKPIStats }),
        } as Response);

        const result = await fetchDashboardKPIStats();

        expect(result).toEqual(mockKPICards);
    });

    it('debería lanzar un error si fetch falla', async () => {
        fetchMock.mockResolvedValue({
            ok: false,
            json: async () => ({ errorCode: '500', message: 'Internal Server Error' }),
        } as Response);

        await expect(fetchDashboardKPIStats()).rejects.toThrow('Error fetching dashboard stats: 500: Internal Server Error');
    });
});
