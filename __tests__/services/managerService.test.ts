import { describe, it, expect, beforeEach, vi } from 'vitest';
import { createManager } from '../../src/services/api/managerService';
import { fetchWithHandling } from '../../src/services/handlers/fetchHandler';
import type { Gestor } from '../../src/models/types';
import { GestorFormSchema } from '../../src/models/validation/formSchemas';
import { getEnvironments } from '../../src/helpers/envHelpers'

/**
 * Tests para el servicio `gestorService`
 * 
 * - Debería crear un gestor correctamente
 * - Debería lanzar un error si fetch falla
 */

const { VITE_USER_API_URL } = getEnvironments();

vi.mock('../../src/services/handlers/fetchHandler', () => ({
    fetchWithHandling: vi.fn()
}));

const fetchWithHandlingMock = vi.mocked(fetchWithHandling);


const newGestor = { username: 'New Gestor' } as GestorFormSchema;
const createdGestor = { id: 1, username: 'New Gestor', userRole: "MANAGER" } as Gestor;

beforeEach(() => {
    vi.resetAllMocks();
});

describe('createGestor', () => {
    it('debería crear un gestor correctamente', async () => {
        fetchWithHandlingMock.mockResolvedValue({ data: createdGestor });

        const result = await createManager(newGestor);

        expect(fetchWithHandling).toHaveBeenCalledWith(`${VITE_USER_API_URL}/managers`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(newGestor),
            errorMessage: 'Error creating gestor'
        });
        expect(result).toEqual(createdGestor);
    });
});
