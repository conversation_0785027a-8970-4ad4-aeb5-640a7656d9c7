import { describe, it, expect, beforeEach, vi } from 'vitest';
import { fetchAllMaturityModels } from '../../src/services/api/maturityModelService';
import type { MaturityModel } from '../../src/models/types/maturityModel';
import { fetchWithHandling } from '../../src/services/handlers/fetchHandler';
import { getEnvironments } from '../../src/helpers/envHelpers'

/**
 * Tests para el servicio `maturityModelService`
 * 
 * - Debería obtener todos los modelos de madurez correctamente
 * - Debería lanzar un error si fetchWithHandling falla
 */

const { VITE_API_URL } = getEnvironments();

vi.mock('../../src/services/handlers/fetchHandler', () => ({
    fetchWithHandling: vi.fn(),
}));

const fetchWithHandlingMock = vi.mocked(fetchWithHandling);

const mockMaturityModels: MaturityModel[] = [
    { id: 1, name: 'Model A', description: 'Description for Model A', timeLimit: 1234 },
    { id: 2, name: 'Model B', description: 'Description for Model B', timeLimit: 5678 },
];

beforeEach(() => {
    vi.resetAllMocks();
});

describe('fetchAllMaturityModels', () => {
    it('debería obtener todos los modelos de madurez correctamente', async () => {
        fetchWithHandlingMock.mockResolvedValue({
            data: {
                maturityModels: mockMaturityModels
            }
        });

        const result = await fetchAllMaturityModels();

        expect(fetchWithHandlingMock).toHaveBeenCalledWith(`${VITE_API_URL}/maturity-models?page=0`, {
            errorMessage: 'Error fetching maturity models'
        });
        expect(result.data).toEqual(mockMaturityModels);
    });

    it('debería lanzar un error si fetchWithHandling falla', async () => {
        fetchWithHandlingMock.mockRejectedValue(new Error('Error fetching maturity models: Network error'));

        await expect(fetchAllMaturityModels()).rejects.toThrow('Error fetching maturity models: Network error');
    });
});
