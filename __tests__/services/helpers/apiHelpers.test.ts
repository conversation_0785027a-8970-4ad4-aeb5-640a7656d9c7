import { describe, it, expect } from 'vitest';
import { buildQueryParams } from '../../../src/services/api/helpers/apiHelpers';

describe('API Helpers', () => {
    describe('buildQueryParams', () => {
        it('should build a query string with valid parameters', () => {
            const params = {
                search: 'test',
                page: 2,
                limit: 10,
            };
            const result = buildQueryParams(params);
            expect(result).toBe('search=test&page=2&limit=10');
        });

        it('should ignore undefined, null, and empty string values', () => {
            const params = {
                search: 'test',
                page: undefined,
                limit: 10,
                sort: null,
                filter: '',
            };
            const result = buildQueryParams(params);
            expect(result).toBe('search=test&limit=10');
        });

        it('should return an empty string when all values are undefined, null, or empty', () => {
            const params = {
                search: undefined,
                page: null,
                limit: '',
            };
            const result = buildQueryParams(params);
            expect(result).toBe('');
        });

        it('should handle parameters with special characters', () => {
            const params = {
                search: 'hello world',
                filter: 'name:asc',
            };
            const result = buildQueryParams(params);
            expect(result).toBe('search=hello+world&filter=name%3Aasc');
        });
    });
});