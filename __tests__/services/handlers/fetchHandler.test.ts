import { describe, it, expect, vi, beforeEach } from 'vitest';
import { fetchWithHandling } from '../../../src/services/handlers/fetchHandler';

/**
 * Tests para el manejador `fetchHandler`
 * 
 * - Debería retornar datos correctamente cuando la respuesta es exitosa
 */

describe('fetchWithHandling', () => {
    beforeEach(() => {
        vi.resetAllMocks();
    });

    it('debería retornar datos correctamente cuando la respuesta es exitosa', async () => {
        const mockResponse = {
            ok: true,
            json: vi.fn().mockResolvedValue({ data: 'test data' }),
        } as unknown as Response;

        global.fetch = vi.fn().mockResolvedValue(mockResponse);

        const result = await fetchWithHandling('http://localhost:8080/test-url');

        expect(fetch).toHaveBeenCalledWith('http://localhost:8080/test-url', expect.objectContaining({
            headers: expect.any(Headers),
        }));
        expect(result).toEqual({ data: 'test data' });
    });

});