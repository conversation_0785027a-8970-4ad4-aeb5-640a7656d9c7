import { describe, it, expect, vi } from 'vitest';
import { handleFetchError } from '../../../src/services/handlers/errorHandler';

/**
 * Tests para el handler `handleFetchError`
 * 
 * - Debería lanzar un error con el mensaje completo cuando se recibe un error del servidor
 * - Debería lanzar un error con el mensaje del servidor cuando no se proporciona un mensaje de error personalizado
 * - Debería lanzar un error con un mensaje desconocido cuando la respuesta del servidor no contiene errorCode 
 */

describe('handleFetchError', () => {
    it('debería lanzar un error con el mensaje completo cuando se recibe un error del servidor', async () => {
        const mockResponse = {
            json: vi.fn().mockResolvedValue({
                status: 500,
                errorCode: 'SERVER_ERROR',
                message: 'Internal Server Error',
                timestamp: new Date().toISOString()
            }),
            ok: false,
        } as unknown as Response;

        const errorMessage = 'Custom error message';

        await expect(handleFetchError(mockResponse, errorMessage)).rejects.toThrow(
            `${errorMessage}: SERVER_ERROR: Internal Server Error`
        );
    });

    it('debería lanzar un error con el mensaje del servidor cuando no se proporciona un mensaje de error personalizado', async () => {
        const mockResponse = {
            json: vi.fn().mockResolvedValue({
                status: 404,
                errorCode: 'NOT_FOUND',
                message: 'Resource not found',
                timestamp: new Date().toISOString()
            }),
            ok: false,
        } as unknown as Response;

        await expect(handleFetchError(mockResponse)).rejects.toThrow(
            'NOT_FOUND: Resource not found'
        );
    });

    it('debería lanzar un error con un mensaje desconocido cuando la respuesta del servidor no contiene errorCode', async () => {
        const mockResponse = {
            json: vi.fn().mockResolvedValue({
                status: 400,
                message: 'Bad Request',
                timestamp: new Date().toISOString()
            }),
            ok: false,
        } as unknown as Response;

        await expect(handleFetchError(mockResponse)).rejects.toThrow(
            'Unknown error'
        );
    });
});
