## [1.2.0-dev.6](https://github.com/tutransparencia/tutransparencia-ui-vue/compare/v1.2.0-dev.5...v1.2.0-dev.6) (2025-06-26)

### Features

* **employees:** add rawEmployees reference and clear employees functionality ([a430f0d](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a430f0dfb3b83e04c19765f71e8f837f1220e89b))

### Bug Fixes

* **employee:** Fix employees to send array instead of excel ([5e2a766](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/5e2a766c5427839ad910f55773855e3f23f8a067))
* **uploadEmployee:** prevent Excel upload from overwriting manually added employees ([cd7761c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/cd7761cf40ed4c3534045e219de1c0b747b22e8b))
* **uploadEmployees:** fixed function to upload employees with excel and manually ([87103f6](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/87103f624421a08f672fbbbe75828fa0e7a9f89e))

## [1.2.0-dev.5](https://github.com/tutransparencia/tutransparencia-ui-vue/compare/v1.2.0-dev.4...v1.2.0-dev.5) (2025-06-20)

### Bug Fixes

* **survey-progress-chart:** update isTodayBeforeEndDate function to include today in date comparison ([488b644](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/488b64458a72ca9f102785536b711b46ab18599e))

## [1.2.0-dev.4](https://github.com/tutransparencia/tutransparencia-ui-vue/compare/v1.2.0-dev.3...v1.2.0-dev.4) (2025-06-20)

### Bug Fixes

* **survey-progress-chart:** improve date comparison logic in isTodayBeforeEndDate function ([427ac82](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/427ac82d4349f2ab890957bbb7648668099f8f02))
* **survey-progress-chart:** update active state checks to include date validation ([8cf5a1d](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/8cf5a1d3e8532657edc73be8e6444de278f15d28))

## [1.2.0-dev.3](https://github.com/tutransparencia/tutransparencia-ui-vue/compare/v1.2.0-dev.2...v1.2.0-dev.3) (2025-06-12)

### Bug Fixes

* **process-steps:** add fetchRecommendations to useProcessSteps and call it on mount ([2d86ffe](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/2d86ffe56161f58cdb21ad335d11d39356de9408))

## [1.2.0-dev.2](https://github.com/tutransparencia/tutransparencia-ui-vue/compare/v1.2.0-dev.1...v1.2.0-dev.2) (2025-06-12)

### Bug Fixes

* **gestor:** update condition to display loading section based on process start date ([343d03a](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/343d03a7be1815e3f6c867f389d38c68f22d1eb6))
* **gestor:** update process description and enhance routing with minStartDate query ([4180a4b](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/4180a4b740b66c1897c7e075f6dca0a5ada72422))

## [1.2.0-dev.1](https://github.com/tutransparencia/tutransparencia-ui-vue/compare/v1.1.2-dev.1...v1.2.0-dev.1) (2025-06-11)

### Features

* **audit:** add auditing process functionality and update related components ([9e2ca05](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/9e2ca05510ae4141623903af3df4afbe6f452018))
* **date-range-picker:** add minDate prop and update date formatting logic ([131af98](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/131af9899388994054e17519bb4ffaa79fc8155e))
* **process-steps:** update process step titles and add step state logic ([bfa9a21](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/bfa9a21bf417ceecbbac1107a32b64deb38472c6))

## [1.1.2-dev.1](https://github.com/tutransparencia/tutransparencia-ui-vue/compare/v1.1.1...v1.1.2-dev.1) (2025-06-07)

### Bug Fixes

* **admin:** improve notification UI and prevent process selection for institutions without processes ([a6979fd](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a6979fd3f3bb4dcf025b1f8a0ff58addd3880b27))
* **gestor:** fix typos like "contiunar" and "ya se a" ([b349301](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/b349301649e6231f4960d07daab7badaa5da62e0))
* **progress-chart:** show "El proceso termina hoy" when daysLeft is 0 ([3742a2c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/3742a2c63dbca06ba9359762874fbf2c404ca964))
* **progress-chart:** sort data by date to fix rendering bug ([c5c63be](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c5c63be4b54d9ad23fde7ff75d9b80a241f18d08))

## [1.1.2-rc.1](https://github.com/tutransparencia/tutransparencia-ui-vue/compare/v1.1.1...v1.1.2-rc.1) (2025-05-28)

### Bug Fixes

* **admin:** improve notification UI and prevent process selection for institutions without processes ([a6979fd](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a6979fd3f3bb4dcf025b1f8a0ff58addd3880b27))
* **gestor:** fix typos like "contiunar" and "ya se a" ([b349301](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/b349301649e6231f4960d07daab7badaa5da62e0))
* **progress-chart:** show "El proceso termina hoy" when daysLeft is 0 ([3742a2c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/3742a2c63dbca06ba9359762874fbf2c404ca964))
* **progress-chart:** sort data by date to fix rendering bug ([c5c63be](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c5c63be4b54d9ad23fde7ff75d9b80a241f18d08))

## [1.1.1-rc.2](https://github.com/tutransparencia/tutransparencia-ui-vue/compare/v1.1.1-rc.1...v1.1.1-rc.2) (2025-05-27)

### Bug Fixes

* **admin:** improve notification UI and prevent process selection for institutions without processes ([a6979fd](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a6979fd3f3bb4dcf025b1f8a0ff58addd3880b27))
* **gestor:** fix typos like "contiunar" and "ya se a" ([b349301](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/b349301649e6231f4960d07daab7badaa5da62e0))
* **progress-chart:** show "El proceso termina hoy" when daysLeft is 0 ([3742a2c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/3742a2c63dbca06ba9359762874fbf2c404ca964))
* **progress-chart:** sort data by date to fix rendering bug ([c5c63be](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c5c63be4b54d9ad23fde7ff75d9b80a241f18d08))

## [1.1.1-rc.1](https://github.com/tutransparencia/tutransparencia-ui-vue/compare/v1.1.0...v1.1.1-rc.1) (2025-05-18)

### Bug Fixes

* **CI/CD:** :green_heart: Fixed API urls ([495c383](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/495c383bdcb8e5eeae74c3b7cf813d31214f156c))
* date-format ([f17acfd](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/f17acfde5f98081922e0ceb255ec7d7a8afdc201))
* use convertUTCToLocalNaiveDate in formatDateToDDMMYYYY ([fca0489](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/fca04894001b9809d3df7dfaedeffc01069273fa))

## [1.1.0-rc.1](https://github.com/tutransparencia/tutransparencia-ui-vue/compare/v1.0.1...v1.1.0-rc.1) (2025-05-10)

### Features

* :construction_worker: Added logic to pre-release and release in pipeline ([5a4ccf0](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/5a4ccf04a2cfaca96a334db5d0a5fd9c7ccaf4f4))

## [1.0.1](https://github.com/tutransparencia/tutransparencia-ui-vue/compare/v1.0.0...v1.0.1) (2025-05-04)

### Bug Fixes

* **CI/CD:** :green_heart: Now frontend uses env in build time ([2c97838](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/2c97838843125d6f9ad6f52daf762a9a5ae62ec4))

## 1.0.0 (2025-04-27)

### Features

* :lipstick: Layout adjusment for last update text ([a714d33](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a714d331d766a673d58c3f30298ca4ce16d35bd3))
* **ActualGraphic:** add new feature ([627381c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/627381c14af00622b330ad53ae020ebee9444889))
* add accordion functionality for questions ([e28894a](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/e28894a08c6097a1431cb342af439c37100807b0))
* add close button to all modals ([d2d082f](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/d2d082f8e3f356167c9fea31bdca1cd891feef96))
* add configure tailwind ([3e47ec0](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/3e47ec07486aec21b42c523f71af9ad0cd948b5d))
* add error handling with notification ([250c1bd](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/250c1bd173baa8a77bfdfba06804ec95b0cbf051))
* add exception handling and fix views ([21574c4](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/21574c49775765d1742210e55453b8a00a43a758))
* add JSON data files for institutions and questions ([863b4ef](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/863b4ef6a3b11708aea3f39aff6f3eed2013b575))
* add logs ([937d44a](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/937d44ada20b6de97b84b2b1f329ae407835e4fc))
* add milestone data to hitos.json ([d9a0a5c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/d9a0a5c4757dd552b723fe45187b85cac6b18eea))
* add new cards component ([b73e84c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/b73e84c8dd7693aedbdb84781e6e529a1d9956b3))
* add password visibility toggle in DisableManagerModal component ([ae3d27b](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/ae3d27b54519245442dbbf5b11d9136fe71bb975))
* add questions data to preguntas.json ([a9b350e](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a9b350eed1f3832f0af9a3132b44280e36937d73))
* add reactivity to system ([6896bc7](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/6896bc7f737f64631d921dc77cbf8bf5b6c347c2))
* add skeleton loading for DashboardPage component ([1182f6f](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/1182f6faf4560654d6d9cb75cf39544323126167))
* Add xlsx library for Excel file manipulation ([7011cbe](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/7011cbeab4bd605c910df7fff513f1d2327b910d))
* added EsLint and Prettier ([31d6e44](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/31d6e442c5a087476a2d5634ed28852380c603ff))
* **AddEvidence:** add new view for adding evidence ([939416c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/939416cfb5f93c10478039ba5adf21d976692eb8))
* **admin:** add model and request views ([e8f5c6d](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/e8f5c6d709a092e20627b5a229f63559f0e6cadd))
* **admin:** Create views, components, and styles for institutional admin domain ([a266b71](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a266b712a9383b45b0f351c51c376dddea5ddce0))
* **api:** add Process, Survey, Institution, and Admin services ([bebe11c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/bebe11c4e7c50244e58f58e94368493449516d89))
* **api:** separate APIs into users, results, and institutions, and update .env routes ([b5828b6](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/b5828b675c6ae08b1f6a8c1f99994acd2325dd0b))
* **async-load:** implement Suspense for handling asynchronous component loading ([f82ce67](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/f82ce67bc11df4559b019d83c03f35b2f154d6fe))
* **auditor-crud:** add auditor creation with API integration ([a05adcb](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a05adcb68f8403414df8e1cab3e6b51b9c12b937))
* **auditor-picker:** implement auditor search in AuditorPicker component ([7df716a](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/7df716a9c40999e90df5afcb0706f57195cbcd87))
* **auditor-picker:** stylize AuditorPicker as a search bar ([d075261](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/d07526174aadc2cfd555a02f049e691b8717cb2c))
* **auditor/appeal:** add appeal flow with mock data for auditor ([418c79e](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/418c79e6bed1e137b48821ae8c435551854f5a39))
* **auditor:** add audit count to type, card, and tile ([1d073b4](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/1d073b460acacb452fbac4c955d9c76ffb82f255))
* **auditor:** update and enhance the entire auditing process ([216af55](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/216af559409d47fbc8e4014cfe98495af58dd486))
* **auditor:** update routes in auditorRoute ([a2e20bb](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a2e20bb6f96f0bcd9aab461e7beb636fa72e8495))
* **auth:** add configuration button for authenticated users ([fc289e0](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/fc289e048ef9b8076f5f7a41f28e0578a55cea8d))
* **auth:** add email spam reminder message ([e23bf65](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/e23bf65521548e7001c74ca8f6eee0934ccdd14f))
* **auth:** add loading state to authentication buttons ([f032dc6](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/f032dc6145aecb47afed1124c1dbbb4454a8666d))
* **auth:** add redirection for user to login page when token has expired ([cef5911](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/cef5911947a5e20b397ea80203a02b972274933b))
* **auth:** implement authentication ([36263ec](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/36263ec55825e4f54f67f5a2f070c1422801b008))
* **auth:** implement password recovery functionality ([dbba60b](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/dbba60bbd0f9c0d0c1a2617ae51e4dabba14bdb7))
* **auth:** remover opción de "Olvidé mi contraseña" ([c8b696b](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c8b696b4cedadce1a66ba4967496a6394a13bfc0))
* change of view in dashboard for finished process. ([75a5eeb](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/75a5eeb5a6a4f13247c1585a8e81fb37691c998d))
* **components:** create reusable button, card, and textSection component ([766d503](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/766d50303d8334f5080f521961330545c967f892))
* **ConditionCard:** add condition card component ([23b9b66](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/23b9b6681900532fd1fd77eb51e514d8fb37bdcf))
* Connects format download component to backend endpoint ([d1ddcdc](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/d1ddcdce0204a70694a9a5f099da7619b3eab336))
* **consent:** implement function to store process consent ([d468e31](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/d468e3118d84f09fd123bda1197bd790d9280caa))
* correction responsive skeleton ([1f610e5](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/1f610e5a8b1c85aa581e282d5f9b7b51361cf7eb))
* create necessary services and interfaces ([d57837e](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/d57837e1659e9867c59d7c893b9bf12f011dc186))
* Create processData JSON file ([0311323](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/03113235c9bb3601e75565f50ba126a4d5c03f88))
* Create PullRequest.yml ([122e0d9](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/122e0d9801587e3e76b5a11f99d94bbc3a4ded7b))
* **create-institution:** Add institution creation and API integration ([498cd94](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/498cd94ca140fb2cae3bf0ae28ed9857e4ff2f42))
* **dashboard, actual-process, recommendations:** create Manager Dashboard, add current process view, initiate recommendations view ([2256be3](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/2256be3075eb9b4d33cc20d7f647f654a201d737))
* **dashboard:** add dashboard component with multiple charts ([36f978f](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/36f978f7a8e6bd56cdf65fc5ef58452d29cba195))
* **dashboard:** add skeleton loading for dashboard gestor ([84cadb6](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/84cadb6e9da023e0ad55a5958b110343074379d8))
* **DashboardGestorPage:** Update Views for Data Loading ([d502385](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/d50238577959b7f2a4461a43fa1688238504a027))
* **dashboard:** integrate data into dashboard view ([a8013df](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a8013df12340195bc229722186122cc1e5712cce))
* **data:** Add JSON files to simulate user and process data ([63f0d41](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/63f0d41edeb669e54fc67ec5cf31f27b5bcd9838))
* **DataRange:** Allow setting of process start date ([07bf762](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/07bf7626e5330e587cc3a418767701d77ea3e84c))
* **DataRange:** Allow setting of process start date ([60ac65a](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/60ac65a47445df3d3e730354e142535f428edd42))
* **date-sort:** Add date sort in ViewOptionsToggle component. ([2cc18e8](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/2cc18e8a13533c4a89b779531f922e1753d34e9b))
* **date:** calculate elapsed and remaining days ([c4233c2](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c4233c2d5ccf1a7a7c70d902cbc73110c4a443c5))
* Dockerfile added ([b4f56cb](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/b4f56cb3142cb57cc41d86094b89af1e15ad61cc))
* **employees:** added enumeration for uploading and displaying employees ([3bf1ae8](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/3bf1ae86cf9d1713c1424620c63b0e84aa6d6bae))
* **employees:** added PDF generation for current process employees using jsPDF ([00f2534](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/00f2534a17856aa46af3985291821bc5b46e8813))
* **env:** add environment variables for API requests ([f002755](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/f0027558c7ae09982a641299f756f19c44edf89c))
* **error:** update error in lint ([ad60733](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/ad60733a9245efdb74ef47d847b7182bf608de97))
* **evidences:** Add evidence upload view following Figma design and flow ([fd8fd2b](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/fd8fd2b4329c11faa56da1c20ed29b981d1facbf))
* **evidences:** add functionality for auditors to audit evidences ([4c42cbe](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/4c42cbed1e48b8e24e15489f6b5d957263f20425))
* **evidences:** implement evidence upload flow for the manager ([50af40c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/50af40c91ebb5eed6f34a344cdcae2f07b27756b))
* filter processes by unread status ([62cf35e](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/62cf35e78a51eced4090666c1ee85a2e74e7088a))
* fix csv format and imports ([3e25491](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/3e25491f16f0e031d4125b18f222e2ca87312598))
* **form:** Add combobox for region and city selection using local JSON as data source ([6c87f06](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/6c87f06d52d32cfb620ed7779eb46e66eb5e63a0))
* **form:** Add combobox for region and city selection using local JSON as data source ([18fbdd9](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/18fbdd9b4ca5045e74361213caa0f71ceac82ed1))
* **form:** implement input validations and enhance UI for better user. ([538823f](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/538823fdd7ac9b7a2ce1b38de030d2e275ff9ae6))
* **form:** remove automatic formatting of phone number ([a0d5aa5](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a0d5aa5e4c473857fbd97a63dd2fba2b167f2490))
* **form:** remove automatic formatting of phone number ([c0f4610](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c0f4610e4fa2f2bc32c2c27d0872ef0a161496c2))
* **forms:** add comparison validations when updating records ([0f08d1c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/0f08d1c82970826c714aec0cda26b70a5ddc3047))
* generic components were created for the application ([f6f30c0](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/f6f30c0b1beb32fc63e04083504564efe1aa4a9a))
* **gestionEvidence:** update evidene gestor ([5125adc](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/5125adc5da67af011a1606e1a436b9d53aa27abd))
* **gestor/request-process:** add available (request) process page to 'Nuevo Proceso' section with nested routing ([550ec64](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/550ec64819bb4fe74c300bfd3c0d9a511fd61d08))
* **GestorDashboard:** add reusable loading component ([e970bc7](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/e970bc7e364d8c7864c52bc3bfb70d2294546e12))
* **GestorDashboard:** make dashboard responsive ([58627eb](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/58627eb095447adf38590b50891b1966f83539bb))
* **GestorDashboardPage:** update totalAnswers ([5c44ff7](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/5c44ff7a7c27d9f7f9d711452b23f2765cf36119))
* **GestorForm:** add toggle to show/hide password field ([d2d397c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/d2d397c6c657aee3659ac711c91904f8fbaede91))
* **headerComponent:** Notifications components are created. ([a2d8868](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a2d8868b0dc6e1e95174a542aa3a890fd9614b94))
* **history modal:** add legend for process chart visualization requirements ([5698ec2](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/5698ec26f5b9380fe96a80e352210cd6d8446b3c))
* implement functional search bar ([d49cbfb](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/d49cbfb92efb36dc511c3c6e4487654e49899a5c))
* Implement historicTable table ([7db95fb](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/7db95fbb9e5bd0904d209be38f6bf1abb2718ba5))
* Implement historyView ([3b0a6df](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/3b0a6df1d3c30719674a344bc096a57501ea1c47))
* implement logger ([5f3cf90](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/5f3cf906a870c0ccfe416f6a84acc118208aa59d))
* implement objections section ([5fafe74](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/5fafe748758f517f085cd07f12203724dea2aaa3))
* implement pagination and accordion toggle in EmployeesAccordion component ([d23cdd7](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/d23cdd72bbd85edda63b01a48fac7f10187baadb))
* implement, create and add services hooks ([f2b097c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/f2b097c7bcad6317794d119cb6fe44cb58b8b03d))
* Implementation of first store use and change of layout ([0a90765](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/0a90765cbf0dcb7c89260623d8f490d60a9c201b))
* implemented view of new process ([60186c2](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/60186c2b08db619a3c89edd1267494847ee2dfd1))
* implements logs ([2b087eb](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/2b087ebde476dc0b12306fb152f220e8fede13e9))
* improve form responsiveness ([2e5ba15](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/2e5ba15cbd692537501b7dca80703479e94f3717))
* improve form responsiveness ([5e2a3cf](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/5e2a3cfa39a0fd11bea70e47243740d2ae427228))
* initialize new branch and add initial components ([ea20c30](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/ea20c301a978c582379937d502687236e2514edb))
* Inplement EmployyesAccordion in ProcessApprovalTab ([a0bc839](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a0bc8399e0af46594f0d7b47283e73d3a5af2ce4))
* **input:** add disabled mode to Input component ([ce74597](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/ce74597167bd1ed4fa4458d17d394c1ea80df7c3))
* **input:** Updated input to handle multiple variants ([e8a7167](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/e8a7167762538a24ae6b9d13a272ad0bb0367e3e))
* **institution:** refactor plural and singular forms to use formatLabel helper ([62f56a4](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/62f56a4c4f650dec0882249fca7dcf192d81bc4c))
* **institutions-crud:** add Institution update and Auditor assignment features ([0cb5dce](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/0cb5dce6a4587cb34972ff402aebadd73518817a))
* **institutions:** add method to get institution by associated manager ID (service & hook) ([52ceda9](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/52ceda96808567b61afe5546699d942da8504578))
* integracion de StyleLint ([361d2f4](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/****************************************))
* **integration:** start backend API integration ([c84e08c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c84e08c4d6577fbcd78edfe7c16f497d945bd252))
* **integration:** start backend API integration ([c359cc8](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c359cc83ae4172ed11f30e05ee76b75693e38d26))
* **join:** implement join page view and components ([c80eca3](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c80eca31ca11a9e7ca05ca48932a86312edd2f35))
* **landing:** Add manual download functionality and integration ([497aebd](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/497aebd8ef5fd35c690e63daa4e0137d30fa6238))
* **landing:** add manuals download functionality ([1c9d1fe](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/1c9d1fe073c8974d361377a191e5d939f720c3ea))
* **landing:** improve landing page layout and design ([c2729bc](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c2729bcadd0f26d0aeba37e8473d530feb019881))
* **LandingPage:** implement landing page view and components ([f763842](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/f763842276297615903c284ef39008e4742f0312))
* **layout:** implement layout and associated components ([99c58ff](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/99c58ff9b5847ab231f3cf50523ea05b51f60c35))
* **loading:** add more skeletons to handle async component loading with Suspense ([9a1d9c2](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/9a1d9c2db89b280a46249b6b40e7195f68e0f73d))
* **loading:** add skeletons for user feedback during loading ([1506687](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/1506687ead14ee10a4ff2b7a66e1e6528055819a))
* **loading:** create hook to handle generic async request loading ([dcce64f](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/dcce64fdd2c6145d55675cc5bda6602caf1147b4))
* **login:** create and implement login functionality ([d345707](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/d34570781857eaa6af1edb24f782eecbb92bb447))
* **login:** implement login page view and components ([70b55e5](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/70b55e566afbfc90b8b0f7baafb5bc61e160ea12))
* **manager-dashboard:** adjust conditions for displaying the download PDF button for employees ([d837ec7](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/d837ec7ccc1c919066eb9072a470c21db755b80b))
* **manager-dashboard:** adjust visibility of recommendations for manager only (not for admin) ([590c8e2](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/590c8e2509d1c0f47ac91ad79a0418a461847e45))
* **manager:** add SimpleInstitutionDTO when logging in as manager to reduce backend requests ([0d50c6d](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/0d50c6d6d1eca29b1a7cd5edb353c67cf0c856b3))
* **maturity:** create and integrate JSON for maturity levels ([91477fd](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/91477fdc9fee51cc3699522f0b45c1757712fe60))
* **milestones:** implement milestones on InProgress page for auditor (experimental) ([37e1812](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/37e18127038f6263295f1f9afa04850eb014a115))
* **ModalEmployee:** add new modal component for employee ([47e31ca](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/47e31ca1b742f0b8ac5e26b23476ff8b80d21a7c))
* **models:** add type model ([85112ea](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/85112ea26fd4d687b55c5bc08b61d4fe9cbf41a8))
* **modeluploader:** add confirm and cancel buttons ([bbb70f5](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/bbb70f5f731c4afdcf047ba11f728af842573540))
* **module-admin:** add multiple components ([e37213c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/e37213ce4036e1f2d6b64e7840723d3f06cf031c))
* **NavBar:** update navBar ([34fb36c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/34fb36c54066a0fb6d82f70c7877a298b2b9f364))
* **NewActualProcess; RequestProcess:** visual fixes and bootstrap implementation ([b570532](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/b5705323aeb68c07f30d1896255977e3e651962c))
* **NewActualProcess:** add new components for this view ([67dbc74](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/67dbc742a7a83addc8e8565735a284d03bcd0e31))
* **notification:** integrate notification component ([5bfde8a](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/5bfde8ad5d2d5f13417d3a461e7ddfb71a7676dd))
* **organisms:** component is created for modal that adds users ([ae12384](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/ae12384f835f82e6035bc585b43e009fe10cb7ad))
* placeholders added when views don't have any data ([17051ab](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/17051ab3f5c53b75dda1aa18ee6b952ca478852e))
* **process-appeal:** implement process appeal flow ([d5d43e3](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/d5d43e303e58e78d9e7d3f9de82a5de98b624bb2))
* **process-history:** add summary of old process ([b85afd7](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/b85afd71183e9f3f203101a2ab385a489b45ac54))
* **process-request:** add date field to process request submission ([d8c0b00](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/d8c0b00745f8b1dde2cd0c6f9c32a75ab9ce0d51))
* **process:** add IN_PROGRESS process visualization and improve admin charts ([8cf5357](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/8cf5357a57295409bf8a6940d1a0677091739d6a))
* **process:** implement request process functionality ([9869315](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/986931503bcf6de3e9d329899a8a8408bbe0fa15))
* **ProcessRequest:** implement accept process functionality ([d851707](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/d8517073b71f5e20e9b31e45afb29d538c160647))
* **profile-sales:** implement profile sales and related integrations ([b08a749](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/b08a7499a3bd1dfb5f91384a082c73a7a0cbf9bd))
* **profile:** enable profile editing for all roles ([9ea7731](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/9ea7731202512d1a7d4fef42a12705c0956aa678))
* **public:** update JSON files ([fea80bc](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/fea80bc643c6ee5c8e9da27ef89769252b00a832))
* **recommendations:** add recommendations for processes ([a2b6737](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a2b6737bff0691a6b828ea34e8973b6c6b4fd0d6))
* refactor components according to Figma design ([c4148cc](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c4148cc29bcd43a972c1bae61794aea0bb109d73))
* **router:** add navigation guards ([f99c204](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/f99c2045725d4a2a17dc1e06c4ff3528a15502c5))
* **router:** add new routes for application navigation ([7c61ea6](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/7c61ea6ec975c4a453ad39b80957a22fbf4c610e))
* **router:** add redirection to /login when user is unauthenticated and page does not exist ([d756c63](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/d756c631fa6f0b62eb77015051bcff9c79fe9911))
* **routes:** add new routes for evidence and process ([c606a15](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c606a15f646b0457c292ca66bfe1c9e61639ceaf))
* **routes:** implement routing and remove unused component ([cbb48b5](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/cbb48b50c6ba3289eda8a8e106e6c9e028c7d2b6))
* **routing:** implement KeepAlive to persist components on navigation and prevent repeated backend requests ([31c281a](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/31c281aba0d39b9e71aa471d9eb854c51760133f))
* **search:** implement backend search for auditors, institutions, and processes ([efbb3e9](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/efbb3e95c4d2a2bb41aa965ab1f4828112db1d88))
* semantic-release added ([70c7274](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/70c72748f05124b8466091e3a1b90b7ff0f7e720))
* **service:** create card_services ([ef0cc76](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/ef0cc76dc4122624a774523c908c0352dbe4871b))
* **services:** add add_employee_service ([9c9c0b1](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/9c9c0b1961569617c477a2fb41890d3bd9f4f5b5))
* **services:** add excel_service ([eb6ef41](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/eb6ef4165a9bffafdf0d5f5fb4df1e898ab57c4d))
* **services:** add modal_service ([e1c6657](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/e1c66575175e30bb3d8dc51a87454a88144d2171))
* sonar analyze ([274955c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/274955c5bde5571b318d4f4845d6bc6ed9f05000))
* **styles:** add new stylesheets for improved UI ([c6afed1](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c6afed1f49ba81f0de550c1879df5d6d5d38c588))
* **survey-link:** Implement copy survey link functionality for current process by manager ([b840d91](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/b840d9199c43d2327f4dfdce5f7395eec8ce59ed))
* **Survey:** implement terms and conditions ([ac70f69](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/ac70f69b8038f3a876c6f3353d27976c67b949c7))
* **survey:** integrate survey views with main frontend ([db2b5a3](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/db2b5a37c981df271a6d85e41bcc3cfae4119e70))
* **TableEmployee:** deleted sections in TableEmployee ([95f893d](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/95f893d79f9c80bc7bfde3f75853d61d9a8c653c))
* **TableEmployee:** update table component to separate services ([715baf7](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/715baf75fcfc2d1e8ca845a9ef8e41dddc7b9e9d))
* **tailwind, readme:** fix tailwind colors and update readme ([d0590d0](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/d0590d0f51a259608f1b00adaa79cc0ac27a8b1e))
* **tests:** increase coverage for SonarQube ([5823781](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/582378190e812d7403a152545246c0558a1abb15))
* **ui:** add icon and fix tailwind configuration ([c52e2b7](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c52e2b7c14eed8354738de91a6f921b6a93e0e06))
* **ui:** add new views for GestorReport and FinishProcess ([e26d067](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/e26d067c40493700cef91c5d1709d2ded0fd9f0f))
* **ui:** Added toasts (vue-toast-notification) to replace user alerts ([2df1567](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/2df1567ccc5223f1aba8f9317bf3b693e6444322))
* **ui:** improve various visual elements ([4097309](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/4097309a5ad0d709e146edce04bd4d52b14b6bfb))
* update allowed format for passwords ([33a633d](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/33a633dbcc774ba0d737c5b17c71b322a91c0f94))
* update common styles ([8695de6](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/8695de652619dc7077580f0eb0c2f7b03de46edb))
* update error survey ([6418f1d](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/6418f1d4e2d4f2044e7cce497e263728037bc80a))
* update evidenceSection ([227fa54](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/227fa5476cefa40217ac1b82032b1840d3595e6b))
* update favicon and title in index.html for improved branding ([74fccf9](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/74fccf9b2b5a6c157761f4571745bd4f722a53a7))
* Update format document ([adea67c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/adea67c41dc6b0157ccdcd1153ac7a5dc3f9402b))
* update main index.html ([91f9386](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/91f9386732e4d841712ef6d856ba393ee40fc575))
* update references ([b2b3791](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/b2b379119fab27a5f25e2b1a563b78958454d12d))
* update timeout ([eab8869](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/eab8869f3f4a46ce609312144f53875a0763a43a))
* Update url for get dimension by process id ([ec64ce5](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/ec64ce582194fc8d9446c6d43b1570292c246404))
* **update-manager:** add useForm hook to handle loading states and update only on real data changes ([a2cbb80](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a2cbb803f64f23b6013b8335844fd4f39f96553a))
* **UploadEvidence:** add new evidence upload component ([fd4db5d](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/fd4db5d35c22c23730a963bb3531c903f169289d))
* **validation-code:** replace single input with 6 separate inputs for each digit ([2e70f77](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/2e70f77f863af88f43d8c65cbdf01555e1a1fc48))
* **view-mode:** Add ListView and GridView modes to AdminAuditorsPage and AdminInstitutionsPage ([e2b99ab](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/e2b99abbc920bbf75f61c63815a2fb2c9d80eeb0))
* **view:** integrates the backend and frontend of the function to create models. ([775073f](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/775073f3ad56bf95ffa5725b2cd724662caa68fc))
* **views:** add new views for user interface ([5be58eb](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/5be58ebdbc2279d2af22dcc026c79238867ac5ca))
* **view:** update components within the view newProcess ([047b501](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/047b501bc91e66fd336f3d12b526369bd2ac733a))
* Vue3 + ts setup ([f6e1083](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/f6e1083d3ae461fb11c390bf1c7c0ff22522bf60))

### Bug Fixes

* **accept-process:** fix process acceptance issue for auditors ([de3a547](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/de3a5471553b1cd39c7da9d035d5ddb95621a5f3))
* **actual-process:** Fixed button click bug on process step changes. ([8c87917](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/8c87917b58cd3603d0530393da65e887b786ebc8))
* **admin-dashboard:** apply visual corrections to admin dashboard ([2e74a24](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/2e74a24cef4960601bba87665bd9caa59d61c5c6))
* **admin-dashboard:** fix bug in institution distribution chart by level not initializing data on component mount ([06c783c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/06c783c5a0f0fb90def5462625dc280ea4b533ce))
* **admin-dashboard:** fix institution colors by level and parse distribution percentages ([5daa57f](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/5daa57f6842a65a4259a68f4f05fff2fb44646f1))
* **admin-dashboard:** implement Top 3 institutions ([22a7691](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/22a7691d569ff21b24f377b39a9acc33ed959fb1))
* **admin-dashboard:** resolve KPI statistics integration error ([4bc99cd](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/4bc99cd0ee6abedd1437627a411ba621d7804807))
* **admin/requests:** adjust visual considerations ([3cb621d](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/3cb621d4ec65b94378ad05bc12f2d529d76ffc30))
* **admin:** correct stats retrieval for admin ([4d997a0](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/4d997a0442614a1bb07074a078bc2537900a3dd4))
* **audit, appeals:** Fix errors in the audit and appeals section ([f889112](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/f88911269460ef30c140e09f2d9c76f137693818))
* **auditor-card:** correct typo 'proceso realizado' to 'proceso activo' ([818718a](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/818718aff010784da9480c6952eccf4c894460b6))
* **auditor-crud:** add page reload on create and update of auditor ([70242f6](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/70242f6e083a00e80705059bcf2dd8c7913c7e6b))
* **auditor-form:** adjust form gap and size to avoid inner-scroll ([b8f3037](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/b8f30376647a2142a1d203614119f0990b057867))
* **auth:** resolve registration request error for institution ([2abb31a](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/2abb31a4101ea070ad7b91199bb6b4398ac42875))
* auto reload page when creating an item ([4d558ab](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/4d558ab310949a075f0ea0ed0481fcc80a6f2614))
* **build:** fix build error caused by Evidence attribute ([484bb1e](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/484bb1eb23bc66a79140ca4f630960e0f852593d))
* **circular-chart:** fix text centering within CircularChart ([b4f3b43](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/b4f3b4377db429d653485889b701d10b2327eef8))
* **consideraciones-visuales:** Applied minor visual considerations ([fbbec71](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/fbbec718425014cd4c61b328ff06b1002a01687f))
* correct filtering condition in unreadProcesses computed property ([024c3f3](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/024c3f3e39b019550683984f8ea652eeb2532c90))
* correct start and end dates for process ([0b451ac](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/0b451ac710f5c230e8ad10fa2e99cdfd9d80cb80))
* **coverage:** add tests for validators and mappers to increase coverage ([7f37be8](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/7f37be8e79a86abb81f9a4cdcce53d583be4ed84))
* **create-institution:** Fix institution creation bug (manager was not assigned when creating the institution) ([e30867e](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/e30867e51900733e89a9e92605dd2d96e0d6aa0c))
* **DataRequest:** Automatically update the end date ([72e50d7](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/72e50d7975dee401e564804ec285244a6e82c857))
* **date:** resolve NaN error when formatting ISO 8601 dates to a readable format ([39ef055](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/39ef055d457527008da9843ee44dcf76190cebf6))
* **dimensions:** change dimension name from 'Accountability' to 'Rendición de Cuentas' ([8d704e7](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/8d704e7a16113526f1c589f09ac97a118d984d54))
* **dimensions:** resolve error when creating dimensions (maturity model) ([48e850b](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/48e850b7ed0aa3fe552a10345881d5bb11668259))
* **dimensions:** update dimensions.json with test data to prevent build errors ([65c24cc](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/65c24cc275f4f92ed699c66b5df197df5e0c46a9))
* dockerfile node version ([1a7441f](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/1a7441f344528c6c15281978242cc926381a3cc7))
* evidence ([9bc37b5](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/9bc37b5013136478567888f176580684e16c8193))
* **evidence-modal:** fix width issue for large screens in evidence modal ([6635db9](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/6635db9f981df8e5e72223ffd6cc3683c51ca90d))
* **evidences:** fixed bug of non-persistence of steps when uploading evidences ([0f1ef19](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/0f1ef198c248aa3b63b84a45c63ad2ad48494023))
* **excel:** validate Excel file upload and extract required file format to .env ([c089c41](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c089c41a438521cab9a9df8cff21da465cf717ff))
* fix build ([844f5b1](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/844f5b18f9191258e1e6b37ec630852a96474ef0))
* fix general issues before production ([5a58523](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/5a58523430e8b87c5de96e0dea35b96c9f30b1f4))
* fix missing property in InstitutionFormModal and institutionMappers ([58ca86b](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/58ca86b2877863a52a3898447593d9c1bb2081c8))
* fixes bugs in tests ([71fdde3](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/71fdde3d82a947c38ca6486e857390dd4ed60df8))
* **gestor-dashboard:** resolve undefined error with current process ([e10ef25](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/e10ef25f1e96274d54b286f05fa1fcc53304a7ce))
* **helpers:** handle null values in formatLabel ([cee4a22](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/cee4a22f84152bf52fb5c88c086885b3a4380ede))
* **ImportEmployeeSection:** fix add and remove logic for employees ([20da49c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/20da49ca50a0d208287ce616ff4a1bb50e317d62))
* **import:** handle exception when importing Excel file of employees ([42006ca](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/42006ca002d5045a48f574b8235ba862658c7cdf))
* **import:** handle exception when importing Excel file of employees ([b180b9f](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/b180b9f4f300ea7b67d552eb6c65d60b61f9703d))
* **input, rutFormatters:** add input exceptions and correct RUT formatting ([40eace3](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/40eace32d7f7526be9c3d2c5ab14161bb7a811c1))
* **institution-modal:** adjust modal for large screens (1920 x 1080) ([6aa2d16](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/6aa2d16060a761ba6acdfcf9264b9cd576dc53ce))
* **institution-request:** Updated route for institution request ([c7e6a9e](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c7e6a9e41d4f8651b509e51979fe450bc98a0454))
* **institution:** add page reload when assigning auditor to an institution ([dfc8b46](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/dfc8b46bf6a8516fa85d04469df443b914c5fb91))
* **institution:** Fix institution's card to handle the null number of employees. ([eb346ac](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/eb346aca75cfe75f0a55f75a2f777e421fffeae5))
* **institution:** resolve institution creation errors ([8c96e54](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/8c96e54fdabce3d52abee3ac4322e068b4ec0543))
* **InstitutionService:** update API route ([14d2792](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/14d27925c4adcc6a3fabf233f49964ee0fc82d98))
* **issues:** resolve SonarQube detected issues ([e9cc931](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/e9cc931e54f567d87334c1d09b496ce47eb797d9))
* jest added ([6dffb26](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/6dffb2635288803ab0d51801ebe94c08222107d3))
* **landing-page:** adjust text on landing page to use 'usted' instead of 'tú' ([e985f0e](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/e985f0ee0ad5afc09dba1df17ffe2b116e847afa))
* **landingPage:** correct background color ([2f0823b](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/2f0823bd5829da2ce1e7aac3b52656da13667653))
* **leveldimension:** correct variables ([cf5a26c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/cf5a26c385e27859706212e6646d5d4d1e965c21))
* **lint:** add rule to ignore Multiword component naming in Vue with ESLint ([e73f378](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/e73f3789a540d619057de84945e8db55c68a369e))
* **lint:** fix linter errors ([4b49bc1](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/4b49bc1ca3a71d25bb305ee5f895793e141954a2))
* **lint:** fix linting errors ([796e1f0](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/796e1f0703aea3bea3ad2fbd1bb04f9def2f93b8))
* **lint:** resolve linting errors ([6da7a50](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/6da7a50ddd35406e7c5560374d0ab69e698f8ba3))
* **loading:** apply fixes to loading states when searching elements and switching to small screens ([1987e52](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/1987e52166f4845f0746f79ac6a1034b617d64d9))
* **logo:** adjust size of login and registration page logos ([2b2df98](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/2b2df98d4c3562e1ea8abe34880a8321e113217d))
* **lTopPanel:** correct variables ([b13d824](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/b13d8248d6d758899fec26077e42b3d03326b573))
* **manager-ui:** Centered alert messages on current and new process pages ([ebdf506](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/ebdf506e48c251bfbd73ec963f90eab25f3371cc))
* **manager/request-process:** add validations for process creation ([7b36340](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/7b36340aa98151140924477e36e5e9bcd3205d08))
* **mappers:** resolve type import error in src/models/mappers.ts ([4b42b67](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/4b42b67b1255389489a28f73382bd4aaa7640914))
* **maturity-model:** handle null values in MaturityModelTile ([30c4d5b](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/30c4d5b3c5cdc4337ddbc04b065c3fa61c18defb))
* **merge:** Merge branch 'feature/dominio-gestor/vista' into feature/dominio-auditor/vista (by revert) ([27ed650](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/27ed650527838a5abb132050bead87f569b3f37e))
* **modal:** correct employee modal display issue ([aa8291d](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/aa8291de72c79f6c2aae5088b63fba23afce3865))
* **ModalEmployee:** remove modalEmployee ([2c4c4e7](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/2c4c4e77d4cfa1433ef6dd2929b9d4dc7d841f90))
* **model:** set model time limit to 92 days (3 months) ([a8f8303](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a8f8303f54bd6cb9d41226f15d43cbb1912b215a))
* **NavBar:** deleted innecesary margin-left ([614c618](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/614c61833c8c8f041ad37320991cecf4b0beebc8))
* **new-process/gestor:** resolve duplication bug on process model selection page ([5c03bd0](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/5c03bd0493f795e9c2793fad19e24f2baede8992))
* **notifications:** modify notification bar animation to prevent background overlay animation on panel open ([8faf057](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/8faf057d71b9b33195f9d95410a5df6e0c302ffe))
* **password-validation:** fix regular expression for password validation ([f4fe73f](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/f4fe73fe1fddb22e165dc3637cef550b9b9eb0e0))
* **pipeline:** remove deprecated components from previous version to resolve build conflicts ([7072db5](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/7072db5d88bb012c4a36224b11887c886a62a8ab))
* **process-request:** fix circular avatar issue and redundancy in wording ([de89792](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/de89792f4765a8fe2e39ff5d8eaa9d248225f9b5))
* **process-request:** Fixed process request error ([0053ac5](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/0053ac503a0314492f800a08ec6d193dafd3119b))
* **process-result:** apply visual fixes to process results ([8f82d4e](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/8f82d4ebaabf8774d7a06eabf13bfe4fc19a813e))
* **process-service:** modify URL for fetching the current process ([bcc2601](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/bcc26014babceba99c9bd1e2ed9f1446a2cb1db8))
* **process:** correct date format in ProcessTile ([dd2f4ec](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/dd2f4ecbf414ee7249abf969718bca59732f3ab7))
* **process:** correct start date of the process ([2970ad3](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/2970ad362782a12029b092d84b692101a7a05d7f))
* **processes:** correct attribute typo and update process handling ([4b69e6f](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/4b69e6f55a9cd1d504b495a5a533d4a53f908b43))
* **processes:** fixed bug in process search (scoped on process institution's name) ([7346930](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/7346930ca532585008b718ea8817fc778f287a5f))
* **processes:** implemented process history ([0a1b435](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/0a1b4351516c800f411b9ab12c040f5adadd753b))
* **ProcessTable:** update import statement for models/types ([dbb041c](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/dbb041c5b10afd4bf6fb8be2402c608884bfa6e8))
* **public-pages:** adjust visual considerations to match Figma UI ([a5b55c6](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a5b55c6753b3f848366b05db5326322c366cb508))
* Removed Bootstrap and its CDN, modifying the components and services that used it (ModalEmployee.vue, modal_service.ts), to ultimately discard its use. ([a910778](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a910778c7d3749ba397785a26fc729b1721904a4))
* **request-process:** resolve visual and loading bugs when requesting a process ([5975aa7](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/5975aa7bf3cdab68d5aefd6aa6aa05cc12c51e85))
* **request-process:** use latest available model ID for manager process request ([dcf3b1a](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/dcf3b1a6feefc739d718037d2a7ae3ef5bc70086))
* **RequestProcess:** remove innecesary comment ([220d7b4](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/220d7b46c6939621c9d838db8dd0b5a7c306529a))
* **responsive:** adjust views for smaller screens (1366 x 768) ([185f853](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/185f8538f6047b0bce978943a8b2f6c89fd4df59))
* **router:** Fixed router imports to avoid the import errors. ([c022db5](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c022db5499939c14e9b980e86bf1a4b6dbf8a6db))
* **routes:** Fixed routes in order to handle the actual user's role (ADMIN, GESTOR). Navbar & Header components updated. ([a86e560](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a86e560bd8fc3c05006036d9351cb3c90bf02e98))
* **sonarqube:** Fixed imports and commented out code lines to avoid the issues from sonarqube metrics. ([a34bf21](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/a34bf212508e5599a0826a8e59ca6f0a9815ad54))
* **survey-creation:** fix survey creation and process assignment issues ([fa249cb](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/fa249cb4c1ed08263d4193822ee3b342a37fcd49))
* **survey:** Applied visual considerations to survey questions page ([00ce3cf](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/00ce3cf004a78f7556b22cc0e4acac2bc3321a07))
* **tabbar:** remove tabbar transition due to a bug ([acdaa8a](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/acdaa8a59a1e2a4d7bb181a8573d423da5920830))
* **table:** fix employee table pagination issue ([ec77066](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/ec77066ff039d0a5967b78d525c03e86b6037021))
* **test:** resolve test errors by incorporating environment variables ([7bb6d03](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/7bb6d03b1c496cdbf574e233b52c48636b0b8000))
* **tests:** adjusted code and tests to pass all test cases ([3e221dd](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/3e221dd4d460219855038c857d875dd252c4e3e0))
* **types:** Fixed type to use primitive types (String to string). ([2b6ee5d](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/2b6ee5d76ae64befb8e40bdd6d3b6e3048675ffd))
* **typo:** improve word choice and capitalization for coherence ([89a4db8](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/89a4db8dddd19cf6f13aec91c9e31bca9b052dee))
* **ui:** apply visual fixes to the user interface ([4314a94](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/4314a94144b6ccaa4af6025709c75a5a3b888bcf))
* **ui:** fix some visual constraint issues ([ab54bb6](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/ab54bb6ebb3bb595a81ab4ad2bd5bcf8ba702b2e))
* **ui:** handle status "Finished" process with new view ([eb88a45](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/eb88a451ec8fb16201a9eecf77944ee8111e56ae))
* update local dates to API data ([6511b92](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/6511b928959e5e86a8d873f6b5eaefe09e99bc6d))
* Update routes for historyView ([6a81844](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/6a818443ba1acfa4e586beceefca25eb77c24561))
* update view insititutionProcessSection in auditor ([c9457e4](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/c9457e421dad5bc728bc9511a3402bafe6e1e8b6))
* **update-auditor:** solve non-reactive update condition error when updating auditor ([3fa9d6d](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/3fa9d6d0b9be5216568d6ebe0f2e3627f6ee7267))
* **update-manager:** Fixed error when updating institution manager ([1715577](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/17155771f3fa222b749353fc68d77f875b69393c))
* **UploadEvidence:** error correction in components to upload evidence ([073e6ff](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/073e6ffaefa724ff420362a6f9365e33a43643e7))
* **useForm:** make update condition reactive by using a ref property ([cd36e0e](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/cd36e0ee490d98d42df53f05b59a502eac9f5acc))
* Visual correction of the employee import component ([2ef08f9](https://github.com/tutransparencia/tutransparencia-ui-vue/commit/2ef08f99191d227596b0ed5a580c355b19d982dd))
