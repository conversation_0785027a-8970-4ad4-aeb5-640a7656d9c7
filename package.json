{"name": "tutransparencia-vue", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "lint:css": "stylelint \"src/**/*.css\"", "test": "vitest", "coverage": "vitest run --coverage"}, "dependencies": {"@fontsource/poppins": "^5.0.14", "@fontsource/roboto": "^5.0.13", "@fortawesome/fontawesome-free": "^6.5.2", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-brands-svg-icons": "^6.5.2", "@fortawesome/free-regular-svg-icons": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@testing-library/jest-dom": "^6.4.8", "@types/xlsx": "^0.0.35", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "apexcharts": "^3.54.1", "axios": "^1.7.4", "dotenv": "^16.4.5", "echarts": "^5.5.1", "jsdom": "^24.1.1", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.2", "jwt-decode": "^4.0.0", "mitt": "^3.0.1", "pinia": "^2.2.0", "primeicons": "^7.0.0", "tutransparencia-vue": "file:", "vue": "^3.4.21", "vue-apexcharts": "^1.6.2", "vue-chartjs": "^5.3.1", "vue-router": "^4.3.2", "vue-toast-notification": "^3.1.2", "xlsx": "^0.18.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.8.0", "@tsconfig/node20": "^20.1.4", "@types/echarts": "^4.9.22", "@types/node": "^20.14.12", "@types/vue-router": "^2.0.0", "@vitejs/plugin-vue": "^5.0.4", "@vitest/coverage-v8": "^2.0.4", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/runtime-core": "^3.4.27", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "npm-run-all2": "^6.1.2", "postcss": "^8.4.38", "prettier": "^3.2.5", "rollup": "^4.24.0", "stylelint": "^16.4.0", "stylelint-config-standard": "^36.0.0", "tailwindcss": "^3.4.4", "tailwindcss-animated": "^1.1.2", "typescript": "~5.4.0", "vite": "^5.2.8", "vite-tsconfig-paths": "^4.3.2", "vitest": "^2.0.4", "vue-tsc": "^2.0.11"}}