name: Release and Docker Build

on:
  push:
    branches:
      - develop
      - main
      - pre-prod

permissions:
  contents: write
  issues: write
  pull-requests: write

jobs:
  release:
    runs-on: ubuntu-latest

    outputs:
      version:    ${{ steps.get_version.outputs.VERSION }}
      is_develop: ${{ steps.check_branch.outputs.is_develop }}
      is_preprod: ${{ steps.check_branch.outputs.is_preprod }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check branch
        id: check_branch
        run: |
          if [[ "$GITHUB_REF" == "refs/heads/develop" ]]; then
            echo "is_develop=true"  >> $GITHUB_OUTPUT
            echo "is_preprod=false" >> $GITHUB_OUTPUT
          elif [[ "$GITHUB_REF" == "refs/heads/pre-prod" ]]; then
            echo "is_develop=false" >> $GITHUB_OUTPUT
            echo "is_preprod=true"  >> $GITHUB_OUTPUT
          else
            echo "is_develop=false" >> $GITHUB_OUTPUT
            echo "is_preprod=false" >> $GITHUB_OUTPUT
          fi

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install semantic-release and plugins
        run: |
          npm install -g semantic-release \
            @semantic-release/git \
            @semantic-release/changelog \
            @semantic-release/github \
            @semantic-release/commit-analyzer \
            @semantic-release/release-notes-generator \
            conventional-changelog-conventionalcommits

      - name: Run semantic-release
        id: semantic
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          npx semantic-release --debug
        continue-on-error: true

      - name: Get latest version
        id: get_version
        run: |
          VERSION=$(git describe --tags $(git rev-list --tags --max-count=1))
          echo "Latest Git tag: $VERSION"
          if [ -n "$VERSION" ]; then
            echo "VERSION=${VERSION#v}" >> $GITHUB_OUTPUT
          else
            echo "VERSION=none"         >> $GITHUB_OUTPUT
          fi

  build_and_push_docker:
    needs: release
    runs-on: ubuntu-latest
    if: needs.release.outputs.version != 'none'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push Docker image
        env:
          VERSION:     ${{ needs.release.outputs.version }}
          IS_DEVELOP:  ${{ needs.release.outputs.is_develop }}
          IS_PREPROD:  ${{ needs.release.outputs.is_preprod }}
          VITE_API_URL:          ${{ vars.VITE_API_URL }}
          VITE_SURVEY_API_URL:   ${{ vars.VITE_SURVEY_API_URL }}
          VITE_USER_API_URL:     ${{ vars.VITE_USER_API_URL }}
          VITE_RESULT_API_URL:   ${{ vars.VITE_RESULT_API_URL }}
          VITE_EMPLOYEE_EXCEL_COLUMNS: ${{ vars.VITE_EMPLOYEE_EXCEL_COLUMNS }}
          VITE_MODEL_EXCEL_COLUMNS:    ${{ vars.VITE_MODEL_EXCEL_COLUMNS }}
        run: |
          if [[ "$IS_DEVELOP" == "true" ]]; then
            LATEST_TAG="latest-dev"
            VITE_API_URL="https://api.dev.etransparencia.cl/v1/api"
            VITE_SURVEY_API_URL="https://survey.dev.etransparencia.cl/v1/api"
            VITE_USER_API_URL="https://user.dev.etransparencia.cl/v1/api"
            VITE_RESULT_API_URL="https://result.dev.etransparencia.cl/v1/api"
          elif [[ "$IS_PREPROD" == "true" ]]; then
            LATEST_TAG="latest-rc"
            VITE_API_URL="https://api.preprod.etransparencia.cl/v1/api"
            VITE_SURVEY_API_URL="https://survey.preprod.etransparencia.cl/v1/api"
            VITE_USER_API_URL="https://user.preprod.etransparencia.cl/v1/api"
            VITE_RESULT_API_URL="https://result.preprod.etransparencia.cl/v1/api"
          else
            LATEST_TAG="latest"
          fi

          echo "Building Docker image version $VERSION with tag $LATEST_TAG"
          docker build \
            --build-arg VITE_API_URL=${VITE_API_URL} \
            --build-arg VITE_SURVEY_API_URL=${VITE_SURVEY_API_URL} \
            --build-arg VITE_USER_API_URL=${VITE_USER_API_URL} \
            --build-arg VITE_RESULT_API_URL=${VITE_RESULT_API_URL} \
            --build-arg VITE_EMPLOYEE_EXCEL_COLUMNS="${VITE_EMPLOYEE_EXCEL_COLUMNS}" \
            --build-arg VITE_MODEL_EXCEL_COLUMNS="${VITE_MODEL_EXCEL_COLUMNS}" \
            -t ${{ secrets.DOCKERHUB_USERNAME }}/etransparencia-ui:$VERSION .
          docker tag ${{ secrets.DOCKERHUB_USERNAME }}/etransparencia-ui:$VERSION ${{ secrets.DOCKERHUB_USERNAME }}/etransparencia-ui:$LATEST_TAG
          docker push ${{ secrets.DOCKERHUB_USERNAME }}/etransparencia-ui:$VERSION
          docker push ${{ secrets.DOCKERHUB_USERNAME }}/etransparencia-ui:$LATEST_TAG
